import jwt from 'jsonwebtoken';
import db from './src/config/database.js';

console.log('🔍 调试认证问题...\n');

// 检查JWT密钥
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production';
console.log('JWT_SECRET:', JWT_SECRET.substring(0, 20) + '...');

// 检查用户数据
const users = db.prepare('SELECT * FROM users').all();
console.log('\n用户列表:');
users.forEach(user => {
  console.log(`  ID: ${user.id}, 用户名: ${user.username}, 角色: ${user.role}`);
});

// 生成一个新的测试Token
const testUser = users[0];
if (testUser) {
  const testToken = jwt.sign(
    { 
      userId: testUser.id, 
      username: testUser.username, 
      role: testUser.role 
    },
    JWT_SECRET,
    { expiresIn: '7d' }
  );
  
  console.log('\n🔑 生成的测试Token:');
  console.log(testToken);
  
  // 验证Token
  try {
    const decoded = jwt.verify(testToken, JWT_SECRET);
    console.log('\n✓ Token验证成功:');
    console.log('  用户ID:', decoded.userId);
    console.log('  用户名:', decoded.username);
    console.log('  角色:', decoded.role);
    console.log('  过期时间:', new Date(decoded.exp * 1000).toLocaleString());
  } catch (error) {
    console.log('\n✗ Token验证失败:', error.message);
  }
}

// 检查中间件
console.log('\n📋 认证中间件检查建议:');
console.log('1. 检查前端localStorage中的token是否存在');
console.log('2. 检查token是否已过期');
console.log('3. 检查后端认证中间件是否正确处理Authorization头');
console.log('4. 检查CORS是否允许Authorization头');

// 模拟前端请求验证
console.log('\n🧪 模拟认证请求测试:');
console.log('请在浏览器控制台中运行以下代码来检查token:');
console.log('localStorage.getItem("token")');
console.log('如果token为null或undefined，请重新登录');

// 检查环境变量
console.log('\n🔧 环境变量检查:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('JWT_EXPIRES_IN:', process.env.JWT_EXPIRES_IN);
console.log('CORS_ORIGIN:', process.env.CORS_ORIGIN);
