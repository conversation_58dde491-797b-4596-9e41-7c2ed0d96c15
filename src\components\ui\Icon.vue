<!-- /src/components/ui/Icon.vue -->
<template>
  <span :class="[$style.icon, className]">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      :width="size"
      :height="size"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      :stroke-width="strokeWidth"
      stroke-linecap="round"
      stroke-linejoin="round"
      :aria-hidden="!title"
      :aria-label="title"
    >
      <title v-if="title">{{ title }}</title>
      <template v-if="name === 'home'">
        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
        <polyline points="9 22 9 12 15 12 15 22"></polyline>
      </template>
      <template v-else-if="name === 'calendar'">
        <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
        <line x1="16" y1="2" x2="16" y2="6"></line>
        <line x1="8" y1="2" x2="8" y2="6"></line>
        <line x1="3" y1="10" x2="21" y2="10"></line>
      </template>
      <template v-else-if="name === 'folder'">
        <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
      </template>
      <template v-else-if="name === 'book'">
        <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"></path>
        <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"></path>
      </template>
      <template v-else-if="name === 'search'">
        <circle cx="11" cy="11" r="8"></circle>
        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
      </template>
      <template v-else-if="name === 'arrow-up'">
        <path d="M18 15l-6-6-6 6" />
      </template>
      <template v-else-if="name === 'menu'">
        <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"></path>
      </template>
      <template v-else-if="name === 'close'">
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </template>
      <template v-else>
        <!-- 默认图标或占位符 -->
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="8" x2="12" y2="16"></line>
        <line x1="8" y1="12" x2="16" y2="12"></line>
      </template>
    </svg>
  </span>
</template>

<script setup>
defineProps({
  name: {
    type: String,
    required: true,
    validator: (value) => [
      'home', 
      'calendar', 
      'folder', 
      'book', 
      'search', 
      'arrow-up',
      'menu',
      'close'
    ].includes(value)
  },
  size: {
    type: [Number, String],
    default: 16
  },
  strokeWidth: {
    type: [Number, String],
    default: 2
  },
  title: {
    type: String,
    default: ''
  },
  className: {
    type: String,
    default: ''
  }
});
</script>

<style module>
.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  position: relative;
  top: -1px;
  margin-right: var(--space-xs);
}
</style>
