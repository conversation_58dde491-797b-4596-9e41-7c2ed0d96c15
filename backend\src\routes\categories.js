import express from 'express';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';
import db from '../config/database.js';

const router = express.Router();

// 获取所有分类
router.get('/', (req, res, next) => {
  try {
    const categories = db.prepare('SELECT * FROM categories ORDER BY name').all();
    
    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    next(error);
  }
});

// 创建分类
router.post('/', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const { name, slug, description } = req.body;

    if (!name || !slug) {
      return res.status(400).json({
        success: false,
        message: '分类名称和 slug 不能为空'
      });
    }

    const insertCategory = db.prepare('INSERT INTO categories (name, slug, description) VALUES (?, ?, ?)');
    const result = insertCategory.run(name, slug, description);

    const newCategory = db.prepare('SELECT * FROM categories WHERE id = ?').get(result.lastInsertRowid);

    res.status(201).json({
      success: true,
      message: '分类创建成功',
      data: newCategory
    });
  } catch (error) {
    next(error);
  }
});

// 更新分类
router.put('/:id', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, slug, description } = req.body;

    if (!name || !slug) {
      return res.status(400).json({
        success: false,
        message: '分类名称和 slug 不能为空'
      });
    }

    const existingCategory = db.prepare('SELECT * FROM categories WHERE id = ?').get(id);
    if (!existingCategory) {
      return res.status(404).json({
        success: false,
        message: '分类不存在'
      });
    }

    // 检查slug是否已被其他分类使用
    if (slug !== existingCategory.slug) {
      const slugExists = db.prepare('SELECT id FROM categories WHERE slug = ? AND id != ?').get(slug, id);
      if (slugExists) {
        return res.status(409).json({
          success: false,
          message: 'Slug 已存在'
        });
      }
    }

    const updateCategory = db.prepare('UPDATE categories SET name = ?, slug = ?, description = ? WHERE id = ?');
    updateCategory.run(name, slug, description, id);

    const updatedCategory = db.prepare('SELECT * FROM categories WHERE id = ?').get(id);

    res.json({
      success: true,
      message: '分类更新成功',
      data: updatedCategory
    });
  } catch (error) {
    next(error);
  }
});

// 删除分类
router.delete('/:id', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const { id } = req.params;

    const category = db.prepare('SELECT * FROM categories WHERE id = ?').get(id);

    if (!category) {
      return res.status(404).json({
        success: false,
        message: '分类不存在'
      });
    }

    // 检查是否有文章使用此分类
    const articlesCount = db.prepare('SELECT COUNT(*) as count FROM article_categories WHERE category_id = ?').get(id).count;

    if (articlesCount > 0) {
      return res.status(409).json({
        success: false,
        message: `无法删除分类，还有 ${articlesCount} 篇文章使用此分类`
      });
    }

    db.prepare('DELETE FROM categories WHERE id = ?').run(id);

    res.json({
      success: true,
      message: '分类删除成功'
    });
  } catch (error) {
    next(error);
  }
});

export default router;
