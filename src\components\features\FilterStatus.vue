<!-- /src/components/features/FilterStatus.vue -->
<template>
  <div :class="$style.filterStatus">
    <template v-if="selectedYear || selectedCategory">
      当前筛选：
      <span v-if="selectedYear">{{ selectedYear }}</span>
      <span v-if="selectedYear && selectedCategory">，</span>
      <span v-if="selectedCategory">{{ selectedCategory }}</span>
      <button @click="clearFilters" :class="$style.clearButton">清除筛选</button>
    </template>
    <template v-else>
      全部文章
    </template>
  </div>
</template>

<script setup>
const props = defineProps({
  selectedYear: {
    type: String,
    default: ''
  },
  selectedCategory: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['clear']);

const clearFilters = () => {
  emit('clear');
};
</script>

<style module>
.filterStatus {
  font-family: var(--font-family-body);
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  margin-bottom: var(--space-l);
  background-color: rgba(255,255,255,0.03);
  padding: var(--space-s) var(--space-m);
  border-radius: var(--border-radius-soft);
  min-height: calc(0.9rem + 2 * var(--space-s) + 2px);
  display: flex;
  align-items: center;
  max-width: 650px;
  border-left: 2px solid rgba(var(--color-accent-rgb), 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(2px);
}

.clearButton {
  background: none;
  border: none;
  color: var(--color-accent);
  margin-left: var(--space-m);
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
  padding: 2px 8px;
  line-height: 1;
  border-radius: 3px;
}

.clearButton:hover {
  background: rgba(var(--color-accent-rgb), 0.1);
}
</style> 