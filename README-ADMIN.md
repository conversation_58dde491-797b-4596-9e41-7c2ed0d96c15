# 墨影心流 - 后端管理系统

## 项目概述

这是一个完整的博客后端管理系统，为墨影心流博客提供内容管理功能。系统包含后端API服务和前端管理界面。

## 技术栈

### 后端
- **Node.js** + **Express** - 服务器框架
- **SQLite** + **better-sqlite3** - 数据库
- **JWT** - 身份认证
- **bcryptjs** - 密码加密
- **multer** + **sharp** - 图片处理
- **Socket.io** - 实时通信

### 前端管理界面
- **Vue 3** + **Composition API** - 前端框架
- **Element Plus** - UI组件库
- **Vue Router 4** - 路由管理
- **Pinia** - 状态管理
- **Axios** - HTTP客户端
- **Vite** - 构建工具

## 功能特性

### ✅ 已完成功能

1. **用户认证系统**
   - 管理员登录/登出
   - JWT token认证
   - 权限验证中间件

2. **文章管理**
   - 文章CRUD操作
   - 发布/草稿状态切换
   - 分类和标签管理
   - Markdown编辑器
   - 实时预览功能

3. **图片管理**
   - 图片上传和删除
   - 自动压缩和格式转换
   - 图片预览和链接复制

4. **管理界面**
   - 响应式设计
   - 仪表盘统计
   - 文章列表和编辑
   - 图片管理器
   - 分类标签管理

5. **安全特性**
   - JWT认证保护
   - SQL注入防护
   - CORS配置
   - 请求频率限制

### 🚧 待完善功能

1. **数据同步机制**
   - 自动同步到前端博客
   - 文件系统同步
   - 缓存更新机制

## 快速开始

### 1. 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 2. 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装管理界面依赖
cd ../admin
npm install
```

### 3. 环境配置

在 `backend` 目录下创建 `.env` 文件：

```env
# 服务器配置
PORT=3001
NODE_ENV=development

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h

# 数据库配置
DB_PATH=./database.sqlite

# 管理员账户
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123456
ADMIN_EMAIL=<EMAIL>

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# CORS配置
CORS_ORIGIN=http://localhost:5174
```

### 4. 启动服务

```bash
# 启动后端服务
cd backend
node src/app.js

# 启动管理界面（新终端）
cd admin
npm run dev
```

### 5. 访问系统

- **后端API**: http://localhost:3001
- **管理界面**: http://localhost:5174
- **默认账户**: admin / admin123456

## API文档

### 认证接口

```
POST /api/auth/login
Body: { username, password }
Response: { success, data: { token, user } }
```

### 文章接口

```
GET    /api/articles           # 获取文章列表
POST   /api/articles           # 创建文章
GET    /api/articles/:id       # 获取单篇文章
PUT    /api/articles/:id       # 更新文章
DELETE /api/articles/:id       # 删除文章
POST   /api/articles/:id/publish    # 发布文章
POST   /api/articles/:id/unpublish  # 取消发布
```

### 分类标签接口

```
GET    /api/categories         # 获取分类列表
POST   /api/categories         # 创建分类
PUT    /api/categories/:id     # 更新分类
DELETE /api/categories/:id     # 删除分类

GET    /api/tags              # 获取标签列表
POST   /api/tags              # 创建标签
PUT    /api/tags/:id          # 更新标签
DELETE /api/tags/:id          # 删除标签
```

### 图片接口

```
GET    /api/images            # 获取图片列表
POST   /api/images/upload     # 上传图片
DELETE /api/images/:id        # 删除图片
```

## 测试

### 运行系统测试

```bash
cd backend
node test-system.js
```

### 运行安全检查

```bash
cd backend
node security-check.js
```

## 部署建议

### 生产环境配置

1. **环境变量**
   - 使用强密码和复杂的JWT密钥
   - 设置正确的CORS源
   - 配置生产数据库

2. **安全加固**
   - 启用HTTPS
   - 配置防火墙
   - 定期更新依赖

3. **性能优化**
   - 启用gzip压缩
   - 配置CDN
   - 数据库索引优化

## 故障排除

### 常见问题

1. **端口占用**
   ```bash
   netstat -ano | findstr :3001
   taskkill /PID <PID> /F
   ```

2. **数据库权限**
   - 确保数据库文件有读写权限
   - 检查目录权限设置

3. **依赖问题**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

## 开发指南

### 项目结构

```
backend/
├── src/
│   ├── app.js              # 应用入口
│   ├── config/             # 配置文件
│   ├── middleware/         # 中间件
│   ├── routes/             # 路由
│   ├── utils/              # 工具函数
│   └── uploads/            # 上传文件
├── database.sqlite         # SQLite数据库
└── .env                    # 环境配置

admin/
├── src/
│   ├── main.js             # 应用入口
│   ├── App.vue             # 根组件
│   ├── components/         # 组件
│   ├── views/              # 页面
│   ├── router/             # 路由
│   ├── stores/             # 状态管理
│   └── api/                # API接口
└── vite.config.js          # Vite配置
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请提交 Issue 或联系开发者。
