# 墨影心流博客部署指南

本文档提供了如何部署墨影心流博客（Vue 3 + Vite单页应用）的详细说明，以确保所有路由都能正常工作。

## 当前路由模式

本项目现在使用Vue Router的哈希路由模式（Hash Mode），URL格式为`http://hmjz.327gzs.top/#/路径`（如`/#/books`、`/#/book/book-1`等）。

哈希路由模式的优势是不需要特殊的服务器配置，因为哈希（#）后面的内容不会被发送到服务器，而是由前端JavaScript处理。这避免了在直接访问非根路径URL时出现404或403错误的问题。

## 历史模式说明（仅供参考）

如果将来需要切换回HTML5 History模式（更美观的URL，没有#符号），则需要注意：单页应用(SPA)的History模式路由是由前端JavaScript处理的，而不是服务器。当直接访问非根路径URL（如 `/books` 或 `/article/slug`）时，服务器会尝试查找对应的物理文件，但这些文件并不存在，因此返回404错误。

要解决这个问题，需要配置服务器将所有请求重定向到 `index.html`，让前端路由处理实际的页面渲染。

## 部署选项

### 1. Nginx (推荐用于生产环境)

1. 将构建后的文件（`npm run build` 生成的 `dist` 目录内容）上传到服务器
2. 使用提供的 `nginx.conf` 配置文件，修改其中的 `server_name` 和 `root` 路径
3. 将配置文件放置在 `/etc/nginx/sites-available/` 目录下
4. 创建符号链接到 `/etc/nginx/sites-enabled/`
5. 重启Nginx: `sudo systemctl restart nginx`

### 2. Apache

1. 将构建后的文件上传到服务器
2. 确保已提供的 `.htaccess` 文件与网站文件一起上传到根目录
3. 确保服务器已启用 `mod_rewrite` 模块: `sudo a2enmod rewrite`
4. 重启Apache: `sudo systemctl restart apache2`

### 3. 静态文件托管服务

#### Netlify

1. 在项目根目录中包含 `netlify.toml` 文件（已提供）
2. 连接您的GitHub仓库到Netlify
3. 设置构建命令为 `npm run build`，发布目录为 `dist`

#### Vercel

1. 在项目根目录中包含 `vercel.json` 文件（已提供）
2. 连接您的GitHub仓库到Vercel
3. Vercel会自动检测Vite项目并使用正确的构建设置

### 4. IIS (Windows服务器)

1. 将构建后的文件上传到IIS网站根目录
2. 确保已提供的 `web.config` 文件与网站文件一起上传到根目录
3. 在IIS管理器中，确保网站已启用URL重写模块
4. 如果未安装URL重写模块，可以从Microsoft官网下载并安装

### 5. 其他静态文件服务器

如果使用其他静态文件服务器，请确保配置将所有404请求重定向到 `index.html`。

## 本地测试部署

在部署到生产环境之前，您可以在本地测试构建版本：

### 方法1：使用Vite预览

```bash
# 构建项目
npm run build

# 使用Vite预览构建结果
npm run preview
```

### 方法2：使用serve工具

```bash
# 安装serve工具
npm install -g serve

# 构建项目
npm run build

# 使用serve启动本地服务器
serve -s dist
```

### 方法3：使用http-server工具

```bash
# 安装http-server工具
npm install -g http-server

# 构建项目
npm run build

# 使用http-server启动本地服务器，并启用单页应用支持
http-server dist -p 8080 --proxy http://localhost:8080?
```

## 常见问题排查

1. **仍然出现404错误**
   - 确认服务器配置正确应用
   - 检查是否有缓存问题，尝试清除浏览器缓存
   - 验证构建文件是否正确上传到服务器
   - 确认服务器配置中的重写规则正确设置
   - 检查服务器日志，查看是否有其他错误信息

2. **静态资源（图片、CSS、JS）无法加载**
   - 检查网络请求，确认资源URL是否正确
   - 确认服务器配置中的静态资源路径设置正确
   - 检查资源文件是否已正确上传到服务器
   - 确认资源文件的MIME类型是否正确配置

3. **部分路由可以访问，部分不行**
   - 检查路由定义是否正确
   - 确认所有嵌套路由都正确配置
   - 检查路由参数是否正确传递
   - 确认路由组件是否正确加载

4. **刷新页面后出现404错误**
   - 这是SPA应用最常见的问题，确保服务器配置正确将所有请求重定向到index.html
   - 检查服务器配置中的重写规则是否正确设置
   - 确认服务器支持URL重写功能

## 注意事项

- 确保在生产环境中启用HTTPS以提高安全性
- 考虑配置适当的缓存策略，提高性能
- 定期备份您的网站数据和配置文件
