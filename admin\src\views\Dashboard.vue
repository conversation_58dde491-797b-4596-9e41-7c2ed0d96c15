<template>
  <div class="dashboard">
    <h1>仪表盘</h1>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon articles">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.articles }}</div>
              <div class="stat-label">文章总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon images">
              <el-icon><Picture /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.images }}</div>
              <div class="stat-label">图片总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon categories">
              <el-icon><Folder /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.categories }}</div>
              <div class="stat-label">分类总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon tags">
              <el-icon><PriceTag /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.tags }}</div>
              <div class="stat-label">标签总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 快速操作 -->
    <el-card class="quick-actions" header="快速操作">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-button type="primary" size="large" @click="$router.push('/articles/new')">
            <el-icon><Plus /></el-icon>
            新建文章
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="success" size="large" @click="$router.push('/images')">
            <el-icon><Upload /></el-icon>
            上传图片
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="info" size="large" @click="$router.push('/categories')">
            <el-icon><FolderAdd /></el-icon>
            管理分类
          </el-button>
        </el-col>
        <el-col :span="6">
          <el-button type="warning" size="large" @click="$router.push('/tags')">
            <el-icon><CollectionTag /></el-icon>
            管理标签
          </el-button>
        </el-col>
      </el-row>

      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="6">
          <el-button type="danger" size="large" @click="syncToFrontend" :loading="syncing">
            <el-icon><Refresh /></el-icon>
            同步到前端
          </el-button>
        </el-col>
        <el-col :span="18">
          <el-alert
            v-if="syncStatus.message"
            :title="syncStatus.message"
            :type="syncStatus.type"
            :closable="false"
            show-icon
          />
        </el-col>
      </el-row>
    </el-card>
    
    <!-- 最近文章 -->
    <el-card header="最近文章">
      <el-table :data="recentArticles" v-loading="loading">
        <el-table-column prop="title" label="标题" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'published' ? 'success' : 'info'">
              {{ row.status === 'published' ? '已发布' : '草稿' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button size="small" @click="$router.push(`/articles/${row.id}/edit`)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import {
  Document,
  Picture,
  Folder,
  PriceTag,
  Plus,
  Upload,
  FolderAdd,
  CollectionTag,
  Refresh
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import api from '@/api'

const loading = ref(false)
const syncing = ref(false)
const stats = ref({
  articles: 0,
  images: 0,
  categories: 0,
  tags: 0
})
const recentArticles = ref([])
const syncStatus = ref({
  message: '',
  type: 'info'
})

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取统计数据
const fetchStats = async () => {
  try {
    // 这里应该调用实际的统计API，暂时使用模拟数据
    stats.value = {
      articles: 7,
      images: 45,
      categories: 5,
      tags: 12
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取最近文章
const fetchRecentArticles = async () => {
  loading.value = true
  try {
    const response = await api.get('/articles?limit=5')
    recentArticles.value = response.data.data.articles
  } catch (error) {
    console.error('获取最近文章失败:', error)
  } finally {
    loading.value = false
  }
}

// 同步到前端
const syncToFrontend = async () => {
  syncing.value = true
  syncStatus.value = { message: '正在同步...', type: 'info' }

  try {
    const response = await api.post('/articles/sync')
    if (response.data.success) {
      syncStatus.value = {
        message: response.data.message,
        type: 'success'
      }
      ElMessage.success(response.data.message)

      // 3秒后清除状态消息
      setTimeout(() => {
        syncStatus.value = { message: '', type: 'info' }
      }, 3000)
    } else {
      throw new Error(response.data.message)
    }
  } catch (error) {
    console.error('同步失败:', error)
    syncStatus.value = {
      message: `同步失败: ${error.response?.data?.message || error.message}`,
      type: 'error'
    }
    ElMessage.error('同步失败')
  } finally {
    syncing.value = false
  }
}

onMounted(() => {
  fetchStats()
  fetchRecentArticles()
})
</script>

<style scoped>
.dashboard h1 {
  margin-bottom: 20px;
  color: #333;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.articles {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.images {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.categories {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.tags {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.quick-actions {
  margin-bottom: 20px;
}

.quick-actions .el-button {
  width: 100%;
  height: 60px;
}
</style>
