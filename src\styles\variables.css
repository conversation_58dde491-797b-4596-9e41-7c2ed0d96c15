/* /src/styles/variables.css */
:root {
  /* 颜色 - 根据『墨影心流』设计规范调整 */
  --color-text-primary: #F5F5DC; /* 浅米色，柔和悦目 */
  --color-text-secondary: #A0A0A0; /* 次要文本色，对比度较低 */
  --color-background: #1A202C; /* 午夜蓝，避免纯黑 */
  --color-background-subtle: #1E1E1E; /* 暖调深炭灰 */
  --color-accent: #B87333; /* 复古古铜色，点缀色 */
  --color-accent-hover: #BDB76B; /* 雅致淡金，悬停状态 */
  --color-highlight: #8B0000; /* 暗调勃艮第红，高亮色 */
  --color-border: #444444; /* 边框色 */
  --color-accent-rgb: 184, 115, 51; /* 复古古铜色RGB */
  --color-highlight-rgb: 139, 0, 0; /* 暗调勃艮第红RGB */

  /* --- 排版变量 (更新) --- */
  /* 字体族 */
  --font-family-heading: 'Playfair Display', 'Source Serif Pro', 'Lora', 'Merriweather', serif; /* 标题字体，衬线 */
  --font-family-body: 'Inter', 'Noto Sans', 'Roboto', 'Open Sans', sans-serif; /* 正文字体，无衬线 */
  --font-family-mono: 'Fira Code', 'Source Code Pro', monospace; /* 代码字体 (可选) */
  /* 基础字号 */
  --font-base-size: 17px; /* 适中字号，提高可读性 */
  /* 标题缩放比例 */
  --font-scale-ratio: 1.3; /* 可以适当调整 Playfair Display 的缩放 */
  /* 行高 */
  --line-height-base: 1.8;        /* 正文行高 1.7-1.9倍字体大小 */
  --line-height-heading: 1.3;     /* 标题行高 */
  /* 字重变量 (新增/调整) */
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;      /* Inter 的 Bold */
  --font-weight-heading-h1: var(--font-weight-light);    /* H1 默认字重，字重适中偏细，营造空灵感 */
  --font-weight-heading-h2: var(--font-weight-semibold); /* H2 默认字重，字重可微调 */
  --font-weight-heading-h3: var(--font-weight-medium);   /* H3 默认字重，字重可微调 */
  --font-weight-heading-h4: var(--font-weight-regular);  /* H4 默认字重 */
  
  /* 字间距精细微调 */
  --letter-spacing-heading: -0.01em; /* 标题字间距 */
  --letter-spacing-body: 0.01em;    /* 正文字间距 */
  --letter-spacing-meta: 0.02em;    /* 元信息字间距 */

  /* 间距 (保持不变) */
  --space-base-unit: 8px;
  --space-xs: calc(var(--space-base-unit) * 0.5);
  --space-s: var(--space-base-unit);
  --space-m: calc(var(--space-base-unit) * 2);
  --space-l: calc(var(--space-base-unit) * 3);
  --space-xl: calc(var(--space-base-unit) * 5);
  --space-xxl: calc(var(--space-base-unit) * 8);

  /* 布局 (保持不变) */
  --content-max-width: 720px;
  --desktop-aside-width-ratio: 0.8;
  --layout-padding-horizontal: var(--space-m);

  /* 边框与圆角 (保持不变) */
  --border-radius-soft: 6px;
  --border-radius-sharp: 0px;
  --border-width: 1px;

  /* 过渡 (保持不变) */
  --transition-duration: 0.3s;
  --transition-timing-function: ease;
  --transition-timing-function-in-out: ease-in-out;

  /* 特定元素样式变量 (保持不变) */
  --blockquote-border-color: var(--color-accent);
  --blockquote-padding: var(--space-l);
}