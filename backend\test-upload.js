import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';

async function testImageUpload() {
  console.log('🔍 测试图片上传功能...\n');

  try {
    // 1. 登录获取token
    console.log('1. 管理员登录');
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123456'
      })
    });

    const loginResult = await loginResponse.json();
    if (!loginResult.success) {
      throw new Error('登录失败: ' + loginResult.message);
    }

    const token = loginResult.data.token;
    console.log('✓ 登录成功');

    // 2. 创建测试图片文件（1x1像素的PNG）
    console.log('\n2. 创建测试图片');
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00,
      0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33,
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    // 保存测试图片到临时文件
    fs.writeFileSync('test-image.png', testImageBuffer);
    console.log('✓ 测试图片创建成功');

    // 3. 测试图片上传
    console.log('\n3. 测试图片上传');
    const formData = new FormData();
    formData.append('image', fs.createReadStream('test-image.png'), {
      filename: 'test-image.png',
      contentType: 'image/png'
    });

    const uploadResponse = await fetch('http://localhost:3001/api/images/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        ...formData.getHeaders()
      },
      body: formData
    });

    const uploadResult = await uploadResponse.json();
    console.log('上传响应:', uploadResult);

    if (uploadResult.success) {
      console.log('✓ 图片上传成功');
      console.log(`✓ 图片URL: ${uploadResult.data.url}`);
      
      // 4. 测试上传的图片是否可以访问
      console.log('\n4. 测试图片访问');
      const imageResponse = await fetch(`http://localhost:3001${uploadResult.data.url}`);
      if (imageResponse.ok) {
        console.log('✓ 上传的图片可以正常访问');
      } else {
        console.log('✗ 上传的图片无法访问:', imageResponse.status);
      }

      // 5. 清理测试图片
      console.log('\n5. 清理测试数据');
      const deleteResponse = await fetch(`http://localhost:3001/api/images/${uploadResult.data.id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (deleteResponse.ok) {
        console.log('✓ 测试图片已清理');
      } else {
        console.log('⚠ 清理测试图片失败');
      }
    } else {
      console.log('✗ 图片上传失败:', uploadResult.message);
    }

    // 清理临时文件
    try {
      fs.unlinkSync('test-image.png');
    } catch (error) {
      // 忽略清理错误
    }

    console.log('\n🎉 图片上传测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    
    // 清理临时文件
    try {
      fs.unlinkSync('test-image.png');
    } catch (error) {
      // 忽略清理错误
    }
  }
}

testImageUpload();
