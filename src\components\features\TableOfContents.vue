<!-- /src/components/features/TableOfContents.vue -->
<template>
  <div :class="[$style.tocWrapper, { [$style.articleToc]: isArticlePage }]" ref="wrapperRef">
    <div
      :class="[$style.tocContainer, { [$style.isSticky]: isSticky }]"
      ref="elementRef"
    >
      <h3 :class="$style.tocTitle">{{ title || '目录' }}</h3>
      <nav :class="$style.tocNav">
        <ul :class="$style.tocList">
          <!-- 首页导航已移除 -->

          <!-- 年份筛选 -->
          <template v-if="showFilters">
            <li :class="$style.tocDivider">
              年份
            </li>
            <!-- 全部年份选项已移除 -->
            <li
              v-for="year in years"
              :key="year"
              :class="[$style.tocItem, { [$style.active]: selectedYear === year }]"
            >
              <a
                href="#"
                :class="$style.tocLink"
                @click.prevent="filterByYear(year)"
              >
                {{ year }}
              </a>
            </li>
          </template>

          <!-- 分类筛选 -->
          <template v-if="showFilters">
            <li :class="$style.tocDivider">
              分类
            </li>
            <!-- 全部分类选项已移除 -->
            <li
              v-for="category in categories"
              :key="category"
              :class="[$style.tocItem, { [$style.active]: selectedCategory === category }]"
            >
              <a
                href="#"
                :class="$style.tocLink"
                @click.prevent="filterByCategory(category)"
              >
                {{ category }}
              </a>
            </li>
          </template>

          <!-- 文章页的动态目录 -->
          <template v-if="isArticlePage && headings.length > 0">
            <li :class="$style.tocDivider">
              文章目录
            </li>
            <li
              v-for="(heading, index) in headings"
              :key="index"
              :class="[$style.tocItem, { [$style.active]: activeHeading === heading.id }]"
              :style="{ paddingLeft: `${(heading.level - 1) * 12}px` }"
            >
              <a
                :href="`#${heading.id}`"
                :class="$style.tocLink"
                @click.prevent="scrollToHeading(heading.id)"
              >
                {{ heading.text }}
              </a>
            </li>
          </template>
        </ul>
      </nav>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { useNavigationStore } from '@/stores/navigation';
import { useScrollTracking } from '@/composables/useScrollTracking';
// Icon 组件已不再使用

// 定义props
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  showFilters: {
    type: Boolean,
    default: true
  },
  enableScrollTracking: {
    type: Boolean,
    default: true
  },
  scrollThreshold: {
    type: Number,
    default: 100
  }
});

// 路由和导航存储
const route = useRoute();
const navigationStore = useNavigationStore();

// 滚动跟踪
const wrapperRef = ref(null);
const { elementRef, isSticky } = useScrollTracking({
  threshold: props.scrollThreshold
});

// 存储提取的标题和当前激活的导航项
const headings = ref([]);
const activeHeading = ref('');
const activeItem = ref('home'); // 默认选中首页

// 计算属性，提高代码可读性
const isArticlePage = computed(() => route.path.includes('/article/'));
const selectedYear = computed(() => navigationStore.selectedYear);
const selectedCategory = computed(() => navigationStore.selectedCategory);

// 年份和分类数据
const years = ref(['2023', '2024', '2025']);
const categories = ref(['诗歌创作', '文学作品', '杂谈随笔']);

// 筛选方法
const filterByYear = (year) => navigationStore.updateYear(year);
const filterByCategory = (category) => navigationStore.updateCategory(category);

// 滚动到指定标题
const scrollToHeading = (id) => {
  const element = document.getElementById(id);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

// 提取文章中的标题
const extractHeadings = () => {
  if (!isArticlePage.value) return;

  const articleContent = document.querySelector('.poem-content');
  if (!articleContent) return;

  const headingElements = articleContent.querySelectorAll('h2, h3, h4');
  const extractedHeadings = [];

  headingElements.forEach((element, index) => {
    if (!element.id) {
      element.id = `heading-${index}`;
    }

    extractedHeadings.push({
      id: element.id,
      text: element.textContent,
      level: parseInt(element.tagName.substring(1)),
      element
    });
  });

  headings.value = extractedHeadings;
};

// 检测当前滚动位置，高亮对应目录项
const updateActiveHeading = () => {
  if (headings.value.length === 0) return;

  const scrollPosition = window.scrollY + 100;

  // 找到当前可见的最上方标题
  for (let i = headings.value.length - 1; i >= 0; i--) {
    const heading = headings.value[i];
    if (heading.element.offsetTop <= scrollPosition) {
      activeHeading.value = heading.id;
      return;
    }
  }

  // 如果没有标题位于滚动位置上方，默认选中第一个标题
  if (headings.value.length > 0) {
    activeHeading.value = headings.value[0].id;
  }
};

// 使用防抖函数优化滚动事件处理
function debounce(fn, delay) {
  let timer = null;
  return function() {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, arguments);
    }, delay);
  };
}

// 优化后的滚动事件处理函数
const debouncedUpdateHeading = debounce(updateActiveHeading, 100);

// 监听路由变化，更新导航状态
watch(() => route.path, (newPath) => {
  // 设置当前选中的导航项
  if (newPath === '/' || newPath === '') {
    activeItem.value = 'home';
  } else {
    activeItem.value = '';
    // 如果是文章页，延迟提取标题
    if (isArticlePage.value) {
      setTimeout(extractHeadings, 300);
    }
  }
});

// 初始化滚动跟踪功能
const initScrollTracking = () => {
  nextTick(() => {
    // 确保 DOM 已经渲染完成
    setTimeout(() => {
      if (elementRef.value) {
        // 手动触发一次滚动事件，确保滚动跟踪功能初始化
        window.dispatchEvent(new Event('scroll'));
      }
    }, 100);
  });
};

// 页面加载和卸载时的处理
onMounted(() => {
  // 初始提取标题
  if (isArticlePage.value) {
    setTimeout(extractHeadings, 300);
  }

  // 添加滚动监听，使用requestAnimationFrame提高性能
  window.addEventListener('scroll', () => {
    requestAnimationFrame(debouncedUpdateHeading);
  }, { passive: true });

  // 初始化滚动跟踪
  initScrollTracking();
});

onUnmounted(() => {
  // 移除滚动监听
  window.removeEventListener('scroll', () => {
    requestAnimationFrame(debouncedUpdateHeading);
  });
});
</script>

<style module>
.tocWrapper {
  position: relative;
  width: 100%;
  height: auto;
}

.tocContainer {
  width: 100%;
  padding-left: var(--space-s);
  border-left: 1px solid rgba(var(--color-accent-rgb), 0.3);
  position: relative;
  padding-bottom: var(--space-s);
  background: rgba(30, 30, 30, 0.3);
  border-radius: var(--border-radius-soft);
  padding: var(--space-s);
  backdrop-filter: blur(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.isSticky {
  will-change: transform;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.tocTitle {
  font-family: var(--font-family-heading);
  font-size: 1.25rem;
  font-weight: var(--font-weight-light);
  color: var(--color-text-primary);
  margin-bottom: var(--space-m);
  letter-spacing: var(--letter-spacing-heading);
  text-shadow: 0 0 1px rgba(var(--color-accent-rgb), 0.3);
  position: relative;
}

.tocTitle::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 30px;
  height: 1px;
  background: var(--color-accent);
  opacity: 0.6;
}

.tocNav {
  position: relative;
}

.tocList {
  list-style: none;
  padding: 0;
  margin: 0;
}

.tocItem {
  margin-bottom: 1px;
  transition: all 0.2s ease;
  position: relative;
  padding-right: var(--space-s);
  padding-left: 8px;
}

.tocItem:last-child {
  margin-bottom: 0;
}

.tocLink {
  display: inline-block;
  font-family: var(--font-family-body);
  font-size: 0.85rem;
  font-weight: var(--font-weight-light);
  color: var(--color-text-secondary);
  text-decoration: none;
  padding: 3px 0;
  transition: all 0.3s ease;
  letter-spacing: var(--letter-spacing-body);
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.tocDivider {
  margin: 10px 0 6px 0;
  padding-bottom: 2px;
  font-family: var(--font-family-heading);
  font-size: 0.9rem;
  color: var(--color-accent);
  border-bottom: 1px solid rgba(var(--color-accent-rgb), 0.2);
  font-weight: var(--font-weight-medium);
}

/* 激活状态样式 */
.tocItem.active .tocLink {
  color: var(--color-accent);
  transform: translateX(2px);
  font-weight: var(--font-weight-medium);
}

/* 悬停效果 */
.tocLink:hover {
  color: var(--color-accent-hover);
  transform: translateX(2px);
  background-color: rgba(var(--color-accent-rgb), 0.05);
  border-radius: 3px;
}

/* 文章页目录样式 */
.articleToc {
  max-height: 80vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--color-accent) transparent;
}

.articleToc::-webkit-scrollbar {
  width: 3px;
}

.articleToc::-webkit-scrollbar-thumb {
  background-color: var(--color-accent);
  border-radius: 4px;
}

.articleToc::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .tocWrapper {
    display: none; /* 在移动端完全隐藏目录 */
  }
}

.icon {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
  position: relative;
  top: -1px;
}

/* 增加首页链接区域与其他目录项的间距 */
li:first-child.tocItem {
  margin-bottom: 4px;
}
</style>
