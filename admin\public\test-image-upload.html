<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片上传测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="text"], input[type="password"], input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .uploaded-image {
            max-width: 300px;
            margin-top: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 图片上传功能测试</h1>
        
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="admin">
        </div>
        
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="admin123456">
        </div>
        
        <div class="form-group">
            <button onclick="login()">登录</button>
            <button onclick="clearResults()">清除结果</button>
        </div>
        
        <div class="form-group">
            <label for="imageFile">选择图片:</label>
            <input type="file" id="imageFile" accept="image/*">
        </div>
        
        <div class="form-group">
            <button onclick="uploadImage()" id="uploadBtn" disabled>上传图片</button>
            <button onclick="loadImages()" id="loadBtn" disabled>加载图片列表</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        let authToken = null;
        const API_BASE = '/api';
        
        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }
        
        function clearResults() {
            document.getElementById('result').innerHTML = '';
        }
        
        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                showResult('正在登录...', 'info');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    authToken = result.data.token;
                    localStorage.setItem('admin_token', authToken);
                    document.getElementById('uploadBtn').disabled = false;
                    document.getElementById('loadBtn').disabled = false;
                    showResult(`登录成功！\nToken: ${authToken.substring(0, 50)}...`, 'success');
                } else {
                    showResult(`登录失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult(`登录错误: ${error.message}`, 'error');
            }
        }
        
        async function uploadImage() {
            const fileInput = document.getElementById('imageFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('请选择一个图片文件', 'error');
                return;
            }
            
            if (!authToken) {
                showResult('请先登录', 'error');
                return;
            }
            
            try {
                showResult('正在上传图片...', 'info');
                
                const formData = new FormData();
                formData.append('image', file);
                
                const response = await fetch(`${API_BASE}/images/upload`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const imageData = result.data;
                    let message = `图片上传成功！\n`;
                    message += `文件名: ${imageData.filename}\n`;
                    message += `原始名称: ${imageData.original_name}\n`;
                    message += `大小: ${imageData.size} bytes\n`;
                    message += `尺寸: ${imageData.width} x ${imageData.height}\n`;
                    message += `URL: ${imageData.url}\n`;
                    message += `ID: ${imageData.id}`;
                    
                    showResult(message, 'success');
                    
                    // 显示上传的图片
                    const img = document.createElement('img');
                    img.src = imageData.url;
                    img.className = 'uploaded-image';
                    img.alt = imageData.filename;
                    document.getElementById('result').appendChild(img);
                    
                } else {
                    showResult(`图片上传失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult(`上传错误: ${error.message}`, 'error');
            }
        }
        
        async function loadImages() {
            if (!authToken) {
                showResult('请先登录', 'error');
                return;
            }
            
            try {
                showResult('正在加载图片列表...', 'info');
                
                const response = await fetch(`${API_BASE}/images`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const images = result.data.images;
                    let message = `图片列表加载成功！\n`;
                    message += `总数: ${images.length}\n\n`;
                    
                    images.slice(0, 5).forEach((img, index) => {
                        message += `${index + 1}. ${img.filename}\n`;
                        message += `   原始名称: ${img.original_name}\n`;
                        message += `   大小: ${img.size} bytes\n`;
                        message += `   上传时间: ${img.created_at}\n\n`;
                    });
                    
                    if (images.length > 5) {
                        message += `... 还有 ${images.length - 5} 张图片`;
                    }
                    
                    showResult(message, 'success');
                } else {
                    showResult(`加载图片列表失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult(`加载错误: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时检查是否有保存的token
        window.onload = function() {
            const savedToken = localStorage.getItem('admin_token');
            if (savedToken) {
                authToken = savedToken;
                document.getElementById('uploadBtn').disabled = false;
                document.getElementById('loadBtn').disabled = false;
                showResult('检测到已保存的登录状态', 'info');
            }
        };
    </script>
</body>
</html>
