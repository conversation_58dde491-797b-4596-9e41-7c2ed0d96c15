<!-- /src/components/common/ImageViewer.vue -->
<template>
  <div>
    <!-- 图片查看器遮罩层，仅在激活时显示 -->
    <Teleport to="body">
      <Transition name="fade">
        <div 
          v-if="isOpen" 
          :class="$style.overlay"
          @click="closeViewer"
        >
          <div :class="$style.imageContainer" @click.stop>
            <img :src="currentImage" :alt="alt" :class="$style.image">
            
            <!-- 关闭按钮 -->
            <button :class="$style.closeButton" @click="closeViewer" aria-label="关闭图片查看器">
              ×
            </button>
            
            <!-- 导航按钮（如果有多张图片） -->
            <div v-if="hasMultipleImages" :class="$style.navigation">
              <button 
                :class="[$style.navButton, $style.prevButton]" 
                @click.stop="prevImage" 
                aria-label="上一张图片"
              >
                &#10094;
              </button>
              <button 
                :class="[$style.navButton, $style.nextButton]" 
                @click.stop="nextImage" 
                aria-label="下一张图片"
              >
                &#10095;
              </button>
            </div>
            
            <!-- 图片计数器 -->
            <div v-if="hasMultipleImages" :class="$style.counter">
              {{ currentIndex + 1 }} / {{ images.length }}
            </div>
          </div>
        </div>
      </Transition>
    </Teleport>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// 定义组件属性
const props = defineProps({
  // 单张图片模式时的图片URL
  src: {
    type: String,
    default: ''
  },
  // 图片集合模式
  images: {
    type: Array,
    default: () => []
  },
  // 备用文本
  alt: {
    type: String,
    default: '图片'
  }
});

// 图片查看器状态
const isOpen = ref(false);
const currentIndex = ref(0);
const currentSrc = ref('');
const localImages = ref([]);

// 计算当前应显示的图片
const currentImage = computed(() => {
  if (localImages.value.length > 0) {
    return localImages.value[currentIndex.value];
  }
  if (props.images && props.images.length > 0) {
    return props.images[currentIndex.value];
  }
  return currentSrc.value || props.src;
});

// 判断是否有多张图片
const hasMultipleImages = computed(() => {
  return (localImages.value.length > 1) || (props.images && props.images.length > 1);
});

// 设置图片集合
const setImages = (images, startIndex = 0) => {
  localImages.value = images || [];
  currentIndex.value = startIndex;
};

// 打开图片查看器
const openViewer = (index = 0, src = null) => {
  if (src) {
    currentSrc.value = src; // 使用本地响应式变量，而不是直接修改props
  }
  currentIndex.value = index;
  isOpen.value = true;
  // 禁止背景滚动
  document.body.style.overflow = 'hidden';
};

// 关闭图片查看器
const closeViewer = () => {
  isOpen.value = false;
  // 恢复背景滚动
  document.body.style.overflow = '';
};

// 显示下一张图片
const nextImage = () => {
  if (!hasMultipleImages.value) return;
  
  const imageArray = localImages.value.length > 0 ? localImages.value : props.images;
  currentIndex.value = (currentIndex.value + 1) % imageArray.length;
};

// 显示上一张图片
const prevImage = () => {
  if (!hasMultipleImages.value) return;
  
  const imageArray = localImages.value.length > 0 ? localImages.value : props.images;
  currentIndex.value = (currentIndex.value - 1 + imageArray.length) % imageArray.length;
};

// 暴露方法给父组件
defineExpose({
  open: openViewer,
  close: closeViewer,
  setImages
});
</script>

<style module>
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  cursor: zoom-out;
}

.imageContainer {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  cursor: default;
}

.image {
  max-width: 100%;
  max-height: 90vh;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.closeButton {
  position: absolute;
  top: -40px;
  right: 0;
  width: 36px;
  height: 36px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border: none;
  border-radius: 50%;
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s;
}

.closeButton:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.navigation {
  position: absolute;
  width: 100%;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  pointer-events: none;
}

.navButton {
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border: none;
  width: 40px;
  height: 40px;
  font-size: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.2s;
  pointer-events: auto;
}

.navButton:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.prevButton {
  margin-left: -20px;
}

.nextButton {
  margin-right: -20px;
}

.counter {
  position: absolute;
  bottom: -30px;
  left: 0;
  right: 0;
  text-align: center;
  color: #fff;
  font-size: 14px;
}

/* 渐变动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style> 