import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import db from '../config/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 导入现有文章数据
export const importExistingArticles = async () => {
  try {
    console.log('开始导入现有文章数据...');

    // 读取前端的文章数据
    const frontendDataPath = path.join(__dirname, '../../../src/data/articles.js');
    const articlesContent = await fs.readFile(frontendDataPath, 'utf-8');

    // 简单的方式提取文章数据（实际项目中可能需要更复杂的解析）
    const articlesMatch = articlesContent.match(/export const articles = (\[[\s\S]*?\]);/);
    if (!articlesMatch) {
      console.log('未找到文章数据');
      return;
    }

    // 使用 eval 解析数据（注意：生产环境中应该使用更安全的方法）
    const articlesData = eval(articlesMatch[1]);

    // 检查是否已有数据
    const existingCount = db.prepare('SELECT COUNT(*) as count FROM articles').get().count;
    if (existingCount > 0) {
      console.log(`数据库中已有 ${existingCount} 篇文章，跳过导入`);
      return;
    }

    // 先读取所有文章内容
    const articlesWithContent = [];
    for (const article of articlesData) {
      const mdPath = path.join(__dirname, '../../../public/articles', `${article.slug}.md`);
      let content = '';

      try {
        content = await fs.readFile(mdPath, 'utf-8');
      } catch (error) {
        console.log(`警告: 无法读取文章文件 ${article.slug}.md`);
        content = `# ${article.title}\n\n${article.description || ''}`;
      }

      articlesWithContent.push({
        ...article,
        content
      });
    }

    // 准备SQL语句
    const insertArticle = db.prepare(`
      INSERT INTO articles (slug, title, description, content, cover_image, status, featured, created_at, published_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const insertCategory = db.prepare('INSERT OR IGNORE INTO categories (name, slug) VALUES (?, ?)');
    const insertTag = db.prepare('INSERT OR IGNORE INTO tags (name, slug) VALUES (?, ?)');
    const insertArticleCategory = db.prepare('INSERT OR IGNORE INTO article_categories (article_id, category_id) VALUES (?, ?)');
    const insertArticleTag = db.prepare('INSERT OR IGNORE INTO article_tags (article_id, tag_id) VALUES (?, ?)');

    // 开始事务
    const transaction = db.transaction((articles) => {
      let count = 0;

      for (const article of articles) {
        try {
          // 插入文章
          const result = insertArticle.run(
            article.slug,
            article.title,
            article.description || '',
            article.content,
            article.coverImage || '',
            'published', // 现有文章默认为已发布
            article.featured ? 1 : 0,
            article.date ? new Date(article.date).toISOString() : new Date().toISOString(),
            article.date ? new Date(article.date).toISOString() : new Date().toISOString()
          );

          const articleId = result.lastInsertRowid;

          // 处理分类
          if (article.categories && Array.isArray(article.categories)) {
            for (const categoryName of article.categories) {
              const categorySlug = categoryName.toLowerCase().replace(/\s+/g, '-');
              insertCategory.run(categoryName, categorySlug);

              const category = db.prepare('SELECT id FROM categories WHERE slug = ?').get(categorySlug);
              if (category) {
                insertArticleCategory.run(articleId, category.id);
              }
            }
          }

          // 处理标签
          if (article.tags && Array.isArray(article.tags)) {
            for (const tagName of article.tags) {
              const tagSlug = tagName.toLowerCase().replace(/\s+/g, '-');
              insertTag.run(tagName, tagSlug);

              const tag = db.prepare('SELECT id FROM tags WHERE slug = ?').get(tagSlug);
              if (tag) {
                insertArticleTag.run(articleId, tag.id);
              }
            }
          }

          count++;
          console.log(`✅ 导入文章: ${article.title}`);
        } catch (error) {
          console.error(`❌ 导入文章失败 ${article.slug}:`, error.message);
        }
      }

      return count;
    });

    const importedCount = transaction(articlesWithContent);
    console.log(`🎉 成功导入 ${importedCount} 篇文章`);

  } catch (error) {
    console.error('❌ 导入文章数据失败:', error.message);
  }
};

// 如果直接运行此文件，则执行导入
if (import.meta.url === `file://${process.argv[1]}`) {
  await importExistingArticles();
  process.exit(0);
}
