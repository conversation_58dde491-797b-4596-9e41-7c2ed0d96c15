<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>实时Token检查工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .token-display {
            word-break: break-all;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
        .log {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 实时Token检查工具</h1>
        
        <div class="section info">
            <h3>📋 当前localStorage状态</h3>
            <div id="storage-status">正在检查...</div>
        </div>

        <div class="section">
            <h3>🔑 Token管理操作</h3>
            <button onclick="checkAllTokens()">检查所有Token</button>
            <button onclick="loginAndSetToken()">重新登录获取Token</button>
            <button onclick="clearAllTokens()">清除所有Token</button>
            <button onclick="testImageUploadNow()">立即测试图片上传</button>
        </div>

        <div class="section">
            <h3>🧪 实时上传测试</h3>
            <input type="file" id="fileInput" accept="image/*" style="margin-bottom: 10px;">
            <br>
            <button onclick="uploadSelectedFile()">上传选中的图片</button>
            <div id="upload-result" style="margin-top: 10px;"></div>
        </div>

        <div class="section">
            <h3>📝 操作日志</h3>
            <div id="log" class="log"></div>
            <button onclick="clearLog()">清除日志</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function updateStorageStatus() {
            const statusDiv = document.getElementById('storage-status');
            const token = localStorage.getItem('token');
            const adminToken = localStorage.getItem('admin_token');
            
            let html = '<strong>localStorage内容：</strong><br>';
            html += `token: ${token ? '存在' : '不存在'}<br>`;
            html += `admin_token: ${adminToken ? '存在' : '不存在'}<br><br>`;
            
            if (adminToken) {
                try {
                    const payload = JSON.parse(atob(adminToken.split('.')[1]));
                    const exp = new Date(payload.exp * 1000);
                    const now = new Date();
                    
                    html += `<strong>admin_token详情：</strong><br>`;
                    html += `用户ID: ${payload.userId}<br>`;
                    html += `过期时间: ${exp.toLocaleString()}<br>`;
                    html += `状态: ${exp < now ? '❌ 已过期' : '✅ 有效'}<br>`;
                    
                    statusDiv.className = exp < now ? 'error' : 'success';
                } catch (error) {
                    html += '❌ admin_token格式无效<br>';
                    statusDiv.className = 'error';
                }
            } else {
                html += '❌ 没有admin_token，需要重新登录<br>';
                statusDiv.className = 'error';
            }
            
            statusDiv.innerHTML = html;
        }

        function checkAllTokens() {
            log('检查所有Token...');
            
            const token = localStorage.getItem('token');
            const adminToken = localStorage.getItem('admin_token');
            
            log(`token: ${token ? '存在' : '不存在'}`);
            log(`admin_token: ${adminToken ? '存在' : '不存在'}`);
            
            if (adminToken) {
                try {
                    const payload = JSON.parse(atob(adminToken.split('.')[1]));
                    const exp = new Date(payload.exp * 1000);
                    const now = new Date();
                    
                    log(`admin_token状态: ${exp < now ? '已过期' : '有效'}，过期时间: ${exp.toLocaleString()}`, exp < now ? 'error' : 'success');
                } catch (error) {
                    log(`admin_token解析失败: ${error.message}`, 'error');
                }
            }
            
            updateStorageStatus();
        }

        async function loginAndSetToken() {
            try {
                log('开始重新登录...');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123456'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    // 设置admin_token
                    localStorage.setItem('admin_token', result.data.token);
                    // 清除旧的token（如果存在）
                    localStorage.removeItem('token');
                    
                    log('重新登录成功，admin_token已设置', 'success');
                    updateStorageStatus();
                } else {
                    log(`登录失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`登录请求失败: ${error.message}`, 'error');
            }
        }

        function clearAllTokens() {
            localStorage.removeItem('token');
            localStorage.removeItem('admin_token');
            log('所有Token已清除', 'warning');
            updateStorageStatus();
        }

        async function testImageUploadNow() {
            const adminToken = localStorage.getItem('admin_token');
            
            if (!adminToken) {
                log('测试失败：没有admin_token，请先登录', 'error');
                return;
            }

            try {
                log('开始测试图片上传...');
                
                // 创建一个1x1像素的测试图片
                const canvas = document.createElement('canvas');
                canvas.width = 1;
                canvas.height = 1;
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = '#ff0000';
                ctx.fillRect(0, 0, 1, 1);
                
                canvas.toBlob(async (blob) => {
                    const formData = new FormData();
                    formData.append('image', blob, 'test.png');

                    log(`发送请求，使用admin_token: ${adminToken.substring(0, 20)}...`);

                    const response = await fetch(`${API_BASE}/images/upload`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${adminToken}`
                        },
                        body: formData
                    });

                    log(`响应状态码: ${response.status}`);
                    const result = await response.json();
                    log(`响应消息: ${result.message}`);
                    
                    if (result.success) {
                        log(`图片上传成功: ${result.data.filename}`, 'success');
                        
                        // 清理测试图片
                        try {
                            await fetch(`${API_BASE}/images/${result.data.id}`, {
                                method: 'DELETE',
                                headers: { 'Authorization': `Bearer ${adminToken}` }
                            });
                            log('测试图片已清理', 'info');
                        } catch (error) {
                            log('清理测试图片失败', 'warning');
                        }
                    } else {
                        log(`图片上传失败: ${result.message}`, 'error');
                    }
                }, 'image/png');
                
            } catch (error) {
                log(`图片上传测试失败: ${error.message}`, 'error');
            }
        }

        async function uploadSelectedFile() {
            const fileInput = document.getElementById('fileInput');
            const resultDiv = document.getElementById('upload-result');
            const adminToken = localStorage.getItem('admin_token');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = '<span style="color: red;">请先选择一个图片文件</span>';
                return;
            }
            
            if (!adminToken) {
                resultDiv.innerHTML = '<span style="color: red;">没有admin_token，请先登录</span>';
                return;
            }

            try {
                resultDiv.innerHTML = '<span style="color: blue;">正在上传...</span>';
                
                const formData = new FormData();
                formData.append('image', fileInput.files[0]);

                log(`上传真实文件: ${fileInput.files[0].name}`);

                const response = await fetch(`${API_BASE}/images/upload`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    },
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `<span style="color: green;">✅ 上传成功: ${result.data.filename}</span>`;
                    log(`真实文件上传成功: ${result.data.filename}`, 'success');
                } else {
                    resultDiv.innerHTML = `<span style="color: red;">❌ 上传失败: ${result.message}</span>`;
                    log(`真实文件上传失败: ${result.message}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">❌ 上传异常: ${error.message}</span>`;
                log(`真实文件上传异常: ${error.message}`, 'error');
            }
        }

        // 页面加载时检查状态
        window.onload = function() {
            updateStorageStatus();
            log('实时Token检查工具已加载', 'info');
            
            // 每5秒自动检查一次状态
            setInterval(updateStorageStatus, 5000);
        };
    </script>
</body>
</html>
