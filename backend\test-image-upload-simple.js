import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';

const BASE_URL = 'http://localhost:3001/api';

async function testImageUpload() {
  try {
    console.log('🔧 测试图片上传功能...\n');
    
    // 1. 登录获取Token
    console.log('=== 步骤1：登录获取Token ===');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123456'
      })
    });

    const loginResult = await loginResponse.json();
    
    if (!loginResult.success) {
      console.log('✗ 登录失败:', loginResult.message);
      return false;
    }

    const token = loginResult.data.token;
    console.log('✓ 登录成功');

    // 2. 检查图片目录
    console.log('\n=== 步骤2：检查图片目录 ===');
    const uploadDir = path.join(process.cwd(), '..', 'public', 'articles', 'img');
    console.log('图片上传目录:', uploadDir);
    
    if (!fs.existsSync(uploadDir)) {
      console.log('✗ 图片目录不存在，正在创建...');
      fs.mkdirSync(uploadDir, { recursive: true });
      console.log('✓ 图片目录创建成功');
    } else {
      console.log('✓ 图片目录存在');
    }

    // 3. 准备测试图片
    console.log('\n=== 步骤3：准备测试图片 ===');
    const testImagePath = path.join(process.cwd(), 'test-upload.jpg');
    const existingImagePath = path.join(uploadDir, '1.jpg');
    
    if (fs.existsSync(existingImagePath)) {
      fs.copyFileSync(existingImagePath, testImagePath);
      console.log('✓ 测试图片准备完成');
    } else {
      console.log('✗ 没有找到测试图片文件');
      return false;
    }

    // 4. 测试图片上传
    console.log('\n=== 步骤4：测试图片上传 ===');
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));

    console.log('发送上传请求...');
    const uploadResponse = await fetch(`${BASE_URL}/images/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        ...formData.getHeaders()
      },
      body: formData
    });

    console.log(`响应状态码: ${uploadResponse.status}`);
    
    const uploadResult = await uploadResponse.json();
    console.log('上传结果:', uploadResult);
    
    // 清理测试文件
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
    }

    if (uploadResult.success) {
      console.log('✓ 图片上传成功');
      console.log(`  文件名: ${uploadResult.data.filename}`);
      console.log(`  URL: ${uploadResult.data.url}`);
      
      // 检查文件是否真的保存到了正确位置
      const savedFilePath = path.join(uploadDir, uploadResult.data.filename);
      if (fs.existsSync(savedFilePath)) {
        console.log('✓ 图片文件已保存到项目目录');
        console.log(`  保存路径: ${savedFilePath}`);
        
        // 清理测试图片
        fs.unlinkSync(savedFilePath);
        console.log('✓ 测试图片已清理');
      } else {
        console.log('✗ 图片文件未保存到项目目录');
        return false;
      }
      
      return true;
    } else {
      console.log('✗ 图片上传失败:', uploadResult.message);
      return false;
    }
    
  } catch (error) {
    console.log('✗ 测试过程中发生错误:', error.message);
    return false;
  }
}

testImageUpload().then(success => {
  console.log('\n🏆 测试结果:', success ? '✅ 成功' : '❌ 失败');
}).catch(console.error);
