/* /src/styles/base/typography.css */

/* 基础正文样式 */
body {
  font-family: var(--font-family-body); /* 使用 Inter 作为正文字体 */
  line-height: var(--line-height-base); /* 1.7-1.9倍字体大小，提升流畅感 */
  font-weight: var(--font-weight-regular); /* 正文默认字重 */
  letter-spacing: 0.01em; /* 微调字间距，提高可读性 */
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading); /* 使用 Playfair Display 作为标题字体 */
  line-height: var(--line-height-heading);
  letter-spacing: -0.01em; /* Playfair Display 字间距微调 */
  margin-bottom: var(--space-m); /* 标题下方统一间距 */
  color: var(--color-text-primary); /* 柔和亮色，如暖米白 */
}

/* 各级标题字号和字重 */
/* 标准 HTML 注释：可以根据视觉效果微调缩放比例和字重变量 */
h1 {
  font-size: calc(1rem * var(--font-scale-ratio) * var(--font-scale-ratio) * var(--font-scale-ratio) * var(--font-scale-ratio)); /* ~2.87rem if ratio 1.3 */
  font-weight: var(--font-weight-heading-h1); /* Light (300)，营造空灵感 */
  letter-spacing: -0.015em; /* H1 可以稍微收紧一点 */
  margin-top: var(--space-xl); /* 顶部间距增加 */
  margin-bottom: var(--space-l); /* 底部间距增加 */
}
h2 {
  font-size: calc(1rem * var(--font-scale-ratio) * var(--font-scale-ratio) * var(--font-scale-ratio)); /* ~2.2rem */
  font-weight: var(--font-weight-heading-h2); /* SemiBold (600) */
  margin-top: var(--space-xl); /* 增加与上方内容的间距 */
  margin-bottom: var(--space-m); /* 与下方内容的间距 */
  border-bottom: 1px solid var(--color-border); /* 底部边框增强层次感 */
  padding-bottom: var(--space-s); /* 底部内边距 */
}
h3 {
  font-size: calc(1rem * var(--font-scale-ratio) * var(--font-scale-ratio)); /* ~1.69rem */
  font-weight: var(--font-weight-heading-h3); /* Medium (500) */
  margin-top: var(--space-l); /* 与上方内容的间距 */
}
h4 {
  font-size: calc(1rem * var(--font-scale-ratio)); /* ~1.3rem */
  font-weight: var(--font-weight-heading-h4); /* Regular (400) */
  margin-top: var(--space-m); /* 与上方内容的间距 */
}
h5 {
  font-size: 1rem;
  font-weight: var(--font-weight-semibold); /* H5 可以用 SemiBold 强调 */
  font-family: var(--font-family-body); /* H5/H6 可能用 Sans 更好 */
}
h6 {
  font-size: calc(1rem / var(--font-scale-ratio)); /* ~0.77rem */
  font-weight: var(--font-weight-semibold);
  font-family: var(--font-family-body);
  color: var(--color-text-secondary);
  text-transform: uppercase; /* H6 常用于小标题，大写效果不错 */
  letter-spacing: 0.05em;
}

/* 段落样式 */
p {
  margin-bottom: var(--space-l); /* 增加段落间距，明显大于行高 */
  max-width: 65ch; /* 保持易读性 */
  line-height: 1.8; /* 1.7-1.9倍字体大小，提升流畅感 */
}

/* 链接基础样式 (大部分由 LinkWrapper 处理) */
a {
  color: var(--color-accent);
  text-decoration: none;
}
a:hover {
  color: var(--color-accent-hover);
}

/* 强调样式 */
strong, b {
  font-weight: var(--font-weight-bold); /* 使用 Inter 的 Bold */
  font-family: var(--font-family-body); /* 强调通常用无衬线体 */
}
em, i {
  font-style: italic;
  /* 标准 HTML 注释：如果想让 em 使用 Playfair Italic, 可以取消下面注释 */
  /* font-family: var(--font-family-heading); */
}

/* 小字/元信息 */
small {
  font-size: 0.85em;
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-light); /* 更细的字重 */
}
.metadata { /* 元信息统一样式 */
    font-size: 0.9rem;
    font-family: var(--font-family-body); /* 使用正文字体 */
    color: var(--color-text-secondary); /* 对比度更低的深灰 */
    font-weight: var(--font-weight-light); /* 更细的字重 */
    letter-spacing: 0.02em; /* 字间距微调 */
    margin-bottom: var(--space-l); /* 与下方内容保持距离 */
    display: block; /* 确保独占一行 */
    line-height: 1.6; /* 行高稍微紧凑 */
}

/* 代码样式 */
code {
  font-family: var(--font-family-mono); /* 使用等宽字体 */
  background-color: var(--color-background-subtle);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.9em;
  word-break: break-word;
}
pre code { /* 代码块内部 */
  display: block;
  overflow-x: auto;
  font-size: 0.85em;
  background-color: transparent; /* pre 元素负责背景色 */
  padding: 0;
  white-space: pre-wrap; /* 允许代码换行 */
  word-wrap: break-word; /* 允许长单词换行 */
}
pre { /* 代码块容器 */
   background-color: var(--color-background-subtle);
   border-radius: var(--border-radius-soft);
   padding: var(--space-m);
   margin-bottom: var(--space-l);
   overflow-x: auto;
   max-width: 100%;
}

/* 语言特定代码块样式 */
pre.language-markdown,
pre.language-text,
pre.language-json,
pre.language-javascript,
pre.language-html,
pre.language-css {
  position: relative;
  border-left: 3px solid var(--color-accent);
}

/* 响应式表格 */
.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  margin-bottom: var(--space-l);
}

/* 移动设备适配 */
@media (max-width: 768px) {
  pre {
    padding: var(--space-s);
    font-size: 0.8em;
  }

  pre code {
    font-size: 0.8em;
  }
}