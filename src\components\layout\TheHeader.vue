<!-- /src/components/layout/TheHeader.vue -->
<template>
  <header :class="$style.header">
    <!-- Logo 区域 -->

    <!-- 搜索框 -->
    <SearchBox :class="$style.searchBox" />

    <!-- 导航触发器 -->
    <!-- 标准 HTML 注释：桌面端触发器 (方案：角落图标) -->
    <button
      ref="menuToggleRef"
      :class="[$style.menuToggle, $style.desktopToggle]"
      @click="navigationStore.toggleMenu"
      :aria-expanded="navigationStore.isMenuOpen.toString()"
      aria-label="切换导航菜单"
      type="button"
    >
      <!-- 标准 HTML 注释：使用 SVG 图标 -->
      <svg :class="$style.icon" viewBox="0 0 24 24" width="24" height="24" fill="currentColor" aria-hidden="true">
        <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"></path>
      </svg>
      <!-- 标准 HTML 注释：首次访问微光提示动画元素 -->
      <span v-if="showMicroGlow" :class="$style.microGlow"></span>
    </button>

    <!-- 标准 HTML 注释：移动端触发器 (汉堡菜单) -->
     <button
        :class="[$style.menuToggle, $style.mobileToggle]"
        @click="navigationStore.toggleMenu"
        :aria-expanded="navigationStore.isMenuOpen.toString()"
        aria-label="切换导航菜单"
        type="button"
      >
        <!-- 标准 HTML 注释：使用 SVG 图标 -->
        <svg :class="$style.icon" viewBox="0 0 24 24" width="24" height="24" fill="currentColor" aria-hidden="true">
           <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z"></path>
        </svg>
     </button>

    <!-- 标准 HTML 注释：导航菜单组件 -->
    <NavigationMenu />
  </header>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { RouterLink } from 'vue-router';
import { useNavigationStore } from '@/stores/navigation';
import NavigationMenu from '@/components/features/NavigationMenu.vue';
import SearchBox from '@/components/features/SearchBox.vue';

const navigationStore = useNavigationStore();
const menuToggleRef = ref(null); // 用于微光效果定位
const showMicroGlow = ref(false);

// 微光提示逻辑 (方案要求)
onMounted(() => {
  const visitedKey = 'moyin-first-visit-glow'; // 使用特定 key
  if (typeof localStorage !== 'undefined') { // 确保 localStorage 可用
      if (!localStorage.getItem(visitedKey) && menuToggleRef.value) {
        console.log('First visit detected, showing micro glow.');
        showMicroGlow.value = true;
        // 动画结束后移除 glow 并标记已访问
        setTimeout(() => {
          showMicroGlow.value = false;
          localStorage.setItem(visitedKey, 'true');
        }, 2500); // 动画持续时间 + 延迟
      }
  }
});
</script>

<style module>
.header {
  /* 网格定位 */
  grid-column: 1 / 4; /* 水平撑满所有列（1到3列） */
  grid-row: 1 / 2;    /* 占据第一行 */
  display: flex;
  justify-content: flex-end; /* 菜单靠右显示 */
  align-items: center;
  padding: var(--space-m) var(--layout-padding-horizontal); /* 内边距 */
  position: relative; /* 为微光效果提供定位上下文 */
  z-index: 10; /* 确保在内容和背景之上 */
  gap: var(--space-m); /* 添加间距 */
}

.searchBox {
  margin-right: auto; /* 将搜索框推到左侧 */
}

.logoContainer {
  z-index: 11; /* 比菜单遮罩层高 */
}

.logoLink {
  display: inline-block;
  line-height: 0; /* 避免图片下方空白 */
  border-radius: 2px; /* 用于焦点轮廓 */
}
.logoLink:focus-visible {
   outline: 2px solid var(--color-highlight);
   outline-offset: 2px;
}

.logo {
  height: 70px; /* 根据实际 Logo 调整 */
  width: auto;
  display: block;
  color: var(--color-text-primary); /* SVG 颜色继承 */
}

.menuToggle {
  background: none;
  border: none;
  color: var(--color-text-primary);
  cursor: pointer;
  padding: var(--space-s); /* 提供点击区域 */
  position: relative; /* 为微光效果 */
  z-index: 11; /* 比菜单遮罩层高 */
  border-radius: var(--border-radius-soft);
  transition: color 0.2s ease;
}
.menuToggle:hover {
  color: var(--color-accent);
}
.menuToggle:focus-visible {
   outline: 2px solid var(--color-highlight);
   outline-offset: 2px;
   color: var(--color-highlight);
}

.icon { /* SVG 图标基本样式 */
  display: block; /* 避免额外空间 */
  width: 24px;
  height: 24px;
}

/* 响应式切换触发器 */
.desktopToggle { display: none; } /* 默认隐藏桌面版 */
.mobileToggle { display: block; } /* 默认显示移动版 */

@media (min-width: 1024px) { /* 桌面断点 */
  .header {
     /* 桌面端可以增加垂直内边距 */
     padding-top: var(--space-l);
     padding-bottom: var(--space-l);
  }
  .desktopToggle { display: block; } /* 显示桌面版 */
  .mobileToggle { display: none; }  /* 隐藏移动版 */
}

/* 微光效果 (方案要求) */
@keyframes subtleGlow {
  0%, 100% { box-shadow: 0 0 5px 2px rgba(var(--color-accent-rgb), 0); }
  50% { box-shadow: 0 0 15px 5px rgba(var(--color-accent-rgb), 0.4); }
}
.microGlow {
  position: absolute;
  top: -8px; left: -8px; right: -8px; bottom: -8px; /* 比按钮稍大 */
  border-radius: 50%; /* 圆形光晕 */
  animation: subtleGlow 2s ease-in-out 0.5s 1 forwards; /* 延迟 0.5s 开始 */
  pointer-events: none; /* 不阻挡点击 */
}
</style>