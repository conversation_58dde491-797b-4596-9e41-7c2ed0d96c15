<!-- /src/components/features/NavigationMenu.vue -->
<template>
  <!-- 标准 HTML 注释：使用 Teleport 将菜单渲染到 body 下，避免 z-index 问题 -->
  <Teleport to="body">
    <!-- 标准 HTML 注释：移动端遮罩层和菜单 -->
    <Transition name="nav-fade">
       <!-- 标准 HTML 注释：半透明背景遮罩层 -->
       <!-- 标准 HTML 注释：点击遮罩本身关闭菜单 -->
       <div
          v-if="navigationStore.isMenuOpen && isMobile"
          :class="$style.mobileOverlay"
          @click.self="navigationStore.closeMenu"
       >
          <!-- 标准 HTML 注释：侧滑菜单面板的过渡 -->
          <Transition name="nav-slide-left">
             <!-- 标准 HTML 注释：实际的导航菜单（移动端） -->
             <nav v-if="navigationStore.isMenuOpen" :class="[$style.menu, $style.mobileMenu]">
                <!-- 导航链接 -->
                <ul :class="$style.navLinks">
                  <li><RouterLink to="/" :class="$style.link">首页</RouterLink></li>
                  <li><RouterLink to="/books" :class="$style.link">著作集</RouterLink></li>
                  <li><RouterLink to="/about" :class="$style.link">关于</RouterLink></li>
                </ul>

                <!-- 标准 HTML 注释：筛选功能 -->
                <div :class="$style.filterSection">
                  <div>
                    <label for="year-filter" :class="$style.filterLabel">年份</label>
                    <select id="year-filter" :class="$style.filterSelect" @change="filterByYear" :value="navigationStore.selectedYear">
                      <option value="">全部年份</option>
                      <option v-for="year in years" :key="year" :value="year">{{ year }}</option>
                    </select>
                  </div>
                  <div>
                    <label for="category-filter" :class="$style.filterLabel">分类</label>
                    <select id="category-filter" :class="$style.filterSelect" @change="filterByCategory" :value="navigationStore.selectedCategory">
                      <option value="">全部分类</option>
                      <option v-for="category in categories" :key="category" :value="category">{{ category }}</option>
                    </select>
                  </div>
                </div>
                <!-- 标准 HTML 注释：关闭按钮 -->
                <button :class="$style.closeButton" @click="navigationStore.closeMenu" aria-label="关闭菜单" type="button">✕</button>
             </nav>
          </Transition>
       </div>
    </Transition>

    <!-- 标准 HTML 注释：桌面端菜单 -->
    <Transition name="nav-fade">
      <!-- 标准 HTML 注释：实际的导航菜单（桌面端） -->
      <nav v-if="navigationStore.isMenuOpen && !isMobile" :class="[$style.menu, $style.desktopMenu]">
         <ul>
           <li><RouterLink to="/" :class="$style.link">首页</RouterLink></li>
           <li><RouterLink to="/books" :class="$style.link">著作集</RouterLink></li>
           <li><RouterLink to="/about" :class="$style.link">关于</RouterLink></li>
         </ul>
      </nav>
    </Transition>
  </Teleport>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue';
import { RouterLink } from 'vue-router';
import { useNavigationStore } from '@/stores/navigation';

const navigationStore = useNavigationStore();

// 复制 HomeView.vue 中的数据和函数
const years = ref(['2025', '2024', '2023']);
const categories = ref(['诗歌创作', '文学作品', '杂谈随笔']);

const filterByYear = (event) => {
  const year = event.target.value;
  navigationStore.updateYear(year);
};

const filterByCategory = (event) => {
  const category = event.target.value;
  navigationStore.updateCategory(category);
};

// --- 响应式判断 ---
const mobileBreakpoint = 1024; // 与 CSS 保持一致
// 仅在浏览器环境获取 window.innerWidth
const getWindowWidth = () => (typeof window !== 'undefined' ? window.innerWidth : mobileBreakpoint);
const viewportWidth = ref(getWindowWidth());
const isMobile = computed(() => viewportWidth.value < mobileBreakpoint);

const updateViewportWidth = () => {
  viewportWidth.value = getWindowWidth();
};

// 监听窗口大小变化
onMounted(() => {
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', updateViewportWidth);
  }
});
onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('resize', updateViewportWidth);
  }
});

// 注意：关闭菜单的逻辑已移到 Pinia store 的 afterEach 钩子中
</script>

<style module>
/* 菜单通用基础样式 */
.menu {
  position: fixed;
  background-color: var(--color-background-subtle);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}
.menu ul { list-style: none; padding: 0; margin: 0; flex-grow: 1; }
.menu li { margin-bottom: var(--space-s); }

.link {
  color: var(--color-text-primary);
  text-decoration: none;
  font-size: 1.1rem;
  font-family: var(--font-family-sans);
  display: block;
  padding: var(--space-m) var(--space-l);
  margin: 0 calc(-1 * var(--space-l));
  border-radius: var(--border-radius-soft);
  transition: color 0.2s ease, background-color 0.2s ease;
}
.link:hover, .link:focus-visible {
  color: var(--color-accent-hover);
  background-color: rgba(var(--color-accent-rgb), 0.1);
  outline: none;
}
/* 活动路由链接样式 */
.link.router-link-exact-active {
    color: var(--color-accent);
    font-weight: bold;
    background-color: rgba(var(--color-accent-rgb), 0.15);
}

/* --- 移动端特定样式 --- */
.mobileOverlay {
  position: fixed; top: 0; left: 0; width: 100%; height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
  /* backdrop-filter: blur(4px); 可选模糊效果 */
}
.mobileMenu {
  top: 0; right: 0; height: 100%;
  width: clamp(280px, 80vw, 350px); /* 响应式宽度 */
  border-radius: 0;
  box-shadow: -5px 0 20px rgba(0, 0, 0, 0.25);
  padding: var(--space-xl); /* 统一内边距 */
  padding-top: var(--space-xxl); /* 顶部留出更多空间给关闭按钮 */
}
.navLinks {
  margin-bottom: var(--space-l);
  border-bottom: 1px solid rgba(var(--color-accent-rgb), 0.2);
  padding-bottom: var(--space-m);
}
.filterSection {
  padding-top: var(--space-m);
}
.filterLabel {
  display: block;
  margin-bottom: var(--space-xs);
  color: var(--color-accent);
  font-size: 0.9rem;
}
.filterSelect {
  width: 100%;
  padding: var(--space-xs) var(--space-s);
  margin-bottom: var(--space-m);
  background-color: rgba(30, 30, 30, 0.6);
  border: 1px solid rgba(var(--color-accent-rgb), 0.3);
  color: var(--color-text-primary);
  border-radius: var(--border-radius-soft);
}
.closeButton {
  position: absolute; top: var(--space-l); right: var(--space-l);
  background: none; border: none;
  color: var(--color-text-secondary);
  font-size: 1.8rem; line-height: 1;
  cursor: pointer; padding: var(--space-s);
  transition: color 0.2s ease; z-index: 1001;
}
.closeButton:hover { color: var(--color-text-primary); }

/* --- 桌面端特定样式 --- */
.desktopMenu {
  top: var(--space-l); right: var(--space-l); /* 定位 */
  border-radius: var(--border-radius-soft);
  min-width: 220px;
  padding: var(--space-m); /* 内边距 */
}
.desktopMenu ul { flex-grow: 0; } /* 不需要撑开空间 */

/* 响应式显示/隐藏 (由 v-if 控制，CSS 仅作备用) */
@media (min-width: 1024px) { .mobileOverlay, .mobileMenu { display: none !important; } }
@media (max-width: 1023px) { .desktopMenu { display: none !important; } }
</style>

<style>
/* NavigationMenu 使用的全局 Transition 类 */
.nav-fade-enter-active, .nav-fade-leave-active { transition: opacity 0.3s ease; }
.nav-fade-enter-from, .nav-fade-leave-to { opacity: 0; }

.nav-slide-left-enter-active, .nav-slide-left-leave-active { transition: transform 0.35s ease-in-out; }
.nav-slide-left-enter-from, .nav-slide-left-leave-to { transform: translateX(100%); }
.nav-slide-left-enter-to, .nav-slide-left-leave-from { transform: translateX(0); }
</style>