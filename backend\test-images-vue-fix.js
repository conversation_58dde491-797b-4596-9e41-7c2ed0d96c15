import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';

const BASE_URL = 'http://localhost:3001/api';

// 测试用的管理员凭据
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123456'
};

// 测试Images.vue修复
async function testImagesVueFix() {
  try {
    console.log('🔧 测试Images.vue修复...\n');
    
    // 1. 登录获取Token
    console.log('=== 步骤1：登录获取Token ===');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:5175'
      },
      body: JSON.stringify(ADMIN_CREDENTIALS)
    });

    const loginResult = await loginResponse.json();
    
    if (!loginResult.success) {
      console.log('✗ 登录失败:', loginResult.message);
      return false;
    }

    const token = loginResult.data.token;
    console.log('✓ 登录成功');
    console.log(`  Token: ${token.substring(0, 50)}...`);

    // 2. 模拟前端Images.vue的上传请求
    console.log('\n=== 步骤2：模拟Images.vue上传请求 ===');
    
    // 创建测试图片文件
    const testImagePath = path.join(process.cwd(), 'test-images-vue.jpg');
    const existingImagePath = path.join(process.cwd(), '..', 'public', 'articles', 'img', '1.jpg');
    
    if (fs.existsSync(existingImagePath)) {
      fs.copyFileSync(existingImagePath, testImagePath);
      console.log('✓ 测试图片文件准备完成');
    } else {
      console.log('✗ 没有找到测试图片文件');
      return false;
    }

    // 使用FormData模拟前端上传（使用admin_token）
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));

    console.log('发送上传请求...');
    console.log(`  URL: ${BASE_URL}/images/upload`);
    console.log(`  Origin: http://localhost:5175`);
    console.log(`  Authorization: Bearer ${token.substring(0, 20)}... (使用admin_token)`);

    const uploadResponse = await fetch(`${BASE_URL}/images/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,  // 模拟修复后的admin_token
        'Origin': 'http://localhost:5175',
        ...formData.getHeaders()
      },
      body: formData
    });

    console.log(`响应状态码: ${uploadResponse.status}`);
    
    // 清理测试文件
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
    }

    const uploadResult = await uploadResponse.json();
    console.log(`响应消息: ${uploadResult.message}`);
    
    if (uploadResult.success) {
      console.log('✓ Images.vue上传测试成功');
      console.log(`  文件名: ${uploadResult.data.filename}`);
      console.log(`  URL: ${uploadResult.data.url}`);
      
      // 清理上传的测试图片
      try {
        await fetch(`${BASE_URL}/images/${uploadResult.data.id}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log('✓ 测试图片已清理');
      } catch (error) {
        console.log('⚠ 清理测试图片失败');
      }
      
      return true;
    } else {
      console.log('✗ Images.vue上传测试失败');
      return false;
    }
    
  } catch (error) {
    console.log('✗ 测试过程中发生错误:', error.message);
    return false;
  }
}

// 主测试函数
async function runImagesVueFixTest() {
  console.log('🎯 开始Images.vue修复验证测试...\n');
  
  const success = await testImagesVueFix();
  
  console.log('\n🏆 Images.vue修复测试结果:');
  console.log(`修复验证: ${success ? '✅ 成功' : '❌ 失败'}`);
  
  if (success) {
    console.log('\n🎉 Images.vue问题已完全修复！');
    console.log('修复详情:');
    console.log('1. ✅ 修复了Token Key问题：');
    console.log('   - 将localStorage.getItem("token")');
    console.log('   - 修改为localStorage.getItem("admin_token")');
    console.log('2. ✅ 修复了Element Plus进度条状态问题：');
    console.log('   - 将status: "uploading"');
    console.log('   - 修改为status: ""');
    console.log('\n💡 用户操作建议:');
    console.log('1. 重新登录以获取有效的admin_token');
    console.log('2. 访问 http://localhost:5175/images');
    console.log('3. 现在图片上传功能应该正常工作');
    console.log('4. Element Plus警告应该消失');
  } else {
    console.log('\n❌ Images.vue修复验证失败');
    console.log('请检查:');
    console.log('1. 前端开发服务器是否正在运行');
    console.log('2. 后端服务器是否正在运行');
    console.log('3. 用户是否已重新登录');
  }
}

runImagesVueFixTest().catch(console.error);
