import db from './src/config/database.js';
import fs from 'fs/promises';
import path from 'path';

async function fixImagePaths() {
  console.log('开始修复图片路径...');
  
  try {
    // 获取所有图片记录
    const images = db.prepare('SELECT * FROM images').all();
    console.log(`发现 ${images.length} 个图片记录`);

    for (const image of images) {
      // 更新路径为正确的前端路径
      const newPath = path.join(path.resolve('../public/articles/img'), image.filename);
      
      // 检查文件是否存在
      try {
        await fs.access(newPath);
        
        // 更新数据库记录
        const updateImage = db.prepare('UPDATE images SET path = ? WHERE id = ?');
        updateImage.run(newPath, image.id);
        
        console.log(`✓ 更新图片路径: ${image.filename}`);
      } catch (error) {
        console.log(`⚠ 图片文件不存在: ${image.filename}`);
      }
    }

    console.log('图片路径修复完成！');
  } catch (error) {
    console.error('修复图片路径时出错:', error);
  }
}

fixImagePaths();
