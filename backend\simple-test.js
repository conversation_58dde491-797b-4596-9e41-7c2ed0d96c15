import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('Current directory:', __dirname);
console.log('Database path:', path.join(__dirname, 'database.sqlite'));

try {
  const db = new Database('database.sqlite');
  console.log('✓ Database connected successfully');
  
  // 检查表结构
  console.log('\nChecking tables...');
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
  console.log('Tables:', tables.map(t => t.name));
  
  // 检查图片表
  if (tables.some(t => t.name === 'images')) {
    const imageCount = db.prepare('SELECT COUNT(*) as count FROM images').get();
    console.log(`Images count: ${imageCount.count}`);
    
    if (imageCount.count > 0) {
      const firstImage = db.prepare('SELECT * FROM images LIMIT 1').get();
      console.log('First image:', firstImage);
    }
  }
  
  // 检查文章表
  if (tables.some(t => t.name === 'articles')) {
    const articleCount = db.prepare('SELECT COUNT(*) as count FROM articles').get();
    console.log(`Articles count: ${articleCount.count}`);
  }
  
  db.close();
  console.log('✓ Test completed');
} catch (error) {
  console.error('✗ Database error:', error.message);
}
