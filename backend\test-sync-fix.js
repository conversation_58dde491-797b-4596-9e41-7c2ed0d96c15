import fetch from 'node-fetch';
import Database from 'better-sqlite3';
import { syncArticlesToFrontend } from './src/utils/syncToFrontend.js';

const BASE_URL = 'http://localhost:3001/api';
const db = new Database('database.sqlite');

// 测试用的管理员凭据
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123456'
};

let authToken = '';

// 登录获取token
async function login() {
  try {
    const response = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(ADMIN_CREDENTIALS)
    });

    const result = await response.json();
    if (result.success) {
      authToken = result.data.token;
      console.log('✓ 登录成功');
      return true;
    } else {
      console.log('✗ 登录失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('✗ 登录请求失败:', error.message);
    return false;
  }
}

// 创建测试文章
async function createTestArticle() {
  try {
    console.log('\n=== 创建测试文章 ===');
    
    const testArticle = {
      title: '测试文章 - 数据同步验证',
      slug: 'test-sync-article-' + Date.now(),
      description: '这是一个用于测试数据同步功能的文章',
      content: '# 测试文章内容\n\n这是测试文章的内容。',
      auxiliary_content: '这是辅助介绍内容，应该在同步后保持不变。',
      status: 'published',
      featured: false,
      categories: [],
      tags: [],
      articleImages: [1, 2] // 使用现有的图片ID
    };

    const response = await fetch(`${BASE_URL}/articles`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(testArticle)
    });

    const result = await response.json();
    if (result.success) {
      console.log('✓ 测试文章创建成功');
      console.log(`  文章ID: ${result.data.id}`);
      console.log(`  标题: ${result.data.title}`);
      console.log(`  辅助内容: ${result.data.auxiliary_content}`);
      return result.data.id;
    } else {
      console.log('✗ 测试文章创建失败:', result.message);
      return null;
    }
  } catch (error) {
    console.log('✗ 创建测试文章失败:', error.message);
    return null;
  }
}

// 检查文章配图关联
async function checkArticleImages(articleId) {
  try {
    console.log('\n=== 检查文章配图关联 ===');
    
    const articleImages = db.prepare(`
      SELECT ai.*, i.filename, i.original_name 
      FROM article_images ai
      JOIN images i ON ai.image_id = i.id
      WHERE ai.article_id = ?
      ORDER BY ai.sort_order
    `).all(articleId);

    console.log(`✓ 文章配图数量: ${articleImages.length}`);
    articleImages.forEach((img, index) => {
      console.log(`  配图${index + 1}: ${img.filename} (ID: ${img.image_id})`);
    });

    return articleImages.length > 0;
  } catch (error) {
    console.log('✗ 检查文章配图失败:', error.message);
    return false;
  }
}

// 测试数据同步
async function testDataSync() {
  try {
    console.log('\n=== 测试数据同步 ===');
    
    const result = await syncArticlesToFrontend(db);
    if (result.success) {
      console.log(`✓ 数据同步成功，同步了 ${result.count} 篇文章`);
      return true;
    } else {
      console.log('✗ 数据同步失败:', result.error);
      return false;
    }
  } catch (error) {
    console.log('✗ 数据同步异常:', error.message);
    return false;
  }
}

// 验证同步后的数据
async function verifySync(articleId) {
  try {
    console.log('\n=== 验证同步后的数据 ===');
    
    // 检查数据库中的文章数据
    const article = db.prepare(`
      SELECT a.*, 
             GROUP_CONCAT(DISTINCT c.name) as category_names,
             GROUP_CONCAT(DISTINCT t.name) as tag_names
      FROM articles a
      LEFT JOIN article_categories ac ON a.id = ac.article_id
      LEFT JOIN categories c ON ac.category_id = c.id
      LEFT JOIN article_tags at ON a.id = at.article_id
      LEFT JOIN tags t ON at.tag_id = t.id
      WHERE a.id = ?
      GROUP BY a.id
    `).get(articleId);

    if (article) {
      console.log('✓ 数据库中的文章数据:');
      console.log(`  标题: ${article.title}`);
      console.log(`  辅助内容: ${article.auxiliary_content}`);
      console.log(`  状态: ${article.status}`);
      
      // 检查配图
      const images = db.prepare(`
        SELECT i.* FROM images i
        JOIN article_images ai ON i.id = ai.image_id
        WHERE ai.article_id = ?
        ORDER BY ai.sort_order
      `).all(articleId);
      
      console.log(`  配图数量: ${images.length}`);
      
      return {
        hasAuxiliaryContent: !!article.auxiliary_content,
        hasImages: images.length > 0,
        article: article
      };
    } else {
      console.log('✗ 未找到文章数据');
      return null;
    }
  } catch (error) {
    console.log('✗ 验证同步数据失败:', error.message);
    return null;
  }
}

// 清理测试数据
async function cleanupTestArticle(articleId) {
  if (!articleId) return;
  
  try {
    const response = await fetch(`${BASE_URL}/articles/${articleId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const result = await response.json();
    if (result.success) {
      console.log('✓ 测试文章已清理');
    }
  } catch (error) {
    console.log('⚠ 清理测试文章失败:', error.message);
  }
}

// 主测试函数
async function runSyncTest() {
  console.log('开始数据同步功能测试...\n');
  
  // 登录
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('测试终止：无法登录');
    return;
  }
  
  // 创建测试文章
  const articleId = await createTestArticle();
  if (!articleId) {
    console.log('测试终止：无法创建测试文章');
    return;
  }
  
  // 检查文章配图关联
  await checkArticleImages(articleId);
  
  // 测试数据同步
  const syncSuccess = await testDataSync();
  
  // 验证同步后的数据
  const verifyResult = await verifySync(articleId);
  
  if (verifyResult) {
    console.log('\n=== 测试结果 ===');
    console.log(`辅助内容保持: ${verifyResult.hasAuxiliaryContent ? '✓' : '✗'}`);
    console.log(`配图关联保持: ${verifyResult.hasImages ? '✓' : '✗'}`);
    console.log(`数据同步成功: ${syncSuccess ? '✓' : '✗'}`);
  }
  
  // 清理测试数据
  await cleanupTestArticle(articleId);
  
  console.log('\n数据同步功能测试完成');
}

runSyncTest().catch(console.error).finally(() => {
  db.close();
});
