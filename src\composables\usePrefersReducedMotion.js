// /src/composables/usePrefersReducedMotion.js
import { ref, onMounted, onUnmounted } from 'vue';

export function usePrefersReducedMotion() {
  // 仅在浏览器环境执行
  if (typeof window === 'undefined' || !window.matchMedia) {
    return ref(false); // 服务端渲染或不支持时，默认不减少动画
  }

  const query = '(prefers-reduced-motion: reduce)';
  const mediaQueryList = window.matchMedia(query);
  const prefersReducedMotion = ref(mediaQueryList.matches);

  const listener = (event) => {
    prefersReducedMotion.value = event.matches;
  };

  onMounted(() => {
    mediaQueryList.addEventListener('change', listener);
  });

  onUnmounted(() => {
    mediaQueryList.removeEventListener('change', listener);
  });

  return prefersReducedMotion;
}

/* 使用示例:
import { usePrefersReducedMotion } from '@/composables/usePrefersReducedMotion';
const prefersReduced = usePrefersReducedMotion();

// 在 <template> 中:
<Transition :name="prefersReduced ? 'none' : 'fade'">...</Transition>

// 或在 <script setup> 中根据 prefersReduced.value 决定是否添加动画类
*/