import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';

const BASE_URL = 'http://localhost:3001/api';

// 测试用的管理员凭据
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123456'
};

let authToken = '';

// 登录获取token
async function login() {
  try {
    const response = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(ADMIN_CREDENTIALS)
    });

    const result = await response.json();
    if (result.success) {
      authToken = result.data.token;
      console.log('✓ 登录成功');
      return true;
    } else {
      console.log('✗ 登录失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('✗ 登录请求失败:', error.message);
    return false;
  }
}

// 测试图片列表API
async function testImageList() {
  try {
    console.log('\n=== 测试图片列表API ===');
    const response = await fetch(`${BASE_URL}/images`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const result = await response.json();
    if (result.success) {
      console.log(`✓ 图片列表API正常，共 ${result.data.images.length} 张图片`);
      if (result.data.images.length > 0) {
        const firstImage = result.data.images[0];
        console.log(`  第一张图片: ${firstImage.filename}`);
        console.log(`  URL: ${firstImage.url}`);
        
        // 测试图片静态文件访问
        const imageUrl = `http://localhost:3001${firstImage.url}`;
        const imageResponse = await fetch(imageUrl);
        if (imageResponse.ok) {
          console.log('✓ 图片静态文件访问正常');
        } else {
          console.log('✗ 图片静态文件访问失败:', imageResponse.status);
        }
      }
      return true;
    } else {
      console.log('✗ 图片列表API失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('✗ 图片列表API请求失败:', error.message);
    return false;
  }
}

// 测试图片上传
async function testImageUpload() {
  try {
    console.log('\n=== 测试图片上传 ===');
    
    // 创建一个测试图片文件
    const testImagePath = path.join(process.cwd(), 'test-image.jpg');
    
    // 检查是否有现有的图片文件可以用于测试
    const existingImagePath = path.join(process.cwd(), '..', 'public', 'articles', 'img', '1.jpg');
    let imageToUpload = testImagePath;
    
    if (fs.existsSync(existingImagePath)) {
      // 复制现有图片作为测试文件
      fs.copyFileSync(existingImagePath, testImagePath);
      console.log('✓ 使用现有图片作为测试文件');
    } else {
      console.log('✗ 没有找到测试图片文件');
      return false;
    }

    // 创建FormData
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));

    const response = await fetch(`${BASE_URL}/images/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        ...formData.getHeaders()
      },
      body: formData
    });

    const result = await response.json();
    
    // 清理测试文件
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
    }

    if (result.success) {
      console.log('✓ 图片上传成功');
      console.log(`  文件名: ${result.data.filename}`);
      console.log(`  URL: ${result.data.url}`);
      
      // 测试上传的图片是否可以访问
      const uploadedImageUrl = `http://localhost:3001${result.data.url}`;
      const imageResponse = await fetch(uploadedImageUrl);
      if (imageResponse.ok) {
        console.log('✓ 上传的图片可以正常访问');
      } else {
        console.log('✗ 上传的图片无法访问');
      }
      
      return result.data.id;
    } else {
      console.log('✗ 图片上传失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('✗ 图片上传请求失败:', error.message);
    return false;
  }
}

// 清理测试图片
async function cleanupTestImage(imageId) {
  if (!imageId) return;
  
  try {
    const response = await fetch(`${BASE_URL}/images/${imageId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const result = await response.json();
    if (result.success) {
      console.log('✓ 测试图片已清理');
    }
  } catch (error) {
    console.log('⚠ 清理测试图片失败:', error.message);
  }
}

// 主测试函数
async function runTests() {
  console.log('开始图片上传功能测试...\n');
  
  // 登录
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('测试终止：无法登录');
    return;
  }
  
  // 测试图片列表
  await testImageList();
  
  // 测试图片上传
  const uploadedImageId = await testImageUpload();
  
  // 清理测试数据
  if (uploadedImageId) {
    await cleanupTestImage(uploadedImageId);
  }
  
  console.log('\n图片上传功能测试完成');
}

runTests().catch(console.error);
