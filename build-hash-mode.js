// build-hash-mode.js
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 备份原始文件
console.log('备份原始文件...');
fs.copyFileSync(
  path.join(__dirname, 'index.html'),
  path.join(__dirname, 'index.html.bak')
);

// 修改index.html，使用main-hash.js
console.log('修改index.html...');
let indexHtml = fs.readFileSync(path.join(__dirname, 'index.html'), 'utf8');
indexHtml = indexHtml.replace(
  '<script type="module" src="/src/main.js"></script>',
  '<script type="module" src="/src/main-hash.js"></script>'
);
fs.writeFileSync(path.join(__dirname, 'index.html'), indexHtml);

// 构建项目
console.log('构建项目...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('构建成功！');
} catch (error) {
  console.error('构建失败：', error);
} finally {
  // 恢复原始文件
  console.log('恢复原始文件...');
  fs.copyFileSync(
    path.join(__dirname, 'index.html.bak'),
    path.join(__dirname, 'index.html')
  );
  fs.unlinkSync(path.join(__dirname, 'index.html.bak'));
}

console.log('完成！');
