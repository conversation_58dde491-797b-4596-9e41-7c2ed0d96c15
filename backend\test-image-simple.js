import fetch from 'node-fetch';

async function testImageIntegration() {
  console.log('🖼️ 开始图片集成测试...\n');

  try {
    // 1. 测试后端静态文件服务
    console.log('1. 测试后端静态文件服务');
    const backendImageResponse = await fetch('http://localhost:3001/articles/img/30.jpg');
    if (backendImageResponse.ok) {
      console.log('✓ 后端图片访问正常');
    } else {
      console.log('✗ 后端图片访问失败:', backendImageResponse.status);
    }

    // 2. 测试前端静态文件服务
    console.log('\n2. 测试前端静态文件服务');
    const frontendImageResponse = await fetch('http://localhost:5175/articles/img/30.jpg');
    if (frontendImageResponse.ok) {
      console.log('✓ 前端图片访问正常');
    } else {
      console.log('✗ 前端图片访问失败:', frontendImageResponse.status);
    }

    // 3. 测试管理界面访问
    console.log('\n3. 测试管理界面访问');
    const adminResponse = await fetch('http://localhost:5174');
    if (adminResponse.ok) {
      console.log('✓ 管理界面访问正常');
    } else {
      console.log('✗ 管理界面访问失败:', adminResponse.status);
    }

    // 4. 测试前端博客访问
    console.log('\n4. 测试前端博客访问');
    const frontendResponse = await fetch('http://localhost:5175');
    if (frontendResponse.ok) {
      console.log('✓ 前端博客访问正常');
    } else {
      console.log('✗ 前端博客访问失败:', frontendResponse.status);
    }

    // 5. 测试图片API
    console.log('\n5. 测试图片API');
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123456'
      })
    });

    const loginResult = await loginResponse.json();
    if (loginResult.success) {
      const token = loginResult.data.token;
      
      const imagesResponse = await fetch('http://localhost:3001/api/images', {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      const imagesResult = await imagesResponse.json();
      if (imagesResult.success) {
        console.log(`✓ 图片API正常，共 ${imagesResult.data.images?.length || 0} 张图片`);
        
        if (imagesResult.data.images.length > 0) {
          const firstImage = imagesResult.data.images[0];
          console.log(`✓ 图片URL格式: ${firstImage.url}`);
        }
      } else {
        console.log('✗ 图片API异常');
      }
    } else {
      console.log('✗ 登录失败');
    }

    console.log('\n🎉 图片集成测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testImageIntegration();
