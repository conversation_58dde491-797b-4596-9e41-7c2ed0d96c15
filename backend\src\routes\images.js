import express from 'express';
import multer from 'multer';
import sharp from 'sharp';
import path from 'path';
import fs from 'fs/promises';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';
import db from '../config/database.js';

const router = express.Router();

// 配置 multer 用于文件上传
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    // 保存到前端的图片目录
    const uploadDir = path.join(process.cwd(), '..', 'public', 'articles', 'img');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, uniqueSuffix + ext);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 10 * 1024 * 1024 // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = (process.env.ALLOWED_IMAGE_TYPES || 'image/jpeg,image/png,image/gif,image/webp').split(',');
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的文件类型'));
    }
  }
});

// 获取图片列表
router.get('/', authenticateToken, (req, res, next) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const images = db.prepare('SELECT * FROM images ORDER BY created_at DESC LIMIT ? OFFSET ?')
      .all(parseInt(limit), parseInt(offset));

    // 为每个图片添加URL
    const imagesWithUrl = images.map(image => ({
      ...image,
      url: `/articles/img/${image.filename}`
    }));

    const { total } = db.prepare('SELECT COUNT(*) as total FROM images').get();

    res.json({
      success: true,
      data: {
        images: imagesWithUrl,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

// 上传图片
router.post('/upload', authenticateToken, requireAdmin, upload.single('image'), async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的图片'
      });
    }

    const { filename, originalname, path: filePath, size, mimetype } = req.file;

    // 使用 sharp 获取图片尺寸
    const metadata = await sharp(filePath).metadata();

    // 保存图片信息到数据库
    const insertImage = db.prepare(`
      INSERT INTO images (filename, original_name, path, size, mime_type, width, height)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    const result = insertImage.run(
      filename,
      originalname,
      filePath,
      size,
      mimetype,
      metadata.width,
      metadata.height
    );

    const newImage = db.prepare('SELECT * FROM images WHERE id = ?').get(result.lastInsertRowid);

    res.status(201).json({
      success: true,
      message: '图片上传成功',
      data: {
        ...newImage,
        url: `/articles/img/${filename}`
      }
    });
  } catch (error) {
    next(error);
  }
});

// 删除图片
router.delete('/:id', authenticateToken, requireAdmin, async (req, res, next) => {
  try {
    const { id } = req.params;

    const image = db.prepare('SELECT * FROM images WHERE id = ?').get(id);

    if (!image) {
      return res.status(404).json({
        success: false,
        message: '图片不存在'
      });
    }

    // 删除物理文件
    try {
      await fs.unlink(image.path);
    } catch (error) {
      console.warn(`警告: 无法删除文件 ${image.path}:`, error.message);
    }

    // 从数据库删除记录
    db.prepare('DELETE FROM images WHERE id = ?').run(id);

    res.json({
      success: true,
      message: '图片删除成功'
    });
  } catch (error) {
    next(error);
  }
});

// 批量上传图片
router.post('/batch-upload', authenticateToken, requireAdmin, upload.array('images', 10), async (req, res, next) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请选择要上传的图片'
      });
    }

    const uploadedImages = [];
    const insertImage = db.prepare(`
      INSERT INTO images (filename, original_name, path, size, mime_type, width, height)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    for (const file of req.files) {
      try {
        const { filename, originalname, path: filePath, size, mimetype } = file;

        // 使用 sharp 获取图片尺寸
        const metadata = await sharp(filePath).metadata();

        // 保存图片信息到数据库
        const result = insertImage.run(
          filename,
          originalname,
          filePath,
          size,
          mimetype,
          metadata.width,
          metadata.height
        );

        const newImage = db.prepare('SELECT * FROM images WHERE id = ?').get(result.lastInsertRowid);
        uploadedImages.push({
          ...newImage,
          url: `/articles/img/${filename}`
        });
      } catch (error) {
        console.error(`处理图片 ${file.originalname} 失败:`, error.message);
      }
    }

    res.status(201).json({
      success: true,
      message: `成功上传 ${uploadedImages.length} 张图片`,
      data: uploadedImages
    });
  } catch (error) {
    next(error);
  }
});

export default router;
