import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';

const BASE_URL = 'http://localhost:3001/api';

// 测试用的管理员凭据
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123456'
};

// 测试CORS修复
async function testCORSFix() {
  try {
    console.log('🔧 测试CORS修复（5174端口）...\n');
    
    // 1. 登录获取Token
    console.log('=== 步骤1：登录获取Token ===');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:5174'  // 使用正确的前端端口
      },
      body: JSON.stringify(ADMIN_CREDENTIALS)
    });

    const loginResult = await loginResponse.json();
    
    if (!loginResult.success) {
      console.log('✗ 登录失败:', loginResult.message);
      return false;
    }

    const token = loginResult.data.token;
    console.log('✓ 登录成功');
    console.log(`  Token: ${token.substring(0, 50)}...`);

    // 2. 测试CORS预检请求
    console.log('\n=== 步骤2：测试CORS预检请求 ===');
    const corsResponse = await fetch(`${BASE_URL}/images/upload`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:5174',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'authorization,content-type'
      }
    });

    console.log(`OPTIONS请求状态码: ${corsResponse.status}`);
    
    const corsHeaders = {};
    corsResponse.headers.forEach((value, key) => {
      if (key.toLowerCase().includes('access-control')) {
        corsHeaders[key] = value;
      }
    });
    
    console.log('CORS响应头:', corsHeaders);
    
    const allowedOrigin = corsHeaders['access-control-allow-origin'];
    const corsOk = allowedOrigin === 'http://localhost:5174';
    
    if (corsOk) {
      console.log('✓ CORS配置正确，允许5174端口');
    } else {
      console.log(`✗ CORS配置错误，允许的Origin: ${allowedOrigin}`);
      return false;
    }

    // 3. 测试图片上传（模拟前端5174端口）
    console.log('\n=== 步骤3：测试图片上传（5174端口） ===');
    
    // 创建测试图片文件
    const testImagePath = path.join(process.cwd(), 'test-cors-fix.jpg');
    const existingImagePath = path.join(process.cwd(), '..', 'public', 'articles', 'img', '1.jpg');
    
    if (fs.existsSync(existingImagePath)) {
      fs.copyFileSync(existingImagePath, testImagePath);
      console.log('✓ 测试图片文件准备完成');
    } else {
      console.log('✗ 没有找到测试图片文件');
      return false;
    }

    // 使用FormData模拟前端上传
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));

    console.log('发送上传请求...');
    console.log(`  URL: ${BASE_URL}/images/upload`);
    console.log(`  Origin: http://localhost:5174`);
    console.log(`  Authorization: Bearer ${token.substring(0, 20)}...`);

    const uploadResponse = await fetch(`${BASE_URL}/images/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Origin': 'http://localhost:5174',
        ...formData.getHeaders()
      },
      body: formData
    });

    console.log(`响应状态码: ${uploadResponse.status}`);
    
    // 清理测试文件
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
    }

    const uploadResult = await uploadResponse.json();
    console.log(`响应消息: ${uploadResult.message}`);
    
    if (uploadResult.success) {
      console.log('✓ 图片上传成功（CORS问题已解决）');
      console.log(`  文件名: ${uploadResult.data.filename}`);
      console.log(`  URL: ${uploadResult.data.url}`);
      
      // 清理上传的测试图片
      try {
        await fetch(`${BASE_URL}/images/${uploadResult.data.id}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log('✓ 测试图片已清理');
      } catch (error) {
        console.log('⚠ 清理测试图片失败');
      }
      
      return true;
    } else {
      console.log('✗ 图片上传失败');
      return false;
    }
    
  } catch (error) {
    console.log('✗ 测试过程中发生错误:', error.message);
    return false;
  }
}

// 主测试函数
async function runCORSFixTest() {
  console.log('🎯 开始CORS修复验证测试...\n');
  
  const success = await testCORSFix();
  
  console.log('\n🏆 CORS修复测试结果:');
  console.log(`修复验证: ${success ? '✅ 成功' : '❌ 失败'}`);
  
  if (success) {
    console.log('\n🎉 CORS问题已完全修复！');
    console.log('修复详情:');
    console.log('- 后端CORS_ORIGIN已设置为 http://localhost:5174');
    console.log('- 前端现在可以正常访问后端API');
    console.log('- 图片上传功能已恢复正常');
    console.log('\n💡 用户操作建议:');
    console.log('1. 现在可以正常访问 http://localhost:5174/images');
    console.log('2. 点击"上传图片"按钮应该能成功上传');
    console.log('3. 如果仍有问题，请确保前端使用正确的admin_token');
  } else {
    console.log('\n❌ CORS修复验证失败');
    console.log('请检查:');
    console.log('1. 后端服务器是否正在运行');
    console.log('2. .env文件中的CORS_ORIGIN配置');
    console.log('3. 前端是否运行在正确的端口');
  }
}

runCORSFixTest().catch(console.error);
