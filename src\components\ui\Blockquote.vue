<!-- /src/components/ui/Blockquote.vue -->
<template>
  <!-- 标准 HTML 注释：引用块组件 -->
  <blockquote :cite="citation" :class="$style.blockquote">
    <!-- 标准 HTML 注释：引用内容通过 slot 传入 -->
    <slot></slot>
  </blockquote>
</template>

<script setup>
defineProps({
  citation: { type: String, default: null } // 引用来源 URL (可选)
});
</script>

<style module>
.blockquote {
  border-left: 1px solid var(--blockquote-border-color); /* 极纤细左侧竖线 */
  padding: var(--blockquote-padding); /* 使用 CSS 变量 */
  margin: var(--space-xl) 0; /* 上下外边距增加，左右不缩进 */
  margin-left: var(--space-l); /* 显著视觉缩进 */
  background-color: rgba(var(--color-accent-rgb), 0.02); /* 更加微妙的背景 */
  font-style: italic;
  font-family: var(--font-family-body); /* 确保使用正文无衬线字体 */
  color: var(--color-text-secondary);
  letter-spacing: var(--letter-spacing-body); /* 使用统一的字间距变量 */
  line-height: var(--line-height-base); /* 使用统一的行高 */
}

/* 重置内部 P 标签的边距，使其更紧凑 */
.blockquote p:last-child {
   margin-bottom: 0;
}
/* 允许引用内的文本换行 */
.blockquote p {
    max-width: none;
}
</style>