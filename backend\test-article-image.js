import fetch from 'node-fetch';

async function testArticleImageIntegration() {
  console.log('📝 测试文章编辑页面图片集成...\n');

  try {
    // 1. 登录获取token
    console.log('1. 管理员登录');
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123456'
      })
    });

    const loginResult = await loginResponse.json();
    if (!loginResult.success) {
      throw new Error('登录失败: ' + loginResult.message);
    }

    const token = loginResult.data.token;
    console.log('✓ 登录成功');

    // 2. 获取图片列表
    console.log('\n2. 获取图片列表');
    const imagesResponse = await fetch('http://localhost:3001/api/images?limit=5', {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    const imagesResult = await imagesResponse.json();
    if (imagesResult.success && imagesResult.data.images.length > 0) {
      console.log(`✓ 获取到 ${imagesResult.data.images.length} 张图片`);
      
      const testImage = imagesResult.data.images[0];
      console.log(`✓ 测试图片: ${testImage.filename}`);
      console.log(`✓ 图片URL: ${testImage.url}`);

      // 3. 创建包含图片的测试文章
      console.log('\n3. 创建包含图片的测试文章');
      const testArticle = {
        title: '图片编辑器集成测试',
        slug: 'image-editor-test-' + Date.now(),
        description: '测试Markdown编辑器中的图片插入和预览功能',
        content: `# 图片编辑器集成测试

这是一篇测试文章，用于验证Markdown编辑器中的图片功能。

## 测试图片

下面是通过图片选择器插入的图片：

![${testImage.original_name}](${testImage.url})

## 测试说明

1. 图片应该能在编辑器预览中正常显示
2. 图片应该能在前端博客中正常显示
3. 图片路径应该正确解析

测试完成时间: ${new Date().toLocaleString()}`,
        status: 'draft',
        featured: false,
        categories: [6], // 杂谈随笔
        tags: [18] // 随笔
      };

      const articleResponse = await fetch('http://localhost:3001/api/articles', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(testArticle)
      });

      const articleResult = await articleResponse.json();
      if (articleResult.success) {
        console.log('✓ 测试文章创建成功');
        console.log(`✓ 文章ID: ${articleResult.data.id}`);
        console.log(`✓ 文章标题: ${articleResult.data.title}`);

        // 4. 测试图片URL访问
        console.log('\n4. 测试图片URL访问');
        const imageUrlResponse = await fetch(`http://localhost:3001${testImage.url}`);
        if (imageUrlResponse.ok) {
          console.log('✓ 图片URL在后端可正常访问');
        } else {
          console.log('✗ 图片URL在后端无法访问:', imageUrlResponse.status);
        }

        // 5. 测试管理界面访问
        console.log('\n5. 测试管理界面访问');
        const adminResponse = await fetch('http://localhost:5174');
        if (adminResponse.ok) {
          console.log('✓ 管理界面可正常访问');
          console.log(`✓ 文章编辑页面: http://localhost:5174/articles/edit/${articleResult.data.id}`);
        } else {
          console.log('✗ 管理界面无法访问');
        }

        // 6. 清理测试文章
        console.log('\n6. 清理测试数据');
        const deleteResponse = await fetch(`http://localhost:3001/api/articles/${articleResult.data.id}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (deleteResponse.ok) {
          console.log('✓ 测试文章已清理');
        } else {
          console.log('⚠ 清理测试文章失败');
        }
      } else {
        console.log('✗ 测试文章创建失败:', articleResult.message);
      }
    } else {
      console.log('⚠ 没有可用的图片进行测试');
    }

    console.log('\n🎉 文章图片集成测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testArticleImageIntegration();
