import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 路由组件
import Login from '@/views/Login.vue'
import Layout from '@/components/Layout.vue'
import Dashboard from '@/views/Dashboard.vue'
import Articles from '@/views/Articles.vue'
import ArticleEdit from '@/views/ArticleEdit.vue'
import Images from '@/views/Images.vue'
import Categories from '@/views/Categories.vue'
import Tags from '@/views/Tags.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: Dashboard
      },
      {
        path: 'articles',
        name: 'Articles',
        component: Articles
      },
      {
        path: 'articles/new',
        name: 'ArticleNew',
        component: ArticleEdit
      },
      {
        path: 'articles/:id/edit',
        name: 'ArticleEdit',
        component: ArticleEdit
      },
      {
        path: 'images',
        name: 'Images',
        component: Images
      },
      {
        path: 'categories',
        name: 'Categories',
        component: Categories
      },
      {
        path: 'tags',
        name: 'Tags',
        component: Tags
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
  } else {
    next()
  }
})

export default router
