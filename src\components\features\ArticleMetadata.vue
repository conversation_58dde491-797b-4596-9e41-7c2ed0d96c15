<!-- /src/components/features/ArticleMetadata.vue -->
<template>
  <p :class="$style.metadata">
    {{ date }} | {{ categories.join(', ') }}
  </p>
</template>

<script setup>
defineProps({
  date: {
    type: String,
    required: true
  },
  categories: {
    type: Array,
    required: true
  }
});
</script>

<style module>
.metadata {
  color: var(--color-text-secondary);
  font-size: 0.9rem;
  font-weight: var(--font-weight-light);
  letter-spacing: var(--letter-spacing-meta);
  margin: 0 0 var(--space-m);
  line-height: 1.6;
}
</style> 