import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';

const BASE_URL = 'http://localhost:3001/api';

// 测试用的管理员凭据
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123456'
};

let authToken = '';

// 登录获取token
async function login() {
  try {
    const response = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(ADMIN_CREDENTIALS)
    });

    const result = await response.json();
    if (result.success) {
      authToken = result.data.token;
      console.log('✓ 登录成功');
      return true;
    } else {
      console.log('✗ 登录失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('✗ 登录请求失败:', error.message);
    return false;
  }
}

// 测试1：图片上传功能（CORS修复验证）
async function testImageUploadCORS() {
  try {
    console.log('\n=== 测试1：图片上传功能（CORS修复验证） ===');
    
    // 创建测试图片文件
    const testImagePath = path.join(process.cwd(), 'test-final.jpg');
    const existingImagePath = path.join(process.cwd(), '..', 'public', 'articles', 'img', '1.jpg');
    
    if (fs.existsSync(existingImagePath)) {
      fs.copyFileSync(existingImagePath, testImagePath);
    } else {
      console.log('✗ 没有找到测试图片文件');
      return false;
    }

    // 使用FormData模拟前端上传，包含正确的Origin头
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));

    const response = await fetch(`${BASE_URL}/images/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Origin': 'http://localhost:5175',  // 使用修复后的正确Origin
        ...formData.getHeaders()
      },
      body: formData
    });

    // 清理测试文件
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
    }

    const result = await response.json();
    
    if (result.success) {
      console.log('✓ 图片上传成功（CORS问题已解决）');
      console.log(`  文件名: ${result.data.filename}`);
      console.log(`  URL: ${result.data.url}`);
      
      // 清理上传的测试图片
      try {
        await fetch(`${BASE_URL}/images/${result.data.id}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${authToken}` }
        });
        console.log('✓ 测试图片已清理');
      } catch (error) {
        console.log('⚠ 清理测试图片失败');
      }
      
      return true;
    } else {
      console.log('✗ 图片上传失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('✗ 图片上传异常:', error.message);
    return false;
  }
}

// 测试2：辅助信息前端显示验证
async function testAuxiliaryContentDisplay() {
  try {
    console.log('\n=== 测试2：辅助信息前端显示验证 ===');
    
    // 检查articles.js文件是否包含auxiliary_content
    const articlesPath = '../src/data/articles.js';
    
    if (!fs.existsSync(articlesPath)) {
      console.log('✗ Articles.js文件不存在');
      return false;
    }

    const content = fs.readFileSync(articlesPath, 'utf8');
    const hasAuxiliaryContent = content.includes('auxiliary_content');
    
    if (!hasAuxiliaryContent) {
      console.log('✗ Articles.js不包含auxiliary_content字段');
      return false;
    }

    console.log('✓ Articles.js包含auxiliary_content字段');
    
    // 提取auxiliary_content的值
    const matches = content.match(/auxiliary_content:\s*'([^']*)'|auxiliary_content:\s*"([^"]*)"/g);
    if (matches && matches.length > 0) {
      console.log('✓ 找到auxiliary_content字段:');
      let hasNonEmptyContent = false;
      matches.forEach((match, index) => {
        const value = match.match(/'([^']*)'|"([^"]*)"/)[1] || match.match(/'([^']*)'|"([^"]*)"/)[2];
        console.log(`  ${index + 1}. ${match}`);
        if (value && value.trim() !== '') {
          hasNonEmptyContent = true;
        }
      });
      
      if (hasNonEmptyContent) {
        console.log('✓ 发现包含内容的auxiliary_content字段');
      } else {
        console.log('⚠ 所有auxiliary_content字段都为空（这是正常的，如果数据库中没有辅助内容）');
      }
    }
    
    return true;
  } catch (error) {
    console.log('✗ 辅助信息显示验证失败:', error.message);
    return false;
  }
}

// 测试3：创建包含辅助内容的文章并验证同步
async function testAuxiliaryContentSync() {
  try {
    console.log('\n=== 测试3：创建包含辅助内容的文章并验证同步 ===');
    
    // 创建测试文章
    const testArticle = {
      title: '最终验证测试文章',
      slug: 'final-verification-test',
      description: '用于验证辅助内容同步的测试文章',
      content: '这是测试文章的正文内容。',
      auxiliary_content: '这是辅助介绍内容，用于验证前端显示功能。',
      status: 'published',
      featured: false,
      categories: [1], // 假设分类ID为1
      tags: [1], // 假设标签ID为1
      articleImages: []
    };

    const createResponse = await fetch(`${BASE_URL}/articles`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testArticle)
    });

    const createResult = await createResponse.json();
    
    if (!createResult.success) {
      console.log('✗ 创建测试文章失败:', createResult.message);
      return false;
    }

    const articleId = createResult.data.id;
    console.log('✓ 测试文章创建成功');
    console.log(`  文章ID: ${articleId}`);
    console.log(`  辅助内容: ${testArticle.auxiliary_content}`);

    // 触发数据同步
    const syncResponse = await fetch(`${BASE_URL}/articles/sync`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const syncResult = await syncResponse.json();
    
    if (!syncResult.success) {
      console.log('✗ 数据同步失败:', syncResult.message);
      return false;
    }

    console.log('✓ 数据同步成功');

    // 验证前端文件是否包含新的辅助内容
    const articlesPath = '../src/data/articles.js';
    const content = fs.readFileSync(articlesPath, 'utf8');
    const hasTestContent = content.includes('这是辅助介绍内容，用于验证前端显示功能。');
    
    if (hasTestContent) {
      console.log('✓ 辅助内容已成功同步到前端');
    } else {
      console.log('✗ 辅助内容未同步到前端');
    }

    // 清理测试文章
    try {
      await fetch(`${BASE_URL}/articles/${articleId}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${authToken}` }
      });
      console.log('✓ 测试文章已清理');
    } catch (error) {
      console.log('⚠ 清理测试文章失败');
    }

    return hasTestContent;
  } catch (error) {
    console.log('✗ 辅助内容同步测试失败:', error.message);
    return false;
  }
}

// 主测试函数
async function runFinalVerification() {
  console.log('🎯 开始最终验证测试...\n');
  
  try {
    // 1. 登录
    const loginOk = await login();
    if (!loginOk) {
      console.log('❌ 测试终止：无法登录');
      return;
    }
    
    // 2. 测试图片上传（CORS修复）
    const imageUploadOk = await testImageUploadCORS();
    
    // 3. 测试辅助信息前端显示
    const auxiliaryDisplayOk = await testAuxiliaryContentDisplay();
    
    // 4. 测试辅助内容同步
    const auxiliarySyncOk = await testAuxiliaryContentSync();
    
    // 输出最终结果
    console.log('\n🏆 最终验证结果:');
    console.log(`问题1 - 图片上传功能: ${imageUploadOk ? '✅ 已修复' : '❌ 仍有问题'}`);
    console.log(`问题2a - 辅助信息前端显示: ${auxiliaryDisplayOk ? '✅ 已修复' : '❌ 仍有问题'}`);
    console.log(`问题2b - 辅助信息同步: ${auxiliarySyncOk ? '✅ 已修复' : '❌ 仍有问题'}`);
    
    const allOk = imageUploadOk && auxiliaryDisplayOk && auxiliarySyncOk;
    console.log(`\n🎊 整体修复状态: ${allOk ? '✅ 所有问题已解决' : '❌ 仍有问题需要修复'}`);
    
    if (allOk) {
      console.log('\n🎉 恭喜！博客后端管理系统的所有关键问题都已成功修复：');
      console.log('1. ✅ 图片上传功能完全正常，CORS配置已修复');
      console.log('2. ✅ 辅助内容能够正确保存并在前端显示');
      console.log('3. ✅ 数据同步功能稳定可靠');
      console.log('\n现在您可以正常使用博客管理系统的所有功能了！');
    }
    
  } catch (error) {
    console.log('❌ 最终验证过程中发生错误:', error.message);
  }
}

runFinalVerification().catch(console.error);
