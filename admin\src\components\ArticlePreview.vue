<template>
  <div class="article-preview">
    <div class="preview-header">
      <h2>实时预览</h2>
      <div class="preview-actions">
        <el-button size="small" @click="openInNewTab" type="primary">
          <el-icon><View /></el-icon>
          新窗口预览
        </el-button>
        <el-button size="small" @click="refreshPreview">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>
    
    <div class="preview-content">
      <iframe
        ref="previewFrame"
        :src="previewUrl"
        frameborder="0"
        class="preview-iframe"
        @load="onFrameLoad"
      ></iframe>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { View, Refresh } from '@element-plus/icons-vue'

const props = defineProps({
  article: {
    type: Object,
    required: true
  }
})

const previewFrame = ref()
const previewUrl = computed(() => {
  // 这里应该是前端博客的预览URL
  // 实际项目中可能需要创建一个专门的预览端点
  return `http://localhost:5173/article/${props.article.slug || 'preview'}`
})

// 监听文章变化，实时更新预览
watch(() => props.article, (newArticle) => {
  updatePreview(newArticle)
}, { deep: true })

// 更新预览内容
const updatePreview = (article) => {
  if (!previewFrame.value) return
  
  try {
    const frame = previewFrame.value
    const frameDoc = frame.contentDocument || frame.contentWindow.document
    
    if (frameDoc) {
      // 通过postMessage与iframe通信
      frame.contentWindow.postMessage({
        type: 'UPDATE_PREVIEW',
        article: article
      }, '*')
    }
  } catch (error) {
    console.warn('无法更新预览内容:', error)
  }
}

// iframe加载完成
const onFrameLoad = () => {
  updatePreview(props.article)
}

// 在新窗口中打开预览
const openInNewTab = () => {
  window.open(previewUrl.value, '_blank')
}

// 刷新预览
const refreshPreview = () => {
  if (previewFrame.value) {
    previewFrame.value.src = previewFrame.value.src
  }
}

// 监听来自iframe的消息
const handleMessage = (event) => {
  if (event.data.type === 'PREVIEW_READY') {
    updatePreview(props.article)
  }
}

onMounted(() => {
  window.addEventListener('message', handleMessage)
})

onUnmounted(() => {
  window.removeEventListener('message', handleMessage)
})
</script>

<style scoped>
.article-preview {
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
}

.preview-header h2 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-content {
  flex: 1;
  position: relative;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
}
</style>
