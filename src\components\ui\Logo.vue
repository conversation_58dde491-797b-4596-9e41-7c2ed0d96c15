<!-- /src/components/ui/Logo.vue -->
<template>
  <div :class="$style.logoContainer" ref="logoContainerRef">
    <img 
      src="@/assets/images/logo.svg" 
      alt="黑猫船长 Z Logo" 
      :class="$style.logoImage"
      ref="logoImageRef"
      :style="{ height: props.size + 'px', width: 'auto' }"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';

const props = defineProps({
  size: {
    type: Number,
    default: 150
  }
});

const logoContainerRef = ref(null);
const logoImageRef = ref(null);

const handleMouseMove = (event) => {
  const rect = logoContainerRef.value.getBoundingClientRect();
  const centerX = rect.width / 2;
  const centerY = rect.height / 2;
  const mouseX = event.clientX - rect.left;
  const mouseY = event.clientY - rect.top;
  
  const rotateY = ((mouseX - centerX) / centerX) * 15;
  const rotateX = ((centerY - mouseY) / centerY) * 15;
  
  logoImageRef.value.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
  logoImageRef.value.style.transition = 'transform 0.1s ease';
};

const handleMouseLeave = () => {
  logoImageRef.value.style.transform = 'perspective(1000px) rotateX(0) rotateY(0)';
  logoImageRef.value.style.transition = 'transform 0.3s ease';
};

onMounted(() => {
  if (logoContainerRef.value && logoImageRef.value) {
    logoContainerRef.value.addEventListener('mousemove', handleMouseMove);
    logoContainerRef.value.addEventListener('mouseleave', handleMouseLeave);
  }
});

onBeforeUnmount(() => {
  if (logoContainerRef.value) {
    logoContainerRef.value.removeEventListener('mousemove', handleMouseMove);
    logoContainerRef.value.removeEventListener('mouseleave', handleMouseLeave);
  }
});
</script>

<style module>
.logoContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.logoImage {
  transition: transform 0.3s ease;
}
</style> 