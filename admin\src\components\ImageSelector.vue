<template>
  <el-dialog
    v-model="visible"
    title="选择图片"
    width="80%"
    :before-close="handleClose"
  >
    <div class="image-selector">
      <!-- 上传区域 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
          :show-file-list="false"
          multiple
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 jpg/png/gif 文件，且不超过 10MB
            </div>
          </template>
        </el-upload>
      </div>

      <!-- 搜索和筛选 -->
      <div class="filter-section">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-input
              v-model="searchQuery"
              placeholder="搜索图片..."
              :prefix-icon="Search"
              @input="handleSearch"
            />
          </el-col>
          <el-col :span="6">
            <el-select v-model="sortBy" placeholder="排序方式" @change="loadImages">
              <el-option label="最新上传" value="created_at" />
              <el-option label="文件名" value="filename" />
              <el-option label="文件大小" value="size" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="loadImages" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 图片网格 -->
      <div class="images-grid" v-loading="loading">
        <div
          v-for="image in filteredImages"
          :key="image.id"
          class="image-item"
          :class="{ selected: selectedImages.includes(image.id) }"
          @click="toggleImageSelection(image)"
        >
          <div class="image-wrapper">
            <img :src="getImageUrl(image)" :alt="image.filename" />
            <div class="image-overlay">
              <div class="image-actions">
                <el-button
                  type="primary"
                  size="small"
                  circle
                  @click.stop="previewImage(image)"
                >
                  <el-icon><ZoomIn /></el-icon>
                </el-button>
                <el-button
                  type="success"
                  size="small"
                  circle
                  @click.stop="copyImageUrl(image)"
                >
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
          <div class="image-info">
            <div class="image-name">{{ image.filename }}</div>
            <div class="image-meta">
              {{ formatFileSize(image.size) }} • {{ formatDate(image.created_at) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[12, 24, 48, 96]"
          :total="totalImages"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadImages"
          @current-change="loadImages"
        />
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="selectedImages.length === 0"
        >
          确定选择 ({{ selectedImages.length }})
        </el-button>
      </span>
    </template>

    <!-- 图片预览 -->
    <el-image-viewer
      v-if="previewVisible"
      :url-list="[previewImageUrl]"
      @close="previewVisible = false"
    />
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  UploadFilled, 
  Search, 
  Refresh, 
  ZoomIn, 
  CopyDocument 
} from '@element-plus/icons-vue'
import api from '../api'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  multiple: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'select'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const images = ref([])
const selectedImages = ref([])
const searchQuery = ref('')
const sortBy = ref('created_at')
const currentPage = ref(1)
const pageSize = ref(24)
const totalImages = ref(0)

const previewVisible = ref(false)
const previewImageUrl = ref('')

// 上传配置
const uploadUrl = `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'}/api/images/upload`
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${localStorage.getItem('admin_token')}`
}))

// 计算属性
const filteredImages = computed(() => {
  if (!searchQuery.value) return images.value
  
  const query = searchQuery.value.toLowerCase()
  return images.value.filter(image => 
    image.filename.toLowerCase().includes(query) ||
    image.alt_text?.toLowerCase().includes(query)
  )
})

// 工具函数
const getImageUrl = (image) => {
  const baseUrl = `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'}`
  return `${baseUrl}${image.url}`
}

// 方法
const loadImages = async () => {
  loading.value = true
  try {
    const response = await api.get('/images', {
      params: {
        page: currentPage.value,
        limit: pageSize.value,
        sort: sortBy.value,
        search: searchQuery.value
      }
    })
    
    images.value = response.data.data.images
    totalImages.value = response.data.data.total
  } catch (error) {
    console.error('加载图片失败:', error)
    ElMessage.error('加载图片失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadImages()
}

const toggleImageSelection = (image) => {
  const index = selectedImages.value.indexOf(image.id)
  
  if (props.multiple) {
    if (index > -1) {
      selectedImages.value.splice(index, 1)
    } else {
      selectedImages.value.push(image.id)
    }
  } else {
    selectedImages.value = index > -1 ? [] : [image.id]
  }
}

const previewImage = (image) => {
  previewImageUrl.value = image.url
  previewVisible.value = true
}

const copyImageUrl = async (image) => {
  try {
    await navigator.clipboard.writeText(image.url)
    ElMessage.success('图片链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }
  return true
}

const handleUploadSuccess = (response) => {
  if (response.success) {
    ElMessage.success('图片上传成功')
    loadImages() // 重新加载图片列表
  } else {
    ElMessage.error(response.message || '上传失败')
  }
}

const handleUploadError = (error) => {
  console.error('上传失败:', error)
  ElMessage.error('图片上传失败')
}

const handleConfirm = () => {
  const selectedImageObjects = images.value.filter(img => 
    selectedImages.value.includes(img.id)
  )
  
  emit('select', props.multiple ? selectedImageObjects : selectedImageObjects[0])
  handleClose()
}

const handleClose = () => {
  selectedImages.value = []
  visible.value = false
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 监听器
watch(visible, (newVal) => {
  if (newVal) {
    loadImages()
  }
})

onMounted(() => {
  if (visible.value) {
    loadImages()
  }
})
</script>

<style scoped>
.image-selector {
  max-height: 70vh;
  overflow-y: auto;
}

.upload-section {
  margin-bottom: 20px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
  min-height: 300px;
}

.image-item {
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.image-item:hover {
  border-color: #409eff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.image-item.selected {
  border-color: #67c23a;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 150px;
  overflow: hidden;
}

.image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-item:hover .image-overlay {
  opacity: 1;
}

.image-actions {
  display: flex;
  gap: 10px;
}

.image-info {
  padding: 10px;
  background: white;
}

.image-name {
  font-weight: 500;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.image-meta {
  font-size: 12px;
  color: #909399;
}

.pagination-section {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
