// /src/utils/markdown.js
import MarkdownIt from 'markdown-it';

// 创建 MarkdownIt 实例
const md = new MarkdownIt({
  html: true,       // 启用 HTML 标签
  breaks: true,     // 将换行符转换为 <br>
  linkify: true,    // 自动将 URL 转换为链接
  typographer: true, // 启用一些语言中性的替换和引号
  highlight: function (str, lang) {
    // 为代码块添加语言类，以便应用样式
    return `<pre class="language-${lang || 'text'}"><code>${md.utils.escapeHtml(str)}</code></pre>`;
  }
});

// 自定义水平分隔线渲染
const defaultHrRender = md.renderer.rules.hr || function(tokens, idx, options, _, self) {
  return self.renderToken(tokens, idx, options);
};

md.renderer.rules.hr = function(tokens, idx, options, _, self) {
  // 使用默认渲染器生成基本的 <hr> 标签
  return defaultHrRender(tokens, idx, options, _, self);
};

// 自定义表格渲染，添加响应式支持
const defaultTableRender = md.renderer.rules.table_open || function(tokens, idx, options, _, self) {
  return self.renderToken(tokens, idx, options);
};

md.renderer.rules.table_open = function(tokens, idx, options, env, self) {
  // 添加一个包装器div，使表格可以在移动设备上水平滚动
  return '<div class="table-responsive">' + defaultTableRender(tokens, idx, options, env, self);
};

const defaultTableCloseRender = md.renderer.rules.table_close || function(tokens, idx, options, _, self) {
  return self.renderToken(tokens, idx, options);
};

md.renderer.rules.table_close = function(tokens, idx, options, env, self) {
  // 关闭包装器div
  return defaultTableCloseRender(tokens, idx, options, env, self) + '</div>';
};

/**
 * 将 Markdown 文本转换为 HTML
 * @param {string} text - Markdown 格式的文本
 * @return {string} 转换后的 HTML
 */
export function renderMarkdown(text) {
  if (!text) return '';
  return md.render(text);
}

/**
 * 格式化献词，将\n转换为<br>
 * @param {string} dedication - 献词文本
 * @return {string} 格式化后的 HTML
 */
export function formatDedication(dedication) {
  if (!dedication) return '';
  return dedication.replace(/\n/g, '<br>');
}
