import fetch from 'node-fetch';
import fs from 'fs/promises';
import path from 'path';

const BASE_URL = 'http://localhost:3001/api';
let authToken = '';

// 测试完整功能流程
async function testCompleteFeatures() {
  console.log('🚀 开始完整功能测试...\n');

  try {
    // 1. 登录获取token
    console.log('1. 管理员登录');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123456'
      })
    });

    const loginResult = await loginResponse.json();
    if (!loginResult.success) {
      throw new Error('登录失败');
    }

    authToken = loginResult.data.token;
    console.log('✓ 登录成功');

    // 2. 测试图片上传功能
    console.log('\n2. 测试图片上传功能');
    await testImageUpload();

    // 3. 测试图片管理功能
    console.log('\n3. 测试图片管理功能');
    await testImageManagement();

    // 4. 测试文章创建和图片插入
    console.log('\n4. 测试文章创建和图片插入');
    const articleId = await testArticleWithImages();

    // 5. 测试数据同步功能
    console.log('\n5. 测试数据同步功能');
    await testDataSync(articleId);

    // 6. 测试文章发布和同步
    console.log('\n6. 测试文章发布和同步');
    await testPublishAndSync(articleId);

    // 7. 验证前端文件生成
    console.log('\n7. 验证前端文件生成');
    await verifyFrontendFiles();

    // 8. 清理测试数据
    console.log('\n8. 清理测试数据');
    await cleanupTestData(articleId);

    console.log('\n🎉 所有功能测试完成！');

  } catch (error) {
    console.error('❌ 功能测试失败:', error.message);
  }
}

// 测试图片上传
async function testImageUpload() {
  try {
    // 创建一个测试图片文件（模拟）
    const testImageData = Buffer.from('fake-image-data');
    
    // 由于无法真正上传文件，我们测试获取图片列表
    const response = await fetch(`${BASE_URL}/images`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    const result = await response.json();
    if (result.success) {
      console.log(`✓ 图片列表获取成功，共 ${result.data.images?.length || 0} 张图片`);
    } else {
      console.log('✗ 图片列表获取失败');
    }
  } catch (error) {
    console.log('⚠ 图片上传测试跳过:', error.message);
  }
}

// 测试图片管理功能
async function testImageManagement() {
  try {
    // 测试图片列表分页
    const response = await fetch(`${BASE_URL}/images?page=1&limit=10&sort=created_at`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    const result = await response.json();
    if (result.success) {
      console.log('✓ 图片分页查询成功');
      
      // 测试搜索功能
      const searchResponse = await fetch(`${BASE_URL}/images?search=test`, {
        headers: { 'Authorization': `Bearer ${authToken}` }
      });
      
      const searchResult = await searchResponse.json();
      if (searchResult.success) {
        console.log('✓ 图片搜索功能正常');
      }
    } else {
      console.log('✗ 图片管理功能测试失败');
    }
  } catch (error) {
    console.log('⚠ 图片管理测试失败:', error.message);
  }
}

// 测试文章创建和图片插入
async function testArticleWithImages() {
  try {
    const testArticle = {
      title: '功能测试文章',
      slug: 'feature-test-' + Date.now(),
      description: '这是一篇用于测试图片插入功能的文章',
      content: `# 功能测试文章

这是一篇测试文章，用于验证以下功能：

## 图片插入功能

![测试图片](http://localhost:3001/uploads/images/test.jpg)

## Markdown编辑器功能

- **粗体文本**
- *斜体文本*
- \`代码片段\`

\`\`\`javascript
console.log('Hello World');
\`\`\`

## 列表功能

1. 有序列表项1
2. 有序列表项2
3. 有序列表项3

- 无序列表项1
- 无序列表项2
- 无序列表项3

> 这是一个引用块

## 链接功能

[测试链接](https://example.com)

## 总结

这篇文章测试了编辑器的各种功能。`,
      status: 'draft',
      featured: false,
      categories: [],
      tags: []
    };

    const response = await fetch(`${BASE_URL}/articles`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(testArticle)
    });

    const result = await response.json();
    if (result.success) {
      console.log('✓ 文章创建成功，ID:', result.data.id);
      return result.data.id;
    } else {
      throw new Error('文章创建失败');
    }
  } catch (error) {
    console.log('✗ 文章创建测试失败:', error.message);
    return null;
  }
}

// 测试数据同步功能
async function testDataSync(articleId) {
  if (!articleId) return;

  try {
    // 测试手动同步
    const response = await fetch(`${BASE_URL}/articles/sync`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    const result = await response.json();
    if (result.success) {
      console.log('✓ 手动同步成功:', result.message);
    } else {
      console.log('✗ 手动同步失败:', result.message);
    }
  } catch (error) {
    console.log('⚠ 数据同步测试失败:', error.message);
  }
}

// 测试文章发布和自动同步
async function testPublishAndSync(articleId) {
  if (!articleId) return;

  try {
    // 发布文章
    const response = await fetch(`${BASE_URL}/articles/${articleId}/publish`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    const result = await response.json();
    if (result.success) {
      console.log('✓ 文章发布成功，应该已触发自动同步');
      
      // 等待同步完成
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 测试取消发布
      const unpublishResponse = await fetch(`${BASE_URL}/articles/${articleId}/unpublish`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${authToken}` }
      });

      const unpublishResult = await unpublishResponse.json();
      if (unpublishResult.success) {
        console.log('✓ 文章取消发布成功');
      }
    } else {
      console.log('✗ 文章发布失败');
    }
  } catch (error) {
    console.log('⚠ 发布测试失败:', error.message);
  }
}

// 验证前端文件生成
async function verifyFrontendFiles() {
  try {
    // 检查articles.js文件
    const articlesPath = path.resolve('../src/data/articles.js');
    const articlesContent = await fs.readFile(articlesPath, 'utf8');
    
    if (articlesContent.includes('// 此文件由后端管理系统自动生成')) {
      console.log('✓ articles.js文件已正确生成');
    } else {
      console.log('⚠ articles.js文件格式可能不正确');
    }

    // 检查public/articles目录
    const articlesDir = path.resolve('../public/articles');
    const files = await fs.readdir(articlesDir);
    const mdFiles = files.filter(file => file.endsWith('.md'));
    
    console.log(`✓ 发现 ${mdFiles.length} 个Markdown文件`);
    
  } catch (error) {
    console.log('⚠ 前端文件验证失败:', error.message);
  }
}

// 清理测试数据
async function cleanupTestData(articleId) {
  if (!articleId) return;

  try {
    const response = await fetch(`${BASE_URL}/articles/${articleId}`, {
      method: 'DELETE',
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    const result = await response.json();
    if (result.success) {
      console.log('✓ 测试文章已删除');
    }
  } catch (error) {
    console.log('⚠ 清理测试数据失败:', error.message);
  }
}

// 运行测试
testCompleteFeatures();
