import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3001/api';
let authToken = '';

// 测试函数
async function testAPI(endpoint, method = 'GET', data = null, requireAuth = false) {
  try {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (requireAuth && authToken) {
      options.headers.Authorization = `Bearer ${authToken}`;
    }

    if (data) {
      options.body = JSON.stringify(data);
    }

    const response = await fetch(`${BASE_URL}${endpoint}`, options);
    const result = await response.json();
    
    console.log(`✓ ${method} ${endpoint}:`, response.status, result.message || 'OK');
    return result;
  } catch (error) {
    console.error(`✗ ${method} ${endpoint}:`, error.message);
    return null;
  }
}

// 系统测试
async function runSystemTests() {
  console.log('🚀 开始系统测试...\n');

  // 1. 测试健康检查
  console.log('1. 测试健康检查');
  await testAPI('/health');

  // 2. 测试登录
  console.log('\n2. 测试管理员登录');
  const loginResult = await testAPI('/auth/login', 'POST', {
    username: 'admin',
    password: 'admin123456'
  });

  if (loginResult && loginResult.success) {
    authToken = loginResult.data.token;
    console.log('✓ 登录成功，获取到token');
  } else {
    console.error('✗ 登录失败');
    return;
  }

  // 3. 测试获取文章列表
  console.log('\n3. 测试获取文章列表');
  await testAPI('/articles', 'GET', null, true);

  // 4. 测试创建文章
  console.log('\n4. 测试创建文章');
  const newArticle = {
    title: '测试文章',
    slug: 'test-article-' + Date.now(),
    description: '这是一篇测试文章',
    content: '# 测试文章\n\n这是测试内容。',
    status: 'draft',
    featured: false,
    categories: [],
    tags: []
  };
  
  const createResult = await testAPI('/articles', 'POST', newArticle, true);
  let articleId = null;
  
  if (createResult && createResult.success) {
    articleId = createResult.data.id;
    console.log('✓ 文章创建成功，ID:', articleId);
  }

  // 5. 测试获取单篇文章
  if (articleId) {
    console.log('\n5. 测试获取单篇文章');
    await testAPI(`/articles/${articleId}`, 'GET', null, true);
  }

  // 6. 测试更新文章
  if (articleId) {
    console.log('\n6. 测试更新文章');
    await testAPI(`/articles/${articleId}`, 'PUT', {
      ...newArticle,
      title: '更新后的测试文章',
      content: '# 更新后的测试文章\n\n这是更新后的内容。'
    }, true);
  }

  // 7. 测试发布文章
  if (articleId) {
    console.log('\n7. 测试发布文章');
    await testAPI(`/articles/${articleId}/publish`, 'POST', null, true);
  }

  // 8. 测试取消发布
  if (articleId) {
    console.log('\n8. 测试取消发布');
    await testAPI(`/articles/${articleId}/unpublish`, 'POST', null, true);
  }

  // 9. 测试获取分类列表
  console.log('\n9. 测试获取分类列表');
  await testAPI('/categories', 'GET', null, true);

  // 10. 测试获取标签列表
  console.log('\n10. 测试获取标签列表');
  await testAPI('/tags', 'GET', null, true);

  // 11. 测试同步功能
  console.log('\n11. 测试同步功能');
  await testAPI('/articles/sync', 'POST', null, true);

  console.log('\n🎉 系统测试完成！');
}

// 运行测试
runSystemTests().catch(console.error);
