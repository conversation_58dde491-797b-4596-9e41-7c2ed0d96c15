<!-- /src/components/ui/BackToTop.vue -->
<template>
  <Transition name="fade">
    <button
      v-show="showButton"
      :class="$style.backToTop"
      @click="scrollToTop"
      aria-label="回到顶部"
    >
      <svg
        :class="$style.icon"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <path d="M18 15l-6-6-6 6" />
      </svg>
    </button>
  </Transition>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

const showButton = ref(false);
const scrollThreshold = 300; // 显示按钮的滚动阈值

const checkScroll = () => {
  showButton.value = window.pageYOffset > scrollThreshold;
};

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
};

onMounted(() => {
  window.addEventListener('scroll', checkScroll);
});

onUnmounted(() => {
  window.removeEventListener('scroll', checkScroll);
});
</script>

<style module>
.backToTop {
  position: fixed;
  bottom: var(--space-xl);
  right: var(--space-xl);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(30, 32, 44, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--color-text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
  z-index: 1000;
}

.backToTop:hover {
  background-color: rgba(30, 32, 44, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.icon {
  width: 20px;
  height: 20px;
  transition: transform 0.3s ease;
}

.backToTop:hover .icon {
  transform: translateY(-2px);
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .backToTop {
    bottom: var(--space-l);
    right: var(--space-l);
    width: 36px;
    height: 36px;
  }

  .icon {
    width: 18px;
    height: 18px;
  }
}
</style>