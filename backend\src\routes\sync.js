import express from 'express';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// 获取同步状态
router.get('/status', authenticateToken, (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'ready',
      lastSync: new Date().toISOString(),
      message: '同步功能准备就绪'
    }
  });
});

// 生成静态文件
router.post('/generate-static', authenticateToken, requireAdmin, (req, res) => {
  // TODO: 实现静态文件生成逻辑
  res.json({
    success: true,
    message: '静态文件生成功能开发中'
  });
});

export default router;
