<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一键修复图片上传</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .status {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        .big-button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 18px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px;
            min-width: 200px;
        }
        .big-button:hover {
            background-color: #218838;
        }
        .big-button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        
        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        
        .log {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            text-align: left;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 一键修复图片上传功能</h1>
        
        <div id="status" class="status info">
            准备修复图片上传问题...
        </div>
        
        <div class="step">
            <h3>🎯 修复步骤</h3>
            <p>1. 自动登录获取有效Token</p>
            <p>2. 设置正确的admin_token</p>
            <p>3. 清除错误的token</p>
            <p>4. 测试图片上传功能</p>
        </div>
        
        <button id="fixButton" class="big-button" onclick="autoFix()">
            🚀 一键修复
        </button>
        
        <div style="margin: 20px 0;">
            <button class="test-button" onclick="checkCurrentStatus()">检查当前状态</button>
            <button class="test-button" onclick="testUploadNow()">测试上传</button>
            <button class="test-button" onclick="clearAllData()">清除所有数据</button>
        </div>
        
        <div id="log" class="log"></div>
        
        <div class="step">
            <h3>💡 修复完成后</h3>
            <p>1. 返回图片管理页面：<a href="/images" target="_blank">http://localhost:5175/images</a></p>
            <p>2. 尝试上传图片</p>
            <p>3. 如果仍有问题，请刷新页面后重试</p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        async function autoFix() {
            const button = document.getElementById('fixButton');
            button.disabled = true;
            button.textContent = '🔄 修复中...';
            
            try {
                updateStatus('开始自动修复...', 'info');
                log('开始自动修复图片上传功能');
                
                // 步骤1：登录获取Token
                updateStatus('步骤1：登录获取Token...', 'info');
                const token = await loginAndGetToken();
                
                if (!token) {
                    updateStatus('修复失败：无法获取Token', 'error');
                    return;
                }
                
                // 步骤2：设置正确的Token
                updateStatus('步骤2：设置正确的Token...', 'info');
                localStorage.setItem('admin_token', token);
                localStorage.removeItem('token'); // 清除错误的token
                log('✅ Token设置完成');
                
                // 步骤3：测试上传功能
                updateStatus('步骤3：测试上传功能...', 'info');
                const uploadSuccess = await testImageUpload(token);
                
                if (uploadSuccess) {
                    updateStatus('🎉 修复成功！图片上传功能已恢复正常', 'success');
                    log('🎉 修复完成，现在可以正常上传图片了');
                } else {
                    updateStatus('修复部分成功，但上传测试失败', 'warning');
                    log('⚠️ Token已设置，但上传测试失败，请手动测试');
                }
                
            } catch (error) {
                updateStatus(`修复失败：${error.message}`, 'error');
                log(`❌ 修复过程中发生错误：${error.message}`);
            } finally {
                button.disabled = false;
                button.textContent = '🚀 一键修复';
            }
        }
        
        async function loginAndGetToken() {
            try {
                log('正在登录...');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123456'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    log(`✅ 登录成功，用户：${result.data.user.username}`);
                    return result.data.token;
                } else {
                    log(`❌ 登录失败：${result.message}`);
                    return null;
                }
            } catch (error) {
                log(`❌ 登录请求失败：${error.message}`);
                return null;
            }
        }
        
        async function testImageUpload(token) {
            try {
                log('测试图片上传...');
                
                // 创建测试图片
                const canvas = document.createElement('canvas');
                canvas.width = 1;
                canvas.height = 1;
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = '#00ff00';
                ctx.fillRect(0, 0, 1, 1);
                
                return new Promise((resolve) => {
                    canvas.toBlob(async (blob) => {
                        const formData = new FormData();
                        formData.append('image', blob, 'test-fix.png');

                        const response = await fetch(`${API_BASE}/images/upload`, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${token}`
                            },
                            body: formData
                        });

                        const result = await response.json();
                        
                        if (result.success) {
                            log(`✅ 上传测试成功：${result.data.filename}`);
                            
                            // 清理测试图片
                            try {
                                await fetch(`${API_BASE}/images/${result.data.id}`, {
                                    method: 'DELETE',
                                    headers: { 'Authorization': `Bearer ${token}` }
                                });
                                log('✅ 测试图片已清理');
                            } catch (error) {
                                log('⚠️ 清理测试图片失败');
                            }
                            
                            resolve(true);
                        } else {
                            log(`❌ 上传测试失败：${result.message}`);
                            resolve(false);
                        }
                    }, 'image/png');
                });
                
            } catch (error) {
                log(`❌ 上传测试异常：${error.message}`);
                return false;
            }
        }
        
        function checkCurrentStatus() {
            log('检查当前状态...');
            
            const token = localStorage.getItem('token');
            const adminToken = localStorage.getItem('admin_token');
            
            log(`token: ${token ? '存在' : '不存在'}`);
            log(`admin_token: ${adminToken ? '存在' : '不存在'}`);
            
            if (adminToken) {
                try {
                    const payload = JSON.parse(atob(adminToken.split('.')[1]));
                    const exp = new Date(payload.exp * 1000);
                    const now = new Date();
                    
                    log(`admin_token状态: ${exp < now ? '已过期' : '有效'}，过期时间: ${exp.toLocaleString()}`);
                    
                    if (exp < now) {
                        updateStatus('Token已过期，需要重新修复', 'warning');
                    } else {
                        updateStatus('Token有效，可以尝试上传', 'success');
                    }
                } catch (error) {
                    log(`admin_token解析失败: ${error.message}`);
                    updateStatus('Token格式无效，需要重新修复', 'error');
                }
            } else {
                updateStatus('没有有效Token，需要修复', 'error');
            }
        }
        
        async function testUploadNow() {
            const adminToken = localStorage.getItem('admin_token');
            
            if (!adminToken) {
                updateStatus('没有admin_token，请先点击一键修复', 'error');
                return;
            }
            
            updateStatus('正在测试上传...', 'info');
            const success = await testImageUpload(adminToken);
            
            if (success) {
                updateStatus('上传测试成功！', 'success');
            } else {
                updateStatus('上传测试失败，请重新修复', 'error');
            }
        }
        
        function clearAllData() {
            localStorage.removeItem('token');
            localStorage.removeItem('admin_token');
            log('所有Token已清除');
            updateStatus('所有数据已清除，请重新修复', 'warning');
        }
        
        // 页面加载时检查状态
        window.onload = function() {
            log('一键修复工具已加载');
            checkCurrentStatus();
        };
    </script>
</body>
</html>
