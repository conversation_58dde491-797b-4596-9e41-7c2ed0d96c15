{"name": "moyin-blog-backend", "version": "1.0.0", "description": "Backend API for Moyin Blog Management System", "main": "src/app.js", "type": "module", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["blog", "cms", "api", "express"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"bcryptjs": "^3.0.2", "better-sqlite3": "^12.2.0", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^2.0.2", "node-fetch": "^3.3.2", "sharp": "^0.34.3", "socket.io": "^4.8.1"}, "devDependencies": {"nodemon": "^3.1.10"}}