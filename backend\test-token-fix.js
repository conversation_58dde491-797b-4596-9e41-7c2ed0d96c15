import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';

const BASE_URL = 'http://localhost:3001/api';

// 测试用的管理员凭据
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123456'
};

// 模拟前端的Token存储问题测试
async function testTokenKeyIssue() {
  try {
    console.log('🔍 测试Token Key不匹配问题...\n');
    
    // 1. 登录获取Token
    console.log('=== 步骤1：登录获取Token ===');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:5175'
      },
      body: JSON.stringify(ADMIN_CREDENTIALS)
    });

    const loginResult = await loginResponse.json();
    
    if (!loginResult.success) {
      console.log('✗ 登录失败:', loginResult.message);
      return false;
    }

    const token = loginResult.data.token;
    console.log('✓ 登录成功，获取到Token');
    console.log(`  Token: ${token.substring(0, 50)}...`);

    // 2. 测试使用错误的Token Key（模拟修复前的情况）
    console.log('\n=== 步骤2：模拟错误的Token Key ===');
    await testImageUploadWithToken(null, '使用空Token（模拟localStorage.getItem("token")返回null）');

    // 3. 测试使用正确的Token Key（模拟修复后的情况）
    console.log('\n=== 步骤3：模拟正确的Token Key ===');
    const uploadSuccess = await testImageUploadWithToken(token, '使用正确Token（localStorage.getItem("admin_token")）');

    return uploadSuccess;
    
  } catch (error) {
    console.log('✗ 测试过程中发生错误:', error.message);
    return false;
  }
}

async function testImageUploadWithToken(token, description) {
  try {
    console.log(`测试场景: ${description}`);
    
    // 创建测试图片文件
    const testImagePath = path.join(process.cwd(), 'test-token-fix.jpg');
    const existingImagePath = path.join(process.cwd(), '..', 'public', 'articles', 'img', '1.jpg');
    
    if (fs.existsSync(existingImagePath)) {
      fs.copyFileSync(existingImagePath, testImagePath);
    } else {
      console.log('✗ 没有找到测试图片文件');
      return false;
    }

    // 使用FormData模拟前端上传
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));

    const headers = {
      'Origin': 'http://localhost:5175',
      ...formData.getHeaders()
    };

    // 只有当token存在时才添加Authorization头
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    console.log(`  发送请求，Authorization头: ${token ? `Bearer ${token.substring(0, 20)}...` : '无'}`);

    const response = await fetch(`${BASE_URL}/images/upload`, {
      method: 'POST',
      headers: headers,
      body: formData
    });

    console.log(`  响应状态码: ${response.status}`);
    
    // 清理测试文件
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
    }

    const result = await response.json();
    console.log(`  响应消息: ${result.message}`);
    
    if (result.success) {
      console.log('  ✓ 图片上传成功');
      console.log(`    文件名: ${result.data.filename}`);
      
      // 清理上传的测试图片
      try {
        await fetch(`${BASE_URL}/images/${result.data.id}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log('  ✓ 测试图片已清理');
      } catch (error) {
        console.log('  ⚠ 清理测试图片失败');
      }
      
      return true;
    } else {
      console.log('  ✗ 图片上传失败');
      return false;
    }
  } catch (error) {
    console.log(`  ✗ 上传测试异常: ${error.message}`);
    return false;
  }
}

// 主测试函数
async function runTokenFixTest() {
  console.log('🔧 开始Token Key修复验证测试...\n');
  
  const success = await testTokenKeyIssue();
  
  console.log('\n🎯 Token Key修复测试结果:');
  console.log(`修复验证: ${success ? '✅ 成功' : '❌ 失败'}`);
  
  if (success) {
    console.log('\n🎉 Token Key问题已修复！');
    console.log('修复详情:');
    console.log('- 将ImageSelector组件中的localStorage.getItem("token")');
    console.log('- 修改为localStorage.getItem("admin_token")');
    console.log('- 现在与认证系统使用相同的Token Key');
    console.log('\n💡 用户操作建议:');
    console.log('1. 刷新浏览器页面以加载修复后的代码');
    console.log('2. 如果仍有问题，请重新登录以确保Token有效');
    console.log('3. 现在图片上传功能应该正常工作');
  } else {
    console.log('\n❌ 修复验证失败，可能需要进一步检查');
  }
}

runTokenFixTest().catch(console.error);
