import express from 'express';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';
import db from '../config/database.js';

const router = express.Router();

// 获取所有标签
router.get('/', (req, res, next) => {
  try {
    const tags = db.prepare('SELECT * FROM tags ORDER BY name').all();
    
    res.json({
      success: true,
      data: tags
    });
  } catch (error) {
    next(error);
  }
});

// 创建标签
router.post('/', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const { name, slug } = req.body;

    if (!name || !slug) {
      return res.status(400).json({
        success: false,
        message: '标签名称和 slug 不能为空'
      });
    }

    const insertTag = db.prepare('INSERT INTO tags (name, slug) VALUES (?, ?)');
    const result = insertTag.run(name, slug);

    const newTag = db.prepare('SELECT * FROM tags WHERE id = ?').get(result.lastInsertRowid);

    res.status(201).json({
      success: true,
      message: '标签创建成功',
      data: newTag
    });
  } catch (error) {
    next(error);
  }
});

// 更新标签
router.put('/:id', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, slug } = req.body;

    if (!name || !slug) {
      return res.status(400).json({
        success: false,
        message: '标签名称和 slug 不能为空'
      });
    }

    const existingTag = db.prepare('SELECT * FROM tags WHERE id = ?').get(id);
    if (!existingTag) {
      return res.status(404).json({
        success: false,
        message: '标签不存在'
      });
    }

    // 检查slug是否已被其他标签使用
    if (slug !== existingTag.slug) {
      const slugExists = db.prepare('SELECT id FROM tags WHERE slug = ? AND id != ?').get(slug, id);
      if (slugExists) {
        return res.status(409).json({
          success: false,
          message: 'Slug 已存在'
        });
      }
    }

    const updateTag = db.prepare('UPDATE tags SET name = ?, slug = ? WHERE id = ?');
    updateTag.run(name, slug, id);

    const updatedTag = db.prepare('SELECT * FROM tags WHERE id = ?').get(id);

    res.json({
      success: true,
      message: '标签更新成功',
      data: updatedTag
    });
  } catch (error) {
    next(error);
  }
});

// 删除标签
router.delete('/:id', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const { id } = req.params;

    const tag = db.prepare('SELECT * FROM tags WHERE id = ?').get(id);

    if (!tag) {
      return res.status(404).json({
        success: false,
        message: '标签不存在'
      });
    }

    // 检查是否有文章使用此标签
    const articlesCount = db.prepare('SELECT COUNT(*) as count FROM article_tags WHERE tag_id = ?').get(id).count;

    if (articlesCount > 0) {
      return res.status(409).json({
        success: false,
        message: `无法删除标签，还有 ${articlesCount} 篇文章使用此标签`
      });
    }

    db.prepare('DELETE FROM tags WHERE id = ?').run(id);

    res.json({
      success: true,
      message: '标签删除成功'
    });
  } catch (error) {
    next(error);
  }
});

export default router;
