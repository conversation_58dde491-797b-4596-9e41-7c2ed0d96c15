// /src/router/index.js
import { createRouter, createWebHashHistory } from 'vue-router'
// 导入视图组件 (可以直接导入，或者使用下面的懒加载方式)
import HomeView from '@/views/HomeView.vue'

const routes = [
  {
    path: '/',
    name: 'home',
    component: HomeView, // 直接加载首页
    meta: { transition: 'fade' } // 首页默认淡入淡出
  },
  {
    path: '/article/:slug', // 文章详情页路由示例
    name: 'article',
    // 路由懒加载：只有访问该路由时才会加载对应组件的代码
    component: () => import('@/views/ArticleView.vue'),
    meta: { transition: 'slide-left' } // 文章页左滑进入
  },
  {
    path: '/about',
    name: 'about',
    component: () => import('@/views/AboutView.vue'),
    meta: { transition: 'slide-right' } // 关于页面右滑进入
  },
  {
    path: '/books',
    name: 'books',
    component: () => import('@/views/BooksView.vue'),
    meta: {
      transition: 'slide-left',
      title: '著作集 - 墨影心流'
    }
  },
  {
    path: '/book/:id',
    name: 'book-reader',
    component: () => import('@/views/BookReader.vue'),
    meta: {
      transition: 'fade',
      title: '阅读 - 墨影心流'
    }
  },
  // 在这里添加更多路由规则...
];

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL), // 使用 Hash 模式
  routes,
  // 路由切换时滚动行为：默认滚动到页面顶部
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition; // 如果有保存的位置（浏览器前进/后退），则恢复
    } else {
      return { top: 0, behavior: 'smooth' }; // 否则平滑滚动到顶部
    }
  },
});

// --- 可选：全局导航守卫 ---
// router.beforeEach((to, from, next) => {
//   // 示例：启动加载指示器 (需要配合 Pinia store 或其他状态管理)
//   // const uiStore = useUiStore(); uiStore.startLoading();
//   console.log(`Navigating from ${from.fullPath} to ${to.fullPath}`);
//   next(); // 必须调用 next() 才能继续导航
// });
//
// router.afterEach((to, from) => {
//   // 示例：停止加载指示器
//   // const uiStore = useUiStore(); uiStore.stopLoading();
//   console.log(`Finished navigating to ${to.fullPath}`);
//   // 可以在这里设置页面标题
//   // document.title = to.meta.title || '墨影心流';
// });
// --- 结束：全局导航守卫 ---

export default router;