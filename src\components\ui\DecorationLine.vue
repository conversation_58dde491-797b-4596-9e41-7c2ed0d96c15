<!-- /src/components/ui/DecorationLine.vue -->
<template>
  <div :class="$style.decorationLine" v-if="show">
    <div :class="$style.line"></div>
  </div>
</template>

<script setup>
defineProps({
  show: {
    type: Boolean,
    default: true
  }
});
</script>

<style module>
.decorationLine {
  display: none;
  position: relative;
}

@media (min-width: 768px) {
  .decorationLine {
    display: block;
  }
  
  .line {
    position: absolute;
    top: 30%;
    right: var(--space-l);
    width: 1px;
    height: 200px;
    background: linear-gradient(to bottom, 
      transparent, 
      rgba(var(--color-accent-rgb), 0.08) 20%, 
      rgba(var(--color-accent-rgb), 0.08) 80%, 
      transparent
    );
  }
}
</style> 