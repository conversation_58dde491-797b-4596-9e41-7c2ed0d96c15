<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .token-display {
            word-break: break-all;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
        .log {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 博客管理系统 Token 调试工具</h1>
        
        <div class="section info">
            <h3>📋 当前状态检查</h3>
            <div id="status-info">正在检查...</div>
        </div>

        <div class="section">
            <h3>🔑 Token 管理</h3>
            <button onclick="checkToken()">检查当前Token</button>
            <button onclick="refreshToken()">重新登录获取Token</button>
            <button onclick="clearToken()">清除Token</button>
            <button onclick="testImageUpload()">测试图片上传</button>
            <div id="token-info" class="token-display" style="margin-top: 10px;"></div>
        </div>

        <div class="section">
            <h3>📝 操作日志</h3>
            <div id="log" class="log"></div>
            <button onclick="clearLog()">清除日志</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        function updateStatus() {
            const token = localStorage.getItem('token');
            const statusDiv = document.getElementById('status-info');
            
            if (!token) {
                statusDiv.innerHTML = '<span style="color: red;">❌ 未登录：localStorage中没有token</span>';
                statusDiv.className = 'error';
            } else {
                try {
                    // 解析JWT token
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    const exp = new Date(payload.exp * 1000);
                    const now = new Date();
                    
                    if (exp < now) {
                        statusDiv.innerHTML = `<span style="color: red;">❌ Token已过期：${exp.toLocaleString()}</span>`;
                        statusDiv.className = 'error';
                    } else {
                        statusDiv.innerHTML = `<span style="color: green;">✅ Token有效，过期时间：${exp.toLocaleString()}</span>`;
                        statusDiv.className = 'success';
                    }
                } catch (error) {
                    statusDiv.innerHTML = '<span style="color: orange;">⚠️ Token格式无效</span>';
                    statusDiv.className = 'warning';
                }
            }
        }

        function checkToken() {
            const token = localStorage.getItem('token');
            const tokenDiv = document.getElementById('token-info');
            
            if (!token) {
                tokenDiv.textContent = '没有找到Token';
                log('检查Token：未找到', 'error');
                return;
            }

            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                const exp = new Date(payload.exp * 1000);
                const now = new Date();
                
                tokenDiv.innerHTML = `
                    <strong>Token信息：</strong><br>
                    用户ID: ${payload.userId}<br>
                    用户名: ${payload.username || '未知'}<br>
                    角色: ${payload.role || '未知'}<br>
                    签发时间: ${new Date(payload.iat * 1000).toLocaleString()}<br>
                    过期时间: ${exp.toLocaleString()}<br>
                    状态: ${exp < now ? '❌ 已过期' : '✅ 有效'}<br><br>
                    <strong>完整Token：</strong><br>
                    ${token}
                `;
                
                log(`检查Token：${exp < now ? '已过期' : '有效'}，过期时间 ${exp.toLocaleString()}`, exp < now ? 'error' : 'success');
            } catch (error) {
                tokenDiv.textContent = `Token解析失败: ${error.message}`;
                log(`Token解析失败: ${error.message}`, 'error');
            }
        }

        async function refreshToken() {
            try {
                log('开始重新登录...');
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123456'
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    localStorage.setItem('token', result.data.token);
                    log('重新登录成功，Token已更新', 'success');
                    checkToken();
                    updateStatus();
                } else {
                    log(`登录失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`登录请求失败: ${error.message}`, 'error');
            }
        }

        function clearToken() {
            localStorage.removeItem('token');
            document.getElementById('token-info').textContent = 'Token已清除';
            log('Token已清除', 'warning');
            updateStatus();
        }

        async function testImageUpload() {
            const token = localStorage.getItem('token');
            
            if (!token) {
                log('测试图片上传失败：没有Token', 'error');
                return;
            }

            try {
                log('开始测试图片上传...');
                
                // 创建一个1x1像素的测试图片
                const canvas = document.createElement('canvas');
                canvas.width = 1;
                canvas.height = 1;
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = '#ff0000';
                ctx.fillRect(0, 0, 1, 1);
                
                canvas.toBlob(async (blob) => {
                    const formData = new FormData();
                    formData.append('image', blob, 'test.png');

                    const response = await fetch(`${API_BASE}/images/upload`, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: formData
                    });

                    const result = await response.json();
                    
                    if (result.success) {
                        log(`图片上传成功: ${result.data.filename}`, 'success');
                        
                        // 清理测试图片
                        try {
                            await fetch(`${API_BASE}/images/${result.data.id}`, {
                                method: 'DELETE',
                                headers: { 'Authorization': `Bearer ${token}` }
                            });
                            log('测试图片已清理', 'info');
                        } catch (error) {
                            log('清理测试图片失败', 'warning');
                        }
                    } else {
                        log(`图片上传失败: ${result.message}`, 'error');
                    }
                }, 'image/png');
                
            } catch (error) {
                log(`图片上传测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时检查状态
        window.onload = function() {
            updateStatus();
            log('Token调试工具已加载', 'info');
        };
    </script>
</body>
</html>
