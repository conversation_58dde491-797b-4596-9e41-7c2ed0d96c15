<!-- /src/components/features/ArticleCard.vue -->
<!-- 文章卡片组件，显示文章标题、时间、标签、描述和封面图，带入场动画及悬停效果 -->
<template>
  <!-- 文章卡片容器，点击会打开文章，带有延迟动画效果 -->
  <div 
    :class="$style.articleCard"
    @click="openArticle"
    :style="{ animationDelay: `${props.index * 0.1}s` }"
  >
    <!-- 包含标题、元数据、描述和标签的文本内容区域 -->
    <div :class="$style.textContent">
      <!-- 文章标题 -->
      <h2 :class="$style.title">{{ props.article.title }}</h2>
      <!-- 文章元数据信息（日期、分类） -->
      <ArticleMetadata
        :date="props.article.date"
        :categories="props.article.categories"
      />
      <!-- 文章简介描述 -->
      <p :class="$style.description">{{ props.article.description }}</p>
      <!-- 文章标签列表 -->
      <ArticleTags :tags="props.article.tags" />
    </div>
    <!-- 封面图容器 -->
    <div :class="$style.imageContainer">
      <!-- 如果有封面图片则显示 -->
      <img 
        v-if="props.article.coverImage"
        :src="props.article.coverImage" 
        :alt="props.article.title + ' cover'"
        :class="$style.coverImage"
      />
    </div>
  </div>
</template>

<script setup>
// 引入 Vue 的 defineProps 和 defineEmits 方法
import { defineProps, defineEmits } from 'vue'
// 引入文章元数据和标签两个子组件
import ArticleMetadata from './ArticleMetadata.vue'
import ArticleTags from './ArticleTags.vue'

// 定义接收的 props 参数
const props = defineProps({
  article: {
    type: Object, // 文章对象，包含标题、日期、描述、封面等信息
    required: true
  },
  index: {
    type: Number, // 文章在列表中的索引，用于设置动画延迟
    required: true
  }
})

// 定义组件触发的自定义事件
const emit = defineEmits(['open'])

// 定义打开文章的方法，触发 open 事件并传递文章 slug
const openArticle = () => {
  emit('open', props.article.slug)
}
</script>

<style module>
/* 文章卡片的整体样式 */
.articleCard {
  display: flex; /* 横向排列 */
  align-items: center; /* 垂直居中 */
  gap: var(--space-m); /* 内容与图片间距 */
  width: 100%;
  min-width: 0;
  background: rgba(30, 30, 30, 0.5); /* 半透明深色背景 */
  border-radius: var(--border-radius-soft); /* 圆角 */
  padding: var(--space-m);
  cursor: pointer; /* 鼠标指针 */
  transition: all 0.3s var(--transition-timing-function-in-out); /* 动画过渡效果 */
  border-left: 1px solid rgba(var(--color-accent-rgb), 0.2); /* 左边细线 */
  position: relative;
  overflow: hidden;
  animation: fadeSlideIn 0.5s ease-out forwards; /* 入场动画 */
  opacity: 0;
  transform: translateY(10px); /* 初始向下偏移，配合动画 */
  backdrop-filter: blur(3px); /* 背景模糊 */
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08); /* 阴影效果 */
}

/* 文字内容区域，占据剩余空间 */
.textContent {
  flex-grow: 1.2; 
  min-width: 0;
}

/* 图片容器，固定大小，内部居中对齐 */
.imageContainer {
  flex-shrink: 0;
  width: 130px;
  height: 130px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: var(--border-radius-soft);
  transform: translateX(-7px);
}

/* 封面图片，铺满容器并裁剪 */
.coverImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease; /* 添加缩放平滑过渡效果 */
}

/* 卡片悬停时封面图轻微放大 */
.articleCard:hover .coverImage {
  transform: scale(1.15);
}

/* 入场动画关键帧，透明度渐变并上移 */
@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 悬停时卡片提升阴影和背景变深 */
.articleCard:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  background: rgba(30, 30, 30, 0.8);
  border-left: 1px solid rgba(var(--color-accent-rgb), 0.4);
}

/* 卡片左侧加载动画条，初始高度为0 */
.articleCard:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 0;
  background: var(--color-accent);
  transition: height 0.5s ease;
  opacity: 0.7;
}

/* 悬停时，动画条高度拉满 */
.articleCard:hover:before {
  height: 100%;
}

/* 内部浅色描边边框，仅做装饰性细节 */
.articleCard:after {
  content: '';
  position: absolute;
  top: 1px;
  left: 4px;
  right: 1px;
  bottom: 1px;
  border-radius: calc(var(--border-radius-soft) - 1px);
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.03);
  pointer-events: none;
}

/* 标题样式 */
.title {
  margin: 0 0 var(--space-s);
  color: var(--color-text-primary);
  font-family: var(--font-family-heading);
  font-size: calc(1rem * var(--font-scale-ratio) * var(--font-scale-ratio));
  font-weight: var(--font-weight-heading-h3);
  letter-spacing: var(--letter-spacing-heading);
  text-shadow: 0 0 1px rgba(189, 183, 107, 0.3);
  position: relative;
  display: inline-block;
}

/* 标题底部的动态下划线，默认宽度为0 */
.title:after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -3px;
  width: 0;
  height: 1px;
  background: rgba(var(--color-accent-rgb), 0.4);
  transition: width 0.4s ease;
}

/* 悬停时标题底部下划线展开 */
.articleCard:hover .title:after {
  width: 100%;
}

/* 文章描述文本，限制为2行显示超出部分省略 */
.description {
  color: var(--color-text-primary);
  margin: 0 0 var(--space-l);
  line-height: var(--line-height-base);
  font-family: var(--font-family-body);
  letter-spacing: var(--letter-spacing-body);
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 最多显示两行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>