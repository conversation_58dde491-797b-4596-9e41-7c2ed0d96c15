<!-- /src/views/BookReader.vue -->
<template>
  <MainLayout>
    <div :class="$style.readerContainer">
      <!-- 左侧目录导航 -->
      <div :class="[$style.tocSidebar, { [$style.collapsed]: tocCollapsed }]">
        <div :class="$style.tocHeader">
          <h3 :class="$style.tocTitle">目录</h3>
          <button :class="$style.tocToggle" @click="toggleToc" :aria-label="tocCollapsed ? '展开目录' : '收起目录'">
            <span :class="[$style.toggleIcon, { [$style.collapsed]: tocCollapsed }]"></span>
          </button>
        </div>
        <div :class="$style.tocContent" v-if="!tocCollapsed">
          <ul :class="$style.chaptersList">
            <li
              v-for="(chapter, index) in book?.chapters"
              :key="index"
              :class="[$style.chapterItem, { [$style.active]: currentChapter === index }]"
              @click="navigateToChapter(index)"
            >
              {{ chapter }}
            </li>
          </ul>
        </div>
      </div>

      <!-- 中间内容区域 -->
      <div :class="$style.contentArea">
        <div :class="$style.topNav">
          <router-link :to="{ name: 'books' }" :class="$style.backButton">
            <span :class="$style.backIcon"></span>
            返回书籍列表
          </router-link>
        </div>

        <div :class="$style.bookHeader">
          <h1 :class="$style.bookTitle">{{ book?.title }}</h1>
          <p :class="$style.bookAuthor" v-if="book?.author">{{ book?.author }}</p>
          <div :class="$style.chapterNav">
            <button
              :class="$style.navButton"
              @click="prevChapter"
              :disabled="currentChapter <= 0"
              aria-label="上一章"
            >
              <span :class="$style.navArrow">←</span> 上一章
            </button>
            <span :class="$style.chapterTitle">{{ currentChapterTitle }}</span>
            <button
              :class="$style.navButton"
              @click="nextChapter"
              :disabled="currentChapter >= (book?.chapters.length - 1)"
              aria-label="下一章"
            >
              下一章 <span :class="$style.navArrow">→</span>
            </button>
          </div>
        </div>

        <div :class="$style.bookContent">
          <div v-if="loading" :class="$style.loading">
            <div :class="$style.loadingSpinner"></div>
            <p>加载中...</p>
          </div>
          <div v-else-if="error" :class="$style.error">
            <div :class="$style.errorIcon">!</div>
            <h3 :class="$style.errorTitle">加载失败</h3>
            <p :class="$style.errorMessage">{{ error }}</p>
            <button :class="$style.retryButton" @click="retryLoading">重试</button>
          </div>
          <div v-else :class="$style.chapterContent" v-html="renderedContent"></div>

          <div :class="$style.keyboardHint">
            <div :class="$style.keyHint"><span>←</span> 上一章</div>
            <div :class="$style.keyHint"><span>→</span> 下一章</div>
            <div :class="$style.keyHint"><span>ESC</span> 返回列表</div>
          </div>
        </div>
      </div>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch, useCssModule, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import MainLayout from '@/components/layout/MainLayout.vue';
import { books } from '@/data/books';
import { renderMarkdown } from '@/utils/markdown';

// 获取CSS模块
const styles = useCssModule();

// 路由和导航
const route = useRoute();
const router = useRouter();

// 状态变量
const book = ref(null);
const currentChapter = ref(0);
const tocCollapsed = ref(false);
const loading = ref(true);
const error = ref(null);
const renderedContent = ref('');

// 计算属性：当前章节标题
const currentChapterTitle = computed(() => {
  if (!book.value || !book.value.chapters || book.value.chapters.length === 0) {
    return '';
  }
  return book.value.chapters[currentChapter.value];
});

// 方法：切换目录显示状态
const toggleToc = () => {
  tocCollapsed.value = !tocCollapsed.value;
};

// 方法：导航到指定章节
const navigateToChapter = (index) => {
  currentChapter.value = index;
  // 在移动设备上，点击章节后自动收起目录
  if (window.innerWidth < 768) {
    tocCollapsed.value = true;
  }
  // 滚动到顶部
  window.scrollTo({ top: 0, behavior: 'smooth' });
};

// 方法：重试加载当前章节
const retryLoading = () => {
  if (book.value) {
    loadChapterContent(book.value.id, currentChapter.value);
  }
};

// 方法：上一章
const prevChapter = () => {
  if (currentChapter.value > 0) {
    // 添加翻页动画效果
    const contentElement = document.querySelector(`.${styles.chapterContent}`);
    if (contentElement) {
      // 先添加动画类
      contentElement.classList.add(styles.pageFlipLeft);

      // 等待动画完成后切换章节
      setTimeout(() => {
        // 更新章节索引
        currentChapter.value = currentChapter.value - 1;

        // 滚动到顶部
        window.scrollTo({ top: 0, behavior: 'smooth' });

        // 在移动设备上，自动收起目录
        if (window.innerWidth < 768) {
          tocCollapsed.value = true;
        }

        // 移除动画类，准备下一次动画
        setTimeout(() => {
          contentElement.classList.remove(styles.pageFlipLeft);
        }, 50);
      }, 300);
    } else {
      // 如果找不到内容元素，直接导航
      navigateToChapter(currentChapter.value - 1);
    }
  }
};

// 方法：下一章
const nextChapter = () => {
  if (book.value && currentChapter.value < book.value.chapters.length - 1) {
    // 添加翻页动画效果
    const contentElement = document.querySelector(`.${styles.chapterContent}`);
    if (contentElement) {
      // 先添加动画类
      contentElement.classList.add(styles.pageFlipRight);

      // 等待动画完成后切换章节
      setTimeout(() => {
        // 更新章节索引
        currentChapter.value = currentChapter.value + 1;

        // 滚动到顶部
        window.scrollTo({ top: 0, behavior: 'smooth' });

        // 在移动设备上，自动收起目录
        if (window.innerWidth < 768) {
          tocCollapsed.value = true;
        }

        // 移除动画类，准备下一次动画
        setTimeout(() => {
          contentElement.classList.remove(styles.pageFlipRight);
        }, 50);
      }, 300);
    } else {
      // 如果找不到内容元素，直接导航
      navigateToChapter(currentChapter.value + 1);
    }
  }
};

// 加载章节内容
const loadChapterContent = async (bookId, chapterIndex) => {
  console.log(`开始加载章节内容: bookId=${bookId}, chapterIndex=${chapterIndex}`);

  // 设置加载状态
  loading.value = true;
  error.value = null;
  renderedContent.value = ''; // 清空之前的内容，避免显示旧内容

  try {
    // 构建章节文件路径 - 注意：books.js 中的 id 是 'book-1' 格式，而文件名是 'book-1-chapter-1.md' 格式
    const chapterFilePath = `/books/${bookId}-chapter-${chapterIndex + 1}.md`;

    console.log(`尝试加载章节文件: ${chapterFilePath}`);

    // 尝试从服务器获取章节内容
    const response = await fetch(chapterFilePath, {
      // 添加缓存控制，避免浏览器缓存导致的问题
      cache: 'no-cache',
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });

    let markdownContent = '';

    if (!response.ok) {
      // 如果文件不存在，记录错误并使用模拟数据
      console.warn(`章节文件 ${chapterFilePath} 加载失败 (${response.status}: ${response.statusText})，使用模拟数据`);

      // 模拟章节内容（使用 Markdown 格式）
      // 如果是第一章且是第一本书，显示特殊提示
      if (bookId === 'book-1' && chapterIndex === 0) {
        markdownContent = `
# ${book.value.chapters[chapterIndex]}

## 欢迎阅读

这是《${book.value.title}》的第 ${chapterIndex + 1} 章内容。

> 本书正在持续更新中，感谢您的关注。

### 阅读提示

- 使用左侧目录导航切换章节
- 使用键盘方向键 ← → 切换上一章/下一章
- 按 ESC 键返回书籍列表

祝您阅读愉快！
`;
      } else {
        // 其他章节使用通用模拟内容
        markdownContent = `
# ${book.value.chapters[chapterIndex]}

## 本章内容正在编写中...

这是《${book.value.title}》的第 ${chapterIndex + 1} 章内容预览。

> 完整内容即将上线，敬请期待。

### 章节概要

本章将探讨以下主题：

- 主题一：${book.value.title}的核心概念
- 主题二：实践应用与案例分析
- 主题三：未来展望与发展趋势

感谢您的耐心等待！
`;
      }
    } else {
      // 如果文件存在，使用实际内容
      console.log(`章节文件 ${chapterFilePath} 加载成功`);
      const content = await response.text();

      // 确保内容不为空
      if (!content || content.trim() === '') {
        console.warn(`章节文件 ${chapterFilePath} 内容为空，使用模拟数据`);
        markdownContent = `
# ${book.value.chapters[chapterIndex]}

## 章节内容为空

很抱歉，《${book.value.title}》的第 ${chapterIndex + 1} 章内容似乎为空。

> 请联系网站管理员解决此问题。
`;
      } else {
        // 处理内容中可能存在的格式问题
        markdownContent = content
          .replace(/\r\n/g, '\n') // 统一换行符
          .replace(/\s+$/gm, ''); // 移除行尾多余空格
      }
    }

    console.log('渲染Markdown内容');
    // 渲染Markdown内容
    renderedContent.value = renderMarkdown(markdownContent);

    console.log('内容渲染完成，准备更新DOM');

    // 使用nextTick确保DOM已更新
    nextTick(() => {
      console.log('Vue nextTick: DOM应该已更新');

      // 强制重新计算内容区域高度
      const contentElement = document.querySelector(`.${styles.chapterContent}`);
      if (contentElement) {
        console.log('找到内容元素，强制重新计算布局');
        contentElement.style.display = 'none';
        // 强制浏览器重新计算布局
        void contentElement.offsetHeight;
        contentElement.style.display = '';
      } else {
        console.warn('未找到内容元素，无法重新计算布局');
      }

      // 完成加载
      loading.value = false;
      console.log('章节内容加载完成');
    });
  } catch (err) {
    console.error('加载章节内容失败:', err);
    error.value = `加载章节内容失败: ${err.message}`;
    loading.value = false;
  }
};

// 监听路由参数变化 (包括路径参数和查询参数)
watch(
  [() => route.params.id, () => route.query.chapter],
  ([newId, newChapter]) => {
    console.log(`路由参数变化: id=${newId}, chapter=${newChapter}`);

    if (newId) {
      // 强制延迟一帧，确保在路由完全解析后执行
      nextTick(() => {
        const foundBook = books.find(b => b.id === newId);
        if (foundBook) {
          console.log(`找到书籍: ${foundBook.title}`);
          book.value = foundBook;

          // 更新章节索引
          const chapterIndex = parseInt(newChapter || 0);
          console.log(`设置当前章节索引: ${chapterIndex}`);
          currentChapter.value = chapterIndex;

          // 立即加载章节内容
          console.log(`加载章节内容: ${newId}, 章节索引: ${chapterIndex}`);
          loadChapterContent(newId, chapterIndex);

          // 设置页面标题
          document.title = `${foundBook.title} - 阅读 - 墨影心流`;
        } else {
          console.error(`未找到ID为${newId}的书籍`);
          error.value = '未找到指定书籍';
          book.value = null;
        }
      });
    }
  },
  { immediate: true }
);

// 监听章节变化
watch(currentChapter, (newChapter, oldChapter) => {
  console.log(`章节变化: ${oldChapter} -> ${newChapter}`);

  if (book.value) {
    // 只有当章节真正变化时才更新
    if (newChapter !== oldChapter) {
      console.log(`更新URL查询参数: chapter=${newChapter}`);

      // 更新 URL 查询参数，但不触发路由导航
      router.replace({
        query: { ...route.query, chapter: newChapter }
      }).catch(err => {
        console.error('更新路由查询参数失败:', err);
      });

      // 加载新章节内容
      console.log(`加载新章节内容: ${book.value.id}, 章节索引: ${newChapter}`);
      loadChapterContent(book.value.id, newChapter);

      // 记录当前章节索引到控制台，方便调试
      console.log(`当前章节索引: ${newChapter}, 章节标题: ${book.value.chapters[newChapter]}`);

      // 滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }
});

// 键盘导航处理函数
const handleKeyNavigation = (event) => {
  // 如果用户正在输入框中，不处理键盘导航
  if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
    return;
  }

  // 左箭头键 - 上一章
  if (event.key === 'ArrowLeft') {
    event.preventDefault(); // 防止页面滚动
    console.log('键盘导航: 上一章');
    if (currentChapter.value > 0) {
      prevChapter();
    } else {
      console.log('已经是第一章，无法导航到上一章');
    }
  }

  // 右箭头键 - 下一章
  if (event.key === 'ArrowRight') {
    event.preventDefault(); // 防止页面滚动
    console.log('键盘导航: 下一章');
    if (book.value && currentChapter.value < book.value.chapters.length - 1) {
      nextChapter();
    } else {
      console.log('已经是最后一章，无法导航到下一章');
    }
  }

  // Esc 键 - 返回书籍列表
  if (event.key === 'Escape') {
    console.log('键盘导航: 返回书籍列表');
    router.push({ name: 'books' });
  }
};

// 组件挂载时初始化
onMounted(() => {
  console.log('BookReader组件挂载 - 开始初始化');

  // 根据屏幕宽度初始化目录状态
  tocCollapsed.value = window.innerWidth < 768;

  // 添加键盘事件监听
  window.addEventListener('keydown', handleKeyNavigation);

  // 添加窗口大小变化监听，确保滚动条正确显示
  window.addEventListener('resize', handleResize);

  // 确保在DOM完全渲染后初始化内容
  nextTick(() => {
    // 立即初始化书籍和章节
    initializeBookAndChapter();

    // 强制重新计算内容区域高度
    handleResize();

    console.log('BookReader组件挂载 - DOM更新后初始化完成');
  });

  console.log('BookReader组件挂载 - 初始化完成');
});

// 窗口大小变化处理函数
const handleResize = () => {
  // 强制重新计算内容区域高度
  const contentElement = document.querySelector(`.${styles.chapterContent}`);
  if (contentElement) {
    contentElement.style.display = 'none';
    void contentElement.offsetHeight;
    contentElement.style.display = '';
  }
};

// 初始化书籍和章节
const initializeBookAndChapter = () => {
  console.log('初始化书籍和章节');
  const { id } = route.params;
  const chapter = parseInt(route.query.chapter || 0);

  console.log(`路由参数: id=${id}, chapter=${chapter}`);

  if (!id) {
    console.error('未指定书籍ID');
    error.value = '未指定书籍';
    return;
  }

  const foundBook = books.find(b => b.id === id);
  if (!foundBook) {
    console.error(`未找到ID为${id}的书籍`);
    error.value = '未找到指定书籍';
    return;
  }

  console.log(`找到书籍: ${foundBook.title}`);
  book.value = foundBook;
  currentChapter.value = chapter;

  // 设置页面标题
  document.title = `${foundBook.title} - 阅读 - 墨影心流`;

  // 立即加载章节内容，不使用setTimeout
  console.log(`立即加载章节内容: ${id}, 章节索引: ${chapter}`);

  // 确保在下一个渲染周期加载内容
  nextTick(() => {
    loadChapterContent(id, chapter);
  });
};

// 组件卸载时清理
onBeforeUnmount(() => {
  console.log('BookReader组件卸载 - 清理事件监听器');

  // 移除键盘事件监听
  window.removeEventListener('keydown', handleKeyNavigation);

  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize);

  console.log('BookReader组件卸载 - 清理完成');
});
</script>

<style module>
.readerContainer {
  display: flex;
  width: 100%;
  min-height: calc(100vh - 200px);
  position: relative;
  background: rgba(26, 32, 44, 0.8);
  border-radius: var(--border-radius-soft);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 0 40px rgba(0, 0, 0, 0.1) inset;
  backdrop-filter: blur(5px);
}

.tocSidebar {
  flex: 0 0 300px;
  background: rgba(20, 24, 33, 0.95);
  border-right: 1px solid rgba(255, 255, 255, 0.07);
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  overflow: hidden;
  box-shadow: 5px 0 15px rgba(0, 0, 0, 0.1);
  z-index: 10;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tocSidebar.collapsed {
  flex: 0 0 50px;
}

.tocHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-m) var(--space-l);
  border-bottom: 1px solid rgba(255, 255, 255, 0.07);
  background: linear-gradient(
    to right,
    rgba(var(--color-accent-rgb), 0.1),
    transparent
  );
  flex-shrink: 0;
}

.tocTitle {
  margin: 0;
  font-size: 1.2rem;
  color: var(--color-accent);
  font-family: var(--font-family-heading);
  letter-spacing: 0.03em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.tocToggle {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.tocToggle:hover {
  color: var(--color-text-primary);
}

.toggleIcon {
  position: relative;
  width: 16px;
  height: 2px;
  background-color: currentColor;
  transition: transform 0.3s ease;
}

.toggleIcon::before,
.toggleIcon::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 2px;
  background-color: currentColor;
  transition: transform 0.3s ease;
}

.toggleIcon::before {
  transform: translateY(-5px);
}

.toggleIcon::after {
  transform: translateY(5px);
}

.toggleIcon.collapsed {
  transform: rotate(180deg);
}

.tocContent {
  padding: var(--space-m);
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.chaptersList {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  padding-right: var(--space-xs); /* 添加一些右侧内边距，防止文本靠得太近 */
}

.chaptersList::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.chapterItem {
  padding: var(--space-xs) var(--space-m);
  margin-bottom: var(--space-xs);
  border-radius: var(--border-radius-soft);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  color: var(--color-text-secondary);
  border-left: 2px solid transparent;
  position: relative;
  white-space: normal;
  word-break: break-word;
  line-height: 1.3;
  display: flex;
  align-items: center;
}

.chapterItem:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--color-text-primary);
  border-left-color: var(--color-accent);
  transform: translateX(2px);
}

.chapterItem.active {
  background: rgba(var(--color-accent-rgb), 0.15);
  color: var(--color-text-primary);
  border-left-color: var(--color-accent);
  font-weight: var(--font-weight-medium);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chapterItem.active::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: var(--color-accent);
  opacity: 0.7;
}

.contentArea {
  flex: 1;
  padding: var(--space-xl) var(--space-xl);
  overflow-y: auto;
  background: linear-gradient(
    to bottom,
    rgba(30, 30, 44, 0.4) 0%,
    rgba(26, 32, 44, 0.8) 100%
  );
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--color-accent-rgb), 0.3) rgba(0, 0, 0, 0.1);
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 0; /* 关键修复：确保flex子元素可以正确滚动 */
  height: 100%; /* 确保占满容器高度 */
}

.contentArea::-webkit-scrollbar {
  width: 8px;
}

.contentArea::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.contentArea::-webkit-scrollbar-thumb {
  background-color: rgba(var(--color-accent-rgb), 0.3);
  border-radius: 8px;
}

.topNav {
  margin-bottom: var(--space-xl);
  position: relative;
  display: flex;
  justify-content: flex-start;
  width: 100%;
  max-width: 800px;
}

.backButton {
  display: inline-flex;
  align-items: center;
  padding: var(--space-s) var(--space-l);
  background: rgba(var(--color-accent-rgb), 0.08);
  border: 1px solid rgba(var(--color-accent-rgb), 0.2);
  border-radius: var(--border-radius-soft);
  color: var(--color-accent);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  position: relative;
  overflow: hidden;
  font-size: 0.95rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.02em;
}

.backButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(var(--color-accent-rgb), 0.1),
    rgba(var(--color-accent-rgb), 0)
  );
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.backButton:hover::before {
  transform: translateX(0);
}

.backButton:hover {
  background: rgba(var(--color-accent-rgb), 0.12);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  color: var(--color-accent-hover);
  border-color: rgba(var(--color-accent-rgb), 0.4);
}

.backIcon {
  position: relative;
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: var(--space-m);
}

.backIcon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 14px;
  height: 2px;
  background-color: currentColor;
  transform: translateY(-50%);
}

.backIcon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  width: 9px;
  height: 9px;
  border-left: 2px solid currentColor;
  border-bottom: 2px solid currentColor;
  transform: translateY(-50%) rotate(45deg);
  transform-origin: center;
}

.navArrow {
  display: inline-block;
  font-size: 1.2rem;
  line-height: 1;
  margin: 0 var(--space-xs);
  transition: transform 0.3s ease;
}

.bookHeader {
  margin-bottom: var(--space-xxl);
  padding-bottom: var(--space-l);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  text-align: center;
  width: 100%;
  max-width: 800px;
}

.bookHeader::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 2px;
  background: linear-gradient(
    to right,
    rgba(var(--color-accent-rgb), 0.1),
    var(--color-accent),
    rgba(var(--color-accent-rgb), 0.1)
  );
}

.bookTitle {
  font-family: var(--font-family-heading);
  font-size: 2.2rem;
  color: var(--color-text-primary);
  margin-bottom: var(--space-m);
  font-weight: var(--font-weight-heading-h1);
  letter-spacing: var(--letter-spacing-heading);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  position: relative;
  display: inline-block;
  max-width: 90%;
  line-height: 1.3;
}

.bookTitle::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 1px;
  background: rgba(var(--color-accent-rgb), 0.5);
}

.bookAuthor {
  font-size: 1.2rem;
  color: var(--color-text-secondary);
  margin-bottom: var(--space-xl);
  font-style: italic;
  opacity: 0.9;
  position: relative;
  display: inline-block;
  padding: 0 var(--space-m);
}

.bookAuthor::before,
.bookAuthor::after {
  content: '—';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(var(--color-accent-rgb), 0.5);
  font-style: normal;
}

.bookAuthor::before {
  left: -5px;
}

.bookAuthor::after {
  right: -5px;
}

.chapterNav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: var(--space-xl) auto 0;
  background: rgba(20, 24, 33, 0.7);
  padding: var(--space-m) var(--space-l);
  border-radius: var(--border-radius-soft);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.07);
  max-width: 90%;
  position: relative;
  /* 确保导航区域有足够的高度容纳多行标题 */
  min-height: 60px;
  /* 添加间距，使布局更加舒适 */
  gap: var(--space-m);
}

.chapterNav::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgba(var(--color-accent-rgb), 0.3),
    transparent
  );
}

.chapterTitle {
  font-size: 1.2rem;
  color: var(--color-accent);
  text-align: center;
  flex: 2;
  padding: 0 var(--space-xl);
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-medium);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  /* 允许长标题换行显示 */
  white-space: normal;
  overflow: visible;
  /* 最多显示两行，超出部分省略 */
  display: -webkit-box;
  display: box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  line-height: 1.3;
  min-height: 3rem;
  position: relative;
}

.chapterTitle::before,
.chapterTitle::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 20px;
  height: 1px;
  background: rgba(var(--color-accent-rgb), 0.3);
}

.chapterTitle::before {
  left: var(--space-l);
}

.chapterTitle::after {
  right: var(--space-l);
}

.navButton {
  background: rgba(var(--color-accent-rgb), 0.1);
  border: 1px solid var(--color-accent);
  color: var(--color-accent);
  padding: var(--space-s) var(--space-m);
  border-radius: var(--border-radius-soft);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  font-weight: var(--font-weight-medium);
  position: relative;
  overflow: hidden;
  min-width: 120px;
  text-align: center;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  /* 确保按钮有足够的高度 */
  height: 42px;
  /* 确保按钮不会被挤压 */
  flex-shrink: 0;
  /* 添加更明显的悬停效果 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  /* 确保文本不会被截断 */
  white-space: nowrap;
}

.navButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(var(--color-accent-rgb), 0.1),
    rgba(var(--color-accent-rgb), 0)
  );
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.navButton:hover:not(:disabled)::before {
  transform: translateX(0);
}

.navButton:hover:not(:disabled) {
  background: rgba(var(--color-accent-rgb), 0.2);
  color: var(--color-accent-hover);
  border-color: var(--color-accent-hover);
  box-shadow: 0 2px 10px rgba(var(--color-accent-rgb), 0.2);
  transform: translateY(-2px);
}

.navButton:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.bookContent {
  line-height: 1.8;
  color: var(--color-text-primary);
  position: relative;
}

.bookContent::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgba(var(--color-accent-rgb), 0.3),
    transparent
  );
}

.chapterContent {
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
  position: relative;
  padding: var(--space-l) var(--space-xl);
  background: rgba(26, 32, 44, 0.4);
  border-radius: var(--border-radius-soft);
  box-shadow:
    0 0 40px rgba(0, 0, 0, 0.1) inset,
    0 0 1px rgba(255, 255, 255, 0.1);
  overflow-wrap: break-word;
  word-break: break-word;
  min-height: 300px; /* 确保内容区域有最小高度 */
  overflow-x: hidden; /* 防止水平溢出 */
}

.chapterContent::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: radial-gradient(
    circle at bottom right,
    rgba(var(--color-accent-rgb), 0.05),
    transparent 70%
  );
  pointer-events: none;
}

.chapterContent h1 {
  font-size: 2rem;
  margin-bottom: var(--space-xl);
  color: var(--color-text-primary);
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-heading-h1);
  text-align: center;
  position: relative;
  padding-bottom: var(--space-m);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.chapterContent h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(
    to right,
    rgba(var(--color-accent-rgb), 0.2),
    var(--color-accent),
    rgba(var(--color-accent-rgb), 0.2)
  );
}

.chapterContent h2 {
  font-size: 1.6rem;
  margin-top: var(--space-xxl);
  margin-bottom: var(--space-l);
  color: var(--color-text-primary);
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-heading-h2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: var(--space-s);
  position: relative;
}

.chapterContent h2::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--color-accent);
}

.chapterContent h3 {
  font-size: 1.4rem;
  margin-top: var(--space-xl);
  margin-bottom: var(--space-m);
  color: var(--color-accent);
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-heading-h3);
  position: relative;
  display: inline-block;
}

.chapterContent h3::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 30%;
  height: 1px;
  background: rgba(var(--color-accent-rgb), 0.3);
}

.chapterContent h4 {
  font-size: 1.2rem;
  margin-top: var(--space-l);
  margin-bottom: var(--space-m);
  color: var(--color-text-primary);
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-heading-h4);
}

.chapterContent p {
  margin-bottom: var(--space-l);
  line-height: 1.8;
  letter-spacing: 0.01em;
  text-align: justify;
  max-width: 100%;
  font-size: 1.05rem;
}

.chapterContent strong {
  color: var(--color-text-primary);
  font-weight: var(--font-weight-bold);
}

.chapterContent em {
  font-style: italic;
  color: rgba(var(--color-accent-rgb), 0.9);
}

.chapterContent blockquote {
  border-left: 3px solid var(--color-accent);
  padding: var(--space-m) var(--space-l);
  margin: var(--space-l) 0;
  font-style: italic;
  color: var(--color-text-secondary);
  background: rgba(var(--color-accent-rgb), 0.05);
  border-radius: 0 var(--border-radius-soft) var(--border-radius-soft) 0;
  position: relative;
}

.chapterContent blockquote::before {
  content: '"';
  position: absolute;
  top: 0;
  left: var(--space-s);
  font-size: 2.5rem;
  color: rgba(var(--color-accent-rgb), 0.2);
  font-family: var(--font-family-heading);
  line-height: 1;
}

.chapterContent ul, .chapterContent ol {
  margin: var(--space-l) 0;
  padding-left: var(--space-xl);
}

.chapterContent li {
  margin-bottom: var(--space-s);
  position: relative;
}

.chapterContent ul li::marker {
  color: var(--color-accent);
}

/* 水平分隔线样式 */
.chapterContent hr {
  border: none;
  height: 1px;
  background: linear-gradient(
    to right,
    transparent,
    rgba(var(--color-accent-rgb), 0.3),
    transparent
  );
  margin: var(--space-xl) auto;
  width: 80%;
  position: relative;
}

.chapterContent hr::before {
  content: '✦';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(26, 32, 44, 0.4);
  color: var(--color-accent);
  padding: 0 var(--space-s);
  font-size: 0.8rem;
}

.chapterContent pre {
  background: rgba(20, 22, 30, 0.95);
  padding: var(--space-l);
  border-radius: var(--border-radius-soft);
  overflow-x: auto;
  margin: var(--space-l) 0;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.chapterContent code {
  font-family: var(--font-family-mono);
  font-size: 0.9rem;
  background: rgba(20, 22, 30, 0.6);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  color: var(--color-accent);
}

.chapterContent pre code {
  background: transparent;
  padding: 0;
  color: var(--color-text-primary);
}

/* 页面翻转动画 */
@keyframes pageFlipOutLeft {
  from {
    transform: translateX(0) rotateY(0);
    opacity: 1;
  }
  to {
    transform: translateX(-10%) rotateY(10deg);
    opacity: 0;
  }
}

@keyframes pageFlipOutRight {
  from {
    transform: translateX(0) rotateY(0);
    opacity: 1;
  }
  to {
    transform: translateX(10%) rotateY(-10deg);
    opacity: 0;
  }
}

@keyframes pageFlipIn {
  from {
    transform: translateX(0) rotateY(0);
    opacity: 0;
  }
  to {
    transform: translateX(0) rotateY(0);
    opacity: 1;
  }
}

.pageFlipLeft {
  animation: pageFlipOutLeft 0.3s ease-in forwards;
  transform-origin: left center;
  perspective: 1000px;
  backface-visibility: hidden;
}

.pageFlipRight {
  animation: pageFlipOutRight 0.3s ease-in forwards;
  transform-origin: right center;
  perspective: 1000px;
  backface-visibility: hidden;
}

.chapterContent {
  animation: pageFlipIn 0.4s ease-out;
  transform-origin: center;
  perspective: 1000px;
  backface-visibility: hidden;
  transition: all 0.3s ease;
}

/* 键盘导航提示 */
.keyboardHint {
  display: flex;
  justify-content: center;
  gap: var(--space-l);
  margin-top: var(--space-xxl);
  padding: var(--space-m);
  background: rgba(20, 24, 33, 0.6);
  border-radius: var(--border-radius-soft);
  border: 1px solid rgba(255, 255, 255, 0.05);
  opacity: 0.7;
  transition: opacity 0.3s ease;
  width: 100%;
  max-width: 800px;
}

.keyboardHint:hover {
  opacity: 1;
}

.keyHint {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.keyHint span {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 30px;
  height: 30px;
  background: rgba(var(--color-accent-rgb), 0.1);
  border: 1px solid rgba(var(--color-accent-rgb), 0.3);
  border-radius: var(--border-radius-soft);
  margin-right: var(--space-xs);
  padding: 0 var(--space-xs);
  font-family: var(--font-family-mono);
  color: var(--color-accent);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-xxl);
  color: var(--color-text-secondary);
  min-height: 300px;
  background: rgba(26, 32, 44, 0.4);
  border-radius: var(--border-radius-soft);
  box-shadow: 0 0 40px rgba(0, 0, 0, 0.1) inset;
}

.loadingSpinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(var(--color-accent-rgb), 0.2);
  border-radius: 50%;
  border-top-color: var(--color-accent);
  animation: spin 1.2s cubic-bezier(0.5, 0.1, 0.5, 0.9) infinite;
  margin-bottom: var(--space-l);
  box-shadow: 0 0 15px rgba(var(--color-accent-rgb), 0.1);
  position: relative;
}

.loadingSpinner::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
  border: 1px solid rgba(var(--color-accent-rgb), 0.1);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 0.5; }
  50% { transform: scale(1.1); opacity: 0.3; }
  100% { transform: scale(1); opacity: 0.5; }
}

.loading p {
  font-size: 1.1rem;
  letter-spacing: 0.05em;
  margin-top: var(--space-m);
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-light);
  color: var(--color-accent);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.error {
  padding: var(--space-xxl);
  color: #e74c3c;
  text-align: center;
  background: rgba(231, 76, 60, 0.05);
  border-radius: var(--border-radius-soft);
  border: 1px solid rgba(231, 76, 60, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  font-size: 1.1rem;
  font-weight: var(--font-weight-medium);
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.errorIcon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(231, 76, 60, 0.1);
  border: 2px solid #e74c3c;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: bold;
  color: #e74c3c;
  margin-bottom: var(--space-m);
}

.errorTitle {
  font-size: 1.5rem;
  margin-bottom: var(--space-m);
  color: #e74c3c;
  font-family: var(--font-family-heading);
}

.errorMessage {
  margin-bottom: var(--space-l);
  line-height: 1.6;
  max-width: 600px;
}

.retryButton {
  background: rgba(231, 76, 60, 0.1);
  border: 1px solid #e74c3c;
  color: #e74c3c;
  padding: var(--space-s) var(--space-xl);
  border-radius: var(--border-radius-soft);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: var(--font-weight-medium);
  font-size: 1rem;
}

.retryButton:hover {
  background: rgba(231, 76, 60, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(231, 76, 60, 0.1);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .readerContainer {
    flex-direction: column;
    min-height: calc(100vh - 150px);
  }

  .tocSidebar {
    flex: 0 0 auto;
    width: 100%;
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.07);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    z-index: 20;
  }

  .tocSidebar.collapsed {
    flex: 0 0 auto;
    height: 50px;
  }

  .tocContent {
    height: auto;
    max-height: none;
    flex: 0 0 auto;
  }

  .contentArea {
    padding: var(--space-s);
  }

  .chapterContent {
    padding: var(--space-m) var(--space-s);
  }

  /* 特殊处理附录页面 */
  .chapterContent h3 + pre,
  .chapterContent h4 + pre {
    margin-top: var(--space-xs);
  }

  /* 优化附录页面的列表显示 */
  .chapterContent ul li,
  .chapterContent ol li {
    margin-bottom: var(--space-xs);
  }

  .topNav {
    margin-bottom: var(--space-l);
  }

  .backButton {
    padding: var(--space-xs) var(--space-m);
    font-size: 0.9rem;
    width: 100%;
    justify-content: center;
  }

  .bookHeader {
    margin-bottom: var(--space-l);
    padding-bottom: var(--space-m);
  }

  .bookTitle {
    font-size: 1.5rem;
    max-width: 100%;
    line-height: 1.3;
  }

  .bookAuthor {
    font-size: 1rem;
    margin-bottom: var(--space-m);
  }

  .bookAuthor::before,
  .bookAuthor::after {
    display: none;
  }

  .chapterNav {
    flex-wrap: wrap;
    margin-top: var(--space-m);
    padding: var(--space-m) var(--space-s);
    max-width: 100%;
    gap: var(--space-m);
  }

  .chapterTitle {
    order: -1;
    width: 100%;
    margin-bottom: var(--space-m);
    font-size: 1.1rem;
    padding: var(--space-s) 0;
    min-height: auto;
    /* 确保在移动设备上标题能够完整显示 */
    display: -webkit-box;
    display: box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    box-orient: vertical;
    line-height: 1.4;
  }

  .chapterTitle::before,
  .chapterTitle::after {
    display: none;
  }

  .navButton {
    flex: 1;
    min-width: 100px;
    padding: var(--space-s) var(--space-m);
    font-size: 0.95rem;
    /* 增加按钮高度，提高可点击区域 */
    height: 40px;
    /* 确保按钮文本居中 */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .chapterContent {
    padding: var(--space-m);
    max-width: 100%;
  }

  .chapterContent p {
    font-size: 1rem;
  }

  .chapterContent h1 {
    font-size: 1.6rem;
    margin-bottom: var(--space-l);
    padding-bottom: var(--space-s);
  }

  .chapterContent h2 {
    font-size: 1.4rem;
    margin-top: var(--space-xl);
  }

  .chapterContent h3 {
    font-size: 1.2rem;
  }

  .chapterContent p {
    font-size: 0.95rem;
    line-height: 1.8;
    margin-bottom: var(--space-m);
  }

  .chapterContent blockquote {
    padding: var(--space-s) var(--space-m);
    margin: var(--space-m) 0;
  }

  .chapterContent ul, .chapterContent ol {
    padding-left: var(--space-l);
  }

  /* 移动设备上的水平分隔线样式 */
  .chapterContent hr {
    width: 90%;
    margin: var(--space-l) auto;
  }

  .chapterContent hr::before {
    font-size: 0.7rem;
    padding: 0 var(--space-xs);
  }

  /* 移动设备上的代码块样式 */
  .chapterContent pre {
    padding: var(--space-s);
    font-size: 0.8rem;
    max-width: 100%;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .chapterContent code {
    font-size: 0.8rem;
    word-break: break-word;
  }

  /* 移动设备上的表格样式 */
  .table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin-bottom: var(--space-m);
  }

  .chapterContent table {
    width: 100%;
    font-size: 0.85rem;
    border-collapse: collapse;
    margin-bottom: 0;
  }

  .chapterContent th,
  .chapterContent td {
    padding: var(--space-xs);
    border: 1px solid rgba(255, 255, 255, 0.1);
    white-space: nowrap;
  }

  /* 移动设备上的长文本样式 */
  .chapterContent p {
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: var(--space-m);
  }

  /* 移动设备上的标题样式 */
  .chapterContent h1 {
    font-size: 1.6rem;
    margin-bottom: var(--space-m);
  }

  .chapterContent h2 {
    font-size: 1.4rem;
    margin-top: var(--space-l);
    margin-bottom: var(--space-s);
  }

  .chapterContent h3 {
    font-size: 1.2rem;
    margin-top: var(--space-m);
    margin-bottom: var(--space-xs);
  }

  .keyboardHint {
    flex-wrap: wrap;
    gap: var(--space-m);
    padding: var(--space-s);
    margin-top: var(--space-l);
  }

  .keyHint {
    font-size: 0.8rem;
  }

  .keyHint span {
    min-width: 24px;
    height: 24px;
  }
}

/* 平板设备 */
@media (min-width: 769px) and (max-width: 1024px) {
  .tocSidebar {
    flex: 0 0 240px;
  }

  .contentArea {
    padding: var(--space-l);
  }

  .chapterContent {
    padding: var(--space-l);
  }

  /* 平板设备上的水平分隔线样式 */
  .chapterContent hr {
    width: 85%;
    margin: var(--space-xl) auto;
  }
}
</style>
