import fetch from 'node-fetch';
import fs from 'fs/promises';
import path from 'path';

const BASE_URL = 'http://localhost:3001/api';
let authToken = '';

// 测试前端修复功能
async function testFrontendFix() {
  console.log('🔧 开始测试前端修复功能...\n');

  try {
    // 1. 登录获取token
    console.log('1. 管理员登录');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123456'
      })
    });

    const loginResult = await loginResponse.json();
    if (!loginResult.success) {
      throw new Error('登录失败');
    }

    authToken = loginResult.data.token;
    console.log('✓ 登录成功');

    // 2. 测试图片列表功能
    console.log('\n2. 测试图片列表功能');
    const imagesResponse = await fetch(`${BASE_URL}/images`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    const imagesResult = await imagesResponse.json();
    if (imagesResult.success) {
      console.log(`✓ 图片列表获取成功，共 ${imagesResult.data.images?.length || 0} 张图片`);
      
      // 检查图片URL格式
      if (imagesResult.data.images.length > 0) {
        const firstImage = imagesResult.data.images[0];
        console.log(`✓ 图片URL格式: ${firstImage.url}`);
        
        if (firstImage.url.startsWith('/articles/img/')) {
          console.log('✓ 图片URL格式正确');
        } else {
          console.log('✗ 图片URL格式不正确');
        }
      }
    } else {
      console.log('✗ 图片列表获取失败');
    }

    // 3. 测试数据同步功能
    console.log('\n3. 测试数据同步功能');
    const syncResponse = await fetch(`${BASE_URL}/articles/sync`, {
      method: 'POST',
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    const syncResult = await syncResponse.json();
    if (syncResult.success) {
      console.log('✓ 手动同步成功');
    } else {
      console.log('✗ 手动同步失败');
    }

    // 4. 验证前端数据文件格式
    console.log('\n4. 验证前端数据文件格式');
    await verifyFrontendDataFormat();

    // 5. 测试前端博客访问
    console.log('\n5. 测试前端博客访问');
    await testFrontendAccess();

    console.log('\n🎉 前端修复功能测试完成！');

  } catch (error) {
    console.error('❌ 前端修复测试失败:', error.message);
  }
}

// 验证前端数据文件格式
async function verifyFrontendDataFormat() {
  try {
    const articlesPath = path.resolve('../src/data/articles.js');
    const articlesContent = await fs.readFile(articlesPath, 'utf8');
    
    // 检查基本格式
    if (articlesContent.includes('export const articles = [')) {
      console.log('✓ articles.js文件格式正确');
    } else {
      console.log('✗ articles.js文件格式不正确');
      return;
    }

    // 检查数据结构
    const lines = articlesContent.split('\n');
    let hasSlugField = false;
    let hasCategoriesArray = false;
    let hasTagsArray = false;

    for (const line of lines) {
      if (line.includes("'slug':")) hasSlugField = true;
      if (line.includes("'categories': [")) hasCategoriesArray = true;
      if (line.includes("'tags': [")) hasTagsArray = true;
    }

    if (hasSlugField) {
      console.log('✓ 包含slug字段');
    } else {
      console.log('✗ 缺少slug字段');
    }

    if (hasCategoriesArray) {
      console.log('✓ categories字段为数组格式');
    } else {
      console.log('✗ categories字段格式不正确');
    }

    if (hasTagsArray) {
      console.log('✓ tags字段为数组格式');
    } else {
      console.log('✗ tags字段格式不正确');
    }

  } catch (error) {
    console.log('⚠ 前端数据文件验证失败:', error.message);
  }
}

// 测试前端博客访问
async function testFrontendAccess() {
  try {
    // 测试前端首页
    const frontendResponse = await fetch('http://localhost:5175');
    
    if (frontendResponse.ok) {
      console.log('✓ 前端博客首页访问正常');
      
      const htmlContent = await frontendResponse.text();
      
      // 检查是否包含基本内容
      if (htmlContent.includes('<!DOCTYPE html>')) {
        console.log('✓ 前端页面HTML结构正常');
      } else {
        console.log('⚠ 前端页面HTML结构可能有问题');
      }
      
    } else {
      console.log(`✗ 前端博客访问失败，状态码: ${frontendResponse.status}`);
    }
    
  } catch (error) {
    console.log('⚠ 前端博客访问测试失败:', error.message);
  }
}

// 运行测试
testFrontendFix();
