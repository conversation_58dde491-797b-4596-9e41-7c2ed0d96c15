<!-- /src/views/BooksView.vue -->
<template>
  <MainLayout>
    <div :class="$style.booksContainer">
      <div :class="$style.pageHeader">
        <h1 :class="$style.pageTitle">著作集</h1>
        <p :class="$style.pageDescription">
          精选著作，凝聚思想与情感的结晶
        </p>
      </div>

      <div :class="$style.booksList">
        <div
          v-for="(book, index) in books"
          :key="book.id"
          :class="$style.bookCard"
          :style="{ animationDelay: `${index * 0.1}s` }"
          @click="openBookDetails(book.id)"
        >
          <div :class="$style.bookCover">
            <img
              :src="book.coverImage"
              :alt="`${book.title} 封面`"
              :class="$style.coverImage"
            />
          </div>
          <div :class="$style.bookInfo">
            <h2 :class="$style.bookTitle">{{ book.title }}</h2>
            <div :class="$style.bookMeta">
              <span v-if="book.author" :class="$style.author">作者: {{ book.author }}</span>
              <span :class="$style.publishDate">出版日期: {{ formatDate(book.publishDate) }}</span>
            </div>
            <div :class="$style.bookSummary">
              <p>{{ truncateText(book.summary, 180) }}</p>
            </div>
            <div :class="$style.bookActions">
              <button :class="$style.viewDetailsButton" @click.stop="openBookDetails(book.id)">
                查看详情
              </button>
              <router-link
                :to="{ name: 'book-reader', params: { id: book.id }, query: { chapter: 0 } }"
                :class="$style.cardReadButton"
                @click.stop
              >
                阅读
              </router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- 书籍详情模态框 -->
      <Teleport to="body">
        <Transition name="modal-fade">
          <div v-if="selectedBook" :class="$style.modalOverlay" @click.self="closeBookDetails">
            <div :class="$style.modalContainer">
              <button :class="$style.closeButton" @click="closeBookDetails" aria-label="关闭详情">×</button>

              <div :class="$style.bookDetailContent">
                <div :class="$style.bookDetailHeader">
                  <div :class="$style.bookDetailCover">
                    <img
                      :src="selectedBook.coverImage"
                      :alt="`${selectedBook.title} 封面`"
                      :class="$style.detailCoverImage"
                    />
                  </div>
                  <div :class="$style.bookDetailInfo">
                    <h2 :class="$style.detailTitle">{{ selectedBook.title }}</h2>
                    <div :class="$style.detailMeta">
                      <p v-if="selectedBook.author"><strong>作者:</strong> {{ selectedBook.author }}</p>
                      <p><strong>出版日期:</strong> {{ formatDate(selectedBook.publishDate) }}</p>
                      <p><strong>ISBN:</strong> {{ selectedBook.details.isbn }}</p>
                      <p><strong>页数:</strong> {{ selectedBook.details.pages }}</p>
                      <p><strong>出版社:</strong> {{ selectedBook.details.publisher }}</p>
                    </div>
                    <div :class="$style.detailTags">
                      <span
                        v-for="tag in selectedBook.details.tags"
                        :key="tag"
                        :class="$style.tag"
                      >
                        #{{ tag }}
                      </span>
                    </div>
                    <div :class="$style.actionButtons">
                      <router-link
                        :to="{ name: 'book-reader', params: { id: selectedBook.id }, query: { chapter: 0 } }"
                        :class="$style.readButton"
                        @click="closeBookDetails"
                      >
                        开始阅读
                      </router-link>
                    </div>
                  </div>
                </div>

                <div :class="$style.bookDetailBody">
                  <div v-if="selectedBook.dedication" :class="$style.dedicationSection">
                    <h3 :class="$style.sectionTitle">献词</h3>
                    <div :class="$style.dedicationText" v-html="formatDedication(selectedBook.dedication)"></div>
                  </div>

                  <div :class="$style.summarySection">
                    <h3 :class="$style.sectionTitle">内容简介</h3>
                    <div :class="$style.summaryText" v-html="renderMarkdown(selectedBook.summary)"></div>
                  </div>

                  <div :class="$style.chaptersSection">
                    <h3 :class="$style.sectionTitle">目录</h3>
                    <div v-if="selectedBook.chapters.length > 10" :class="$style.chaptersContainer">
                      <div :class="$style.chaptersColumn">
                        <ul :class="$style.chaptersList">
                          <li v-for="(chapter, idx) in selectedBook.chapters.slice(0, Math.ceil(selectedBook.chapters.length / 2))" :key="idx">
                            {{ chapter }}
                          </li>
                        </ul>
                      </div>
                      <div :class="$style.chaptersColumn">
                        <ul :class="$style.chaptersList">
                          <li v-for="(chapter, idx) in selectedBook.chapters.slice(Math.ceil(selectedBook.chapters.length / 2))" :key="idx + Math.ceil(selectedBook.chapters.length / 2)">
                            {{ chapter }}
                          </li>
                        </ul>
                      </div>
                    </div>
                    <ul v-else :class="$style.chaptersList">
                      <li v-for="(chapter, idx) in selectedBook.chapters" :key="idx">
                        {{ chapter }}
                      </li>
                    </ul>
                  </div>

                  <div :class="$style.reviewsSection">
                    <h3 :class="$style.sectionTitle">评论摘选</h3>
                    <div
                      v-for="(review, idx) in selectedBook.reviews"
                      :key="idx"
                      :class="$style.reviewItem"
                    >
                      <blockquote :class="$style.reviewQuote">
                        {{ review.content }}
                      </blockquote>
                      <blockquote v-if="review.translation" :class="$style.reviewTranslation">
                        {{ review.translation }}
                      </blockquote>
                      <div :class="$style.reviewAuthor">
                        <span>{{ review.author }}</span>
                        <span :class="$style.reviewSource">{{ review.source }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Transition>
      </Teleport>
    </div>
  </MainLayout>
</template>

<script setup>
import { ref, computed } from 'vue';
import MainLayout from '@/components/layout/MainLayout.vue';
import { books } from '@/data/books';
import { renderMarkdown, formatDedication } from '@/utils/markdown';

const selectedBookId = ref(null);

// 截断文本，保留指定长度
const truncateText = (text, maxLength) => {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  // 如果只有年月，则只显示年月
  if (dateString.split('-').length === 2 || dateString.split('/').length === 2) {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long'
    });
  }
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// 计算属性：获取选中的书籍详情
const selectedBook = computed(() => {
  if (!selectedBookId.value) return null;
  return books.find(book => book.id === selectedBookId.value);
});

// 打开书籍详情
const openBookDetails = (bookId) => {
  selectedBookId.value = bookId;
  document.body.style.overflow = 'hidden'; // 禁止背景滚动
};

// 关闭书籍详情
const closeBookDetails = () => {
  selectedBookId.value = null;
  document.body.style.overflow = ''; // 恢复背景滚动
};
</script>

<style module>
.booksContainer {
  max-width: 900px; /* 减小最大宽度，增加两侧留白 */
  margin: 0 auto;
  padding: var(--space-xl) var(--space-l); /* 增加上下内边距，添加左右内边距 */
  grid-column: 1 / -1; /* 在网格布局中占据所有列 */
  position: relative; /* 为可能的装饰元素做准备 */
}

.pageHeader {
  text-align: center;
  margin-bottom: var(--space-xxl); /* 增加与内容的间距 */
  margin-top: var(--space-l); /* 向上提高位置 */
  position: relative;
}

.pageHeader::after {
  content: '';
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(
    to right,
    rgba(var(--color-accent-rgb), 0.2),
    rgba(var(--color-accent-rgb), 0.8),
    rgba(var(--color-accent-rgb), 0.2)
  );
}

.pageTitle {
  font-family: var(--font-family-heading);
  font-size: 2.8rem; /* 增大标题字号 */
  color: var(--color-text-primary);
  margin-bottom: var(--space-s);
  font-weight: var(--font-weight-heading-h1);
  letter-spacing: var(--letter-spacing-heading);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2); /* 添加文字阴影增强视觉效果 */
}

.pageDescription {
  font-size: 1.2rem; /* 增大描述文字字号 */
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
  line-height: var(--line-height-base);
  opacity: 0.9; /* 微调透明度 */
  letter-spacing: 0.01em; /* 增加字间距 */
}

.booksList {
  display: flex;
  flex-direction: column;
  gap: var(--space-xxl); /* 增加卡片之间的间距 */
  margin: var(--space-xl) 0; /* 增加列表与页面标题的间距 */
}

.bookCard {
  display: flex;
  background: rgba(30, 30, 44, 0.6);
  border-radius: calc(var(--border-radius-soft) * 1.2); /* 稍微增大圆角 */
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1); /* 更平滑的过渡效果 */
  cursor: pointer;
  animation: fadeInUp 0.6s ease-out both;
  height: 320px; /* 增加卡片高度，确保内容有足够空间 */
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.03);
  position: relative;
}

.bookCard::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  box-shadow: 0 5px 30px rgba(var(--color-accent-rgb), 0.05);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.bookCard:hover {
  transform: translateY(-5px);
  box-shadow:
    0 12px 30px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(var(--color-accent-rgb), 0.2);
}

.bookCard:hover::after {
  opacity: 1;
}

.bookCover {
  flex: 0 0 200px; /* 增加封面宽度 */
  height: 320px; /* 匹配新的卡片高度 */
  overflow: hidden;
  position: relative;
  border-radius: var(--border-radius-soft);
  box-shadow:
    0 5px 15px rgba(0, 0, 0, 0.2),
    0 0 5px rgba(0, 0, 0, 0.1) inset;
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.3s ease;
}

.coverImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s ease;
  filter: brightness(0.95); /* 微调亮度，增加质感 */
}

.bookCard:hover .bookCover {
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.25),
    0 0 8px rgba(var(--color-accent-rgb), 0.1) inset;
  border-color: rgba(var(--color-accent-rgb), 0.15);
}

.bookCard:hover .coverImage {
  transform: scale(1.05);
  filter: brightness(1.05); /* 悬停时增加亮度 */
}

.bookInfo {
  flex: 1;
  padding: var(--space-m) var(--space-m) var(--space-s) var(--space-m); /* 恢复顶部内边距 */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 隐藏溢出内容 */
  position: relative;
  background: linear-gradient(
    to right,
    rgba(30, 30, 44, 0.8),
    rgba(30, 30, 44, 0.6)
  ); /* 调整渐变背景 */
  justify-content: space-between; /* 确保内容均匀分布 */
}

.bookTitle {
  font-family: var(--font-family-heading);
  font-size: 1.4rem; /* 保持适中字号 */
  color: var(--color-text-primary);
  margin: 0 0 var(--space-s) 0; /* 适当增加底部间距 */
  font-weight: var(--font-weight-heading-h2);
  line-height: 1.3; /* 增加行高，提高可读性 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 限制标题最多显示2行 */
  line-clamp: 2; /* 标准属性 */
  -webkit-box-orient: vertical;
  letter-spacing: 0.01em; /* 添加微小字间距 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* 添加微妙的文字阴影 */
  min-height: auto; /* 移除固定高度，让标题自然流动 */
  padding-right: var(--space-s); /* 右侧留出一些空间 */
  max-height: 3.7rem; /* 稍微增加最大高度 */
}

.bookMeta {
  color: var(--color-text-secondary);
  font-size: 0.85rem; /* 保持适中字号 */
  margin-bottom: var(--space-s); /* 增加与摘要的间距 */
  opacity: 0.9;
  display: flex;
  flex-direction: column;
  gap: 4px; /* 增加行间距 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.05); /* 添加微妙的分隔线 */
  padding-bottom: var(--space-xs); /* 保持底部内边距 */
}

.publishDate {
  display: inline-block;
}

.bookSummary {
  color: var(--color-text-secondary);
  line-height: 1.6; /* 增加行高，提高可读性 */
  margin-bottom: var(--space-s); /* 增加底部间距 */
  flex-grow: 1;
  overflow: hidden;
  position: relative;
  font-size: 0.85rem; /* 保持适中字号 */
  max-height: 120px; /* 调整摘要高度 */
  padding-bottom: var(--space-s); /* 增加底部内边距，避免文本与渐变遮罩重叠 */
}

/* 添加渐变遮罩，使文本优雅地消失 */
.bookSummary::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 40px; /* 增加高度，使渐变更加自然 */
  background: linear-gradient(
    to bottom,
    rgba(30, 30, 44, 0),
    rgba(30, 30, 44, 0.8)
  );
  pointer-events: none;
  z-index: 1; /* 确保在按钮下层 */
}

.bookActions {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
  margin-bottom: 12px;
  gap: var(--space-m);
  z-index: 2;
}

.viewDetailsButton {
  background: rgba(var(--color-accent-rgb), 0.08);
  border: 1px solid var(--color-accent);
  color: var(--color-accent);
  padding: 6px 16px; /* 增加内边距，使按钮更加突出 */
  border-radius: var(--border-radius-soft);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  font-size: 0.9rem; /* 增加字号 */
  letter-spacing: 0.01em; /* 添加微小字间距 */
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  font-weight: 500; /* 稍微加粗 */
  white-space: nowrap; /* 防止文本换行 */
  flex: 1;
}

.cardReadButton {
  background: var(--color-accent);
  border: 1px solid var(--color-accent);
  color: #fff;
  padding: 6px 16px;
  border-radius: var(--border-radius-soft);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  font-size: 0.9rem;
  letter-spacing: 0.01em;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  font-weight: 500;
  white-space: nowrap;
  text-decoration: none;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.viewDetailsButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(var(--color-accent-rgb), 0.1),
    rgba(var(--color-accent-rgb), 0)
  );
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.viewDetailsButton:hover {
  background-color: rgba(var(--color-accent-rgb), 0.15);
  color: var(--color-accent-hover);
  border-color: var(--color-accent-hover);
  box-shadow: 0 4px 15px rgba(var(--color-accent-rgb), 0.2);
  transform: translateY(-2px);
}

.viewDetailsButton:hover::before {
  transform: translateX(0);
}

.cardReadButton:hover {
  background-color: rgba(var(--color-accent-rgb), 0.9);
  color: #fff;
  border-color: var(--color-accent-hover);
  box-shadow: 0 4px 15px rgba(var(--color-accent-rgb), 0.3);
  transform: translateY(-2px);
}

.cardReadButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0)
  );
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.cardReadButton:hover::before {
  transform: translateX(0);
}

/* 模态框样式 */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(10, 10, 15, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(8px);
  padding: var(--space-m);
}

.modalContainer {
  position: relative;
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  background: rgba(25, 25, 25, 0.95);
  border-radius: var(--border-radius-soft);
  padding: var(--space-xl);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
}

.closeButton {
  position: absolute;
  top: var(--space-m);
  right: var(--space-m);
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.05);
  color: var(--color-text-secondary);
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-radius: 50%;
  z-index: 10;
}

.closeButton:hover {
  color: var(--color-text-primary);
  background: rgba(255, 255, 255, 0.1);
}

.bookDetailHeader {
  display: flex;
  margin-bottom: var(--space-xl);
  gap: var(--space-l);
}

.bookDetailCover {
  flex: 0 0 250px;
  position: relative;
  padding: var(--space-xs);
  background: rgba(255, 255, 255, 0.02);
  border-radius: calc(var(--border-radius-soft) * 1.2);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.detailCoverImage {
  width: 100%;
  border-radius: var(--border-radius-soft);
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.35),
    0 5px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.5s ease;
  filter: brightness(0.95);
}

.bookDetailCover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(var(--color-accent-rgb), 0.1),
    transparent 50%
  );
  border-radius: calc(var(--border-radius-soft) * 1.2);
  opacity: 0.5;
  pointer-events: none;
}

.bookDetailInfo {
  flex: 1;
}

.detailTitle {
  font-family: var(--font-family-heading);
  font-size: 2rem;
  color: var(--color-text-primary);
  margin-bottom: var(--space-m);
}

.detailMeta {
  color: var(--color-text-secondary);
  margin-bottom: var(--space-m);
}

.detailMeta p {
  margin: var(--space-xs) 0;
}

.detailTags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-s);
  margin-top: var(--space-m);
}

.tag {
  display: inline-block;
  padding: var(--space-xs) var(--space-s);
  background: rgba(var(--color-accent-rgb), 0.1);
  color: var(--color-accent);
  border-radius: var(--border-radius-soft);
  font-size: 0.85rem;
}

.actionButtons {
  margin-top: var(--space-l);
  display: flex;
  gap: var(--space-m);
}

.readButton {
  display: inline-block;
  padding: var(--space-s) var(--space-l);
  background: var(--color-accent);
  color: #fff;
  border-radius: var(--border-radius-soft);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: all 0.3s ease;
  text-align: center;
  border: 1px solid var(--color-accent);
  box-shadow: 0 4px 10px rgba(var(--color-accent-rgb), 0.2);
}

.readButton:hover {
  background: rgba(var(--color-accent-rgb), 0.9);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(var(--color-accent-rgb), 0.3);
}

.bookDetailBody {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
}

.sectionTitle {
  font-family: var(--font-family-heading);
  font-size: 1.3rem;
  color: var(--color-accent);
  margin-bottom: var(--space-m);
  position: relative;
  padding-bottom: var(--space-xs);
}

.sectionTitle::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: var(--color-accent);
}

.summaryText {
  line-height: 1.8;
  color: var(--color-text-primary);
  font-size: 1rem;
  letter-spacing: 0.01em;
}

/* Markdown 样式 */
.summaryText p {
  margin-bottom: var(--space-m);
}

.summaryText strong {
  color: var(--color-text-primary);
  font-weight: 600;
}

.summaryText em {
  font-style: italic;
  color: rgba(var(--color-accent-rgb), 0.9);
}

.summaryText ul, .summaryText ol {
  margin: var(--space-m) 0;
  padding-left: var(--space-l);
}

.summaryText li {
  margin-bottom: var(--space-xs);
}

.summaryText blockquote {
  border-left: 3px solid var(--color-accent);
  padding-left: var(--space-m);
  margin: var(--space-m) 0;
  font-style: italic;
  color: var(--color-text-secondary);
}

.chaptersContainer {
  display: flex;
  gap: var(--space-l);
  margin-bottom: var(--space-m);
}

.chaptersColumn {
  flex: 1;
}

.chaptersList {
  list-style-type: none;
  padding: 0;
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-m);
}

.chaptersList li {
  padding: var(--space-s) var(--space-m);
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--border-radius-soft);
  border-left: 2px solid var(--color-accent);
  transition: all 0.2s ease;
  word-break: break-word;
}

.chaptersList li:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateX(2px);
}

.reviewItem {
  margin-bottom: var(--space-l);
  padding: var(--space-m);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--border-radius-soft);
}

.reviewQuote {
  margin: 0 0 var(--space-m) 0;
  padding: 0 0 0 var(--space-m);
  border-left: 2px solid rgba(var(--color-accent-rgb), 0.5);
  font-style: italic;
  color: var(--color-text-primary);
}

.reviewTranslation {
  margin: 0 0 var(--space-m) 0;
  padding: 0 0 0 var(--space-m);
  border-left: 2px solid rgba(var(--color-accent-rgb), 0.3);
  font-style: italic;
  color: var(--color-text-secondary);
  font-size: 0.95rem;
}

.reviewAuthor {
  text-align: right;
  color: var(--color-text-secondary);
}

.reviewSource {
  display: block;
  font-size: 0.85rem;
  opacity: 0.8;
}

.author {
  display: block;
  margin-bottom: var(--space-xs);
}

.dedicationSection {
  margin-bottom: var(--space-xl);
  padding: var(--space-m);
  background: rgba(var(--color-accent-rgb), 0.05);
  border-radius: var(--border-radius-soft);
  border-left: 2px solid var(--color-accent);
  max-width: 100%;
  overflow-wrap: break-word;
}

.dedicationText {
  font-style: italic;
  line-height: 1.8;
  color: var(--color-text-primary);
  text-align: center;
  max-width: 100%;
  overflow-wrap: break-word;
  word-break: break-word;
}

/* 动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式样式 */
@media (max-width: 768px) {
  .booksContainer {
    padding: var(--space-l) var(--space-m); /* 调整移动端内边距 */
  }

  .pageHeader {
    margin-top: 0; /* 移动端不需要向上提高 */
  }

  .pageTitle {
    font-size: 2.2rem; /* 移动端减小标题字号 */
  }

  .booksList {
    gap: var(--space-xl); /* 移动端减小卡片间距 */
  }

  .bookCard {
    flex-direction: column;
    height: auto; /* 移动端不限制高度 */
    margin-bottom: var(--space-m);
  }

  .bookCover {
    flex: 0 0 auto;
    height: 240px; /* 增加移动端封面高度 */
    width: 100%;
    border-radius: var(--border-radius-soft) var(--border-radius-soft) 0 0; /* 只有顶部圆角 */
  }

  .bookInfo {
    padding: var(--space-m);
    border-radius: 0 0 var(--border-radius-soft) var(--border-radius-soft); /* 只有底部圆角 */
  }

  .bookTitle {
    min-height: auto; /* 移动端不需要固定高度 */
    font-size: 1.2rem; /* 进一步减小字号 */
    margin: 0 0 var(--space-xs) 0;
    max-height: none;
  }

  .bookMeta {
    margin-bottom: var(--space-xs);
    padding-bottom: var(--space-xs);
    font-size: 0.8rem;
  }

  .bookSummary {
    max-height: 80px; /* 减小移动端摘要高度 */
    font-size: 0.8rem;
    margin-bottom: var(--space-xs);
    line-height: 1.4;
  }

  .bookSummary::after {
    height: 25px; /* 调整渐变高度 */
  }

  .viewDetailsButton {
    margin-bottom: 6px;
    padding: 3px 10px;
    font-size: 0.8rem;
  }

  .bookDetailHeader {
    flex-direction: column;
  }

  .bookDetailCover {
    flex: 0 0 auto;
    margin-bottom: var(--space-l);
    max-width: 200px;
    margin-left: auto;
    margin-right: auto;
  }

  .detailTitle {
    font-size: 1.8rem;
    text-align: center;
  }

  .chaptersContainer {
    flex-direction: column;
    gap: var(--space-m);
  }

  .chaptersList {
    grid-template-columns: 1fr;
  }

  .modalContainer {
    padding: var(--space-m);
    max-height: 95vh;
  }

  .dedicationText {
    font-size: 0.95rem;
  }

  .summaryText {
    font-size: 0.95rem;
  }

  .reviewItem {
    padding: var(--space-s);
  }

  .bookActions {
    flex-direction: column;
    width: 100%;
    gap: var(--space-s);
  }

  .viewDetailsButton, .cardReadButton {
    width: 100%;
    text-align: center;
    justify-content: center;
  }
}

/* 平板设备 */
@media (min-width: 769px) and (max-width: 1024px) {
  .booksContainer {
    max-width: 800px; /* 平板设备减小容器宽度 */
    padding: var(--space-xl) var(--space-l);
  }

  .bookCard {
    height: 300px; /* 平板设备适当减小高度 */
  }

  .bookCover {
    flex: 0 0 180px; /* 平板设备适当减小封面宽度 */
    height: 300px; /* 匹配卡片高度 */
  }

  .bookInfo {
    padding: var(--space-s) var(--space-m);
  }

  .bookTitle {
    font-size: 1.3rem; /* 平板设备减小标题字号 */
    min-height: auto; /* 移除固定高度 */
    margin: 0 0 var(--space-xs) 0;
    max-height: 3.2rem;
  }

  .bookMeta {
    margin-bottom: var(--space-xs);
    font-size: 0.8rem;
    padding-bottom: 4px;
  }

  .bookSummary {
    max-height: 110px; /* 调整摘要高度 */
    font-size: 0.85rem;
    line-height: 1.45;
  }

  .viewDetailsButton {
    padding: 3px 10px;
    font-size: 0.8rem;
    margin-bottom: 6px;
  }

  .modalContainer {
    max-width: 90%;
  }
}

/* 大屏幕设备 */
@media (min-width: 1025px) and (max-width: 1440px) {
  .booksContainer {
    max-width: 850px; /* 适中的容器宽度 */
  }
}

/* 超大屏幕设备 */
@media (min-width: 1441px) {
  .booksContainer {
    max-width: 900px; /* 保持较大留白 */
  }

  .booksList {
    gap: calc(var(--space-xxl) + var(--space-m)); /* 大屏幕增加间距 */
  }
}
</style>

<style>
/* 全局模态框动画 */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}
</style>
