<template>
  <div :class="$style.articleList">
    <ArticleCard
      v-for="(article, index) in filteredArticles"
      :key="article.slug"
      :article="article"
      :index="index"
      @open="openArticle"
    />
    <ArticleModal
      :slug="selectedSlug"
      :is-open="isModalOpen"
      @close="closeModal"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import ArticleCard from './ArticleCard.vue';
import ArticleModal from './ArticleModal.vue';
import { articles } from '@/data/articles';

const selectedSlug = ref('');
const isModalOpen = ref(false);

const props = defineProps({
  selectedYear: {
    type: String,
    default: ''
  },
  selectedCategory: {
    type: String,
    default: ''
  }
});

const filteredArticles = computed(() => {
  return articles.filter(article => {
    const matchesYear = !props.selectedYear || article.date.startsWith(props.selectedYear);
    const matchesCategory = !props.selectedCategory || article.categories.includes(props.selectedCategory);
    return matchesYear && matchesCategory;
  });
});

const openArticle = (slug) => {
  selectedSlug.value = slug;
  isModalOpen.value = true;
};

const closeModal = () => {
  isModalOpen.value = false;
};
</script>

<style module>
.articleList {
  display: flex;
  flex-direction: column;
  gap: var(--space-l);
  width: 100%;
  max-width: 650px;
}
</style>