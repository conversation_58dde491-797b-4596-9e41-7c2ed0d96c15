{"name": "moyin-blog", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build:hash": "node build-hash-mode.js"}, "dependencies": {"front-matter": "^4.0.2", "markdown-it": "^14.1.0", "pinia": "^2.1.7", "vue": "^3.4.21", "vue-easy-lightbox": "^1.19.0", "vue-router": "^4.3.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "postcss-nesting": "^12.1.2", "vite": "^5.2.8"}}