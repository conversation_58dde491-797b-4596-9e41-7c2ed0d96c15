import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3001/api';

// 测试同步功能
async function testSyncFunction() {
  console.log('🔄 开始测试数据同步功能...\n');

  try {
    // 1. 登录获取token
    console.log('1. 登录获取token');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123456'
      })
    });

    const loginResult = await loginResponse.json();
    if (!loginResult.success) {
      throw new Error('登录失败');
    }

    const token = loginResult.data.token;
    console.log('✓ 登录成功');

    // 2. 创建测试文章
    console.log('\n2. 创建测试文章');
    const testArticle = {
      title: '同步测试文章',
      slug: 'sync-test-' + Date.now(),
      description: '这是一篇用于测试同步功能的文章',
      content: '# 同步测试文章\n\n这是测试内容，用于验证同步功能是否正常工作。\n\n## 功能特性\n\n- 自动同步到前端\n- 生成Markdown文件\n- 更新articles.js数据文件',
      status: 'draft',
      featured: false
    };

    const createResponse = await fetch(`${BASE_URL}/articles`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(testArticle)
    });

    const createResult = await createResponse.json();
    if (!createResult.success) {
      throw new Error('创建文章失败');
    }

    const articleId = createResult.data.id;
    console.log('✓ 文章创建成功，ID:', articleId);

    // 3. 发布文章（触发自动同步）
    console.log('\n3. 发布文章（触发自动同步）');
    const publishResponse = await fetch(`${BASE_URL}/articles/${articleId}/publish`, {
      method: 'POST',
      headers: { 
        'Authorization': `Bearer ${token}`
      }
    });

    const publishResult = await publishResponse.json();
    if (!publishResult.success) {
      throw new Error('发布文章失败');
    }
    console.log('✓ 文章发布成功，应该已触发自动同步');

    // 等待同步完成
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 4. 测试手动同步API
    console.log('\n4. 测试手动同步API');
    const syncResponse = await fetch(`${BASE_URL}/articles/sync`, {
      method: 'POST',
      headers: { 
        'Authorization': `Bearer ${token}`
      }
    });

    const syncResult = await syncResponse.json();
    if (syncResult.success) {
      console.log('✓ 手动同步成功:', syncResult.message);
    } else {
      console.log('✗ 手动同步失败:', syncResult.message);
    }

    // 5. 检查生成的文件
    console.log('\n5. 检查生成的文件');
    
    // 检查articles.js文件
    try {
      const fs = await import('fs/promises');
      const path = await import('path');
      
      const articlesPath = path.resolve('../src/data/articles.js');
      const articlesContent = await fs.readFile(articlesPath, 'utf8');
      
      if (articlesContent.includes(testArticle.title)) {
        console.log('✓ articles.js文件已更新，包含新文章');
      } else {
        console.log('✗ articles.js文件未包含新文章');
      }
    } catch (error) {
      console.log('⚠ 无法检查articles.js文件:', error.message);
    }

    // 检查Markdown文件
    try {
      const fs = await import('fs/promises');
      const path = await import('path');
      
      const markdownPath = path.resolve(`../public/articles/${testArticle.slug}.md`);
      const markdownContent = await fs.readFile(markdownPath, 'utf8');
      
      if (markdownContent.includes(testArticle.title)) {
        console.log('✓ Markdown文件已生成');
      } else {
        console.log('✗ Markdown文件内容不正确');
      }
    } catch (error) {
      console.log('⚠ 无法检查Markdown文件:', error.message);
    }

    // 6. 取消发布（测试删除同步）
    console.log('\n6. 取消发布（测试删除同步）');
    const unpublishResponse = await fetch(`${BASE_URL}/articles/${articleId}/unpublish`, {
      method: 'POST',
      headers: { 
        'Authorization': `Bearer ${token}`
      }
    });

    const unpublishResult = await unpublishResponse.json();
    if (unpublishResult.success) {
      console.log('✓ 文章取消发布成功，应该已删除前端文件');
    }

    // 等待同步完成
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 7. 清理测试数据
    console.log('\n7. 清理测试数据');
    const deleteResponse = await fetch(`${BASE_URL}/articles/${articleId}`, {
      method: 'DELETE',
      headers: { 
        'Authorization': `Bearer ${token}`
      }
    });

    const deleteResult = await deleteResponse.json();
    if (deleteResult.success) {
      console.log('✓ 测试文章已删除');
    }

    console.log('\n🎉 同步功能测试完成！');

  } catch (error) {
    console.error('❌ 同步功能测试失败:', error.message);
  }
}

// 运行测试
testSyncFunction();
