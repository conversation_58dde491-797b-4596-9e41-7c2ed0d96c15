<template>
  <Teleport to="body">
    <Transition name="modal-fade">
      <div v-if="props.isOpen" :class="$style.modalOverlay" @click.self="closeModal">
        <div :class="$style.modalContainer">
          <button :class="$style.closeButton" @click="closeModal" aria-label="关闭文章">×</button>
          <div v-if="loading" :class="$style.loading">
            <div :class="$style.loadingSpinner"></div>
          </div>
          <div v-else-if="error" :class="$style.error">{{ error }}</div>
          <template v-else-if="article">
            <div :class="$style.articleContent">
              <h1 :class="$style.title">{{ article.title }}</h1>
              <div :class="$style.metadata">
                <span v-if="article.readingTime">{{ article.readingTime }} 分钟阅读</span>
              </div>
              <div :class="$style.content" v-html="article.content" ref="contentRef"></div>
            </div>
            <div :class="$style.articleSidebar">
              <div v-if="article.tags?.length" :class="$style.tags">
                <h3 :class="$style.sidebarTitle">标签</h3>
                <div :class="$style.tagList">
                  <span v-for="tag in article.tags" :key="tag" :class="$style.tag">#{{ tag }}</span>
                </div>
              </div>

              <!-- 文章统计信息 -->
              <div v-if="article.reading_time || article.word_count" :class="$style.articleStats">
                <h3 :class="$style.sidebarTitle">文章信息</h3>
                <div :class="$style.statsContent">
                  <p v-if="article.reading_time">
                    <span :class="$style.statLabel">阅读时间:</span>
                    <span :class="$style.statValue">{{ article.reading_time }} 分钟</span>
                  </p>
                  <p v-if="article.word_count">
                    <span :class="$style.statLabel">字数:</span>
                    <span :class="$style.statValue">{{ article.word_count }} 字</span>
                  </p>
                </div>
              </div>

              <!-- 辅助介绍 -->
              <div v-if="article.auxiliary_content" :class="$style.auxiliaryContent">
                <h3 :class="$style.sidebarTitle">文章简介</h3>
                <div :class="$style.auxiliaryText" v-html="formatAuxiliaryContent(article.auxiliary_content)"></div>
              </div>

              <!-- 文章配图 -->
              <div v-if="article.articleImages?.length" :class="$style.imageGallery">
                <h3 :class="$style.sidebarTitle">文章配图</h3>
                <div :class="$style.imageGrid">
                  <img
                    v-for="(img, idx) in article.articleImages"
                    :key="idx"
                    :src="img.url"
                    :alt="img.alt_text || img.original_name || '配图' + (idx + 1)"
                    loading="lazy"
                    @click="openImageViewer(idx, article.articleImages.map(i => i.url))"
                    :class="$style.galleryImage"
                  />
                </div>
              </div>

              <!-- 兼容旧的images字段 -->
              <div v-else-if="article.images?.length" :class="$style.imageGallery">
                <h3 :class="$style.sidebarTitle">文章配图</h3>
                <div :class="$style.imageGrid">
                  <img
                    v-for="(img, idx) in article.images"
                    :key="idx"
                    :src="img"
                    :alt="'配图' + (idx + 1)"
                    loading="lazy"
                    @click="openImageViewer(idx, article.images)"
                    :class="$style.galleryImage"
                  />
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </Transition>
    
    <!-- 添加图片查看器组件 -->
    <ImageViewer ref="imageViewerRef" />
  </Teleport>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { useArticle } from '@/composables/useArticle';
import ArticleMetadata from './ArticleMetadata.vue';
import ArticleTags from './ArticleTags.vue';
import ImageViewer from '@/components/common/ImageViewer.vue';

const props = defineProps({
  slug: {
    type: String,
    required: true
  },
  isOpen: {
    type: Boolean,
    required: true
  }
});

console.log('ArticleModal接收到slug:', props.slug);

const emit = defineEmits(['close']);

const { article, loading, error, loadArticle } = useArticle();
const imageViewerRef = ref(null);
const contentRef = ref(null);

// 图片查看器相关方法
const openImageViewer = (index, images) => {
  if (imageViewerRef.value) {
    imageViewerRef.value.setImages(images, index);
    imageViewerRef.value.open(index);
  }
};

// 格式化辅助内容
const formatAuxiliaryContent = (content) => {
  if (!content) return '';
  // 简单的换行处理
  return content.replace(/\n/g, '<br>');
};

// 处理文章内容中的图片点击
const setupContentImageViewer = () => {
  if (contentRef.value) {
    const images = contentRef.value.querySelectorAll('img');
    
    // 收集所有图片URL
    const imageUrls = Array.from(images).map(img => img.src);
    
    images.forEach((img, index) => {
      // 为图片添加点击事件
      img.style.cursor = 'zoom-in';
      img.addEventListener('click', (event) => {
        event.preventDefault();
        
        if (imageViewerRef.value) {
          imageViewerRef.value.setImages(imageUrls, index);
          imageViewerRef.value.open(index);
        }
      });
    });
  }
};

watch(article, async (val) => {
  console.log('ArticleModal加载的文章内容:', val);
  if (val) {
    // 等待DOM更新后处理图片点击事件
    await nextTick();
    setupContentImageViewer();
  }
});

const closeModal = () => {
  emit('close');
};

// 处理ESC键关闭模态框
const handleKeydown = (event) => {
  if (event.key === 'Escape' && props.isOpen) {
    closeModal();
  }
};

// 当模态框打开时禁止背景滚动
watch(() => props.isOpen, (newValue) => {
  if (newValue) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = '';
  }
});

watch(() => props.slug, (newSlug) => {
  if (newSlug) {
    loadArticle(newSlug);
  }
});

import { watchEffect } from 'vue';

watchEffect(() => {
  if (props.slug) {
    loadArticle(props.slug);
  }
});

onMounted(() => {
  window.addEventListener('keydown', handleKeydown);
});

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleKeydown);
  // 确保在组件销毁时恢复滚动
  document.body.style.overflow = '';
});
</script>

<style module>
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(30, 30, 30, 0.97); /* 使用暖调深炭灰 */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(12px);
  padding: var(--space-m);
}

.modalContainer {
  position: relative;
  max-width: 1200px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  background: rgba(25, 25, 25, 0.8); /* 稍深的背景色 */
  border-radius: var(--border-radius-soft);
  padding: 0;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
  display: grid;
  grid-template-columns: 65% 35%;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
}

.modalContainer::-webkit-scrollbar {
  width: 6px;
}

.modalContainer::-webkit-scrollbar-track {
  background: transparent;
}

.modalContainer::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.closeButton {
  position: absolute;
  top: var(--space-m);
  right: var(--space-m);
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.05);
  color: var(--color-text-secondary);
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-radius: 50%;
  backdrop-filter: blur(4px);
  z-index: 10;
}

.closeButton:hover {
  color: var(--color-text-primary);
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.articleContent {
  padding: var(--space-xl);
  font-family: "Inter", var(--font-family-body);
  line-height: var(--line-height-article, 1.8);
  letter-spacing: 0.02em;
  color: rgba(224, 224, 224, 0.95); /* 微调文本颜色透明度 */
  max-width: 65ch; /* 控制阅读宽度 */
  margin: 0 auto;
}

.articleSidebar {
  background: rgba(255, 255, 255, 0.02);
  padding: var(--space-xl);
  border-left: 1px solid rgba(255, 255, 255, 0.05);
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  grid-column: 1 / -1;
  padding: var(--space-xl);
}

.title {
  font-family: "Playfair Display", var(--font-family-heading);
  font-size: calc(1rem * var(--font-scale-ratio) * var(--font-scale-ratio) * var(--font-scale-ratio));
  font-weight: 300;
  letter-spacing: -0.02em;
  color: #F5F5DC; /* 浅米色标题 */
  margin: 0 0 var(--space-m);
  line-height: 1.3;
  text-align: center;
}

.metadata {
  display: flex;
  gap: var(--space-m);
  color: rgba(224, 224, 224, 0.7); /* 降低对比度的次要文本 */
  font-size: 0.9rem;
  font-weight: var(--font-weight-light, 300);
  letter-spacing: var(--letter-spacing-meta, 0.03em);
  margin-bottom: var(--space-l);
  flex-wrap: wrap;
}

.content {
  font-size: 1.1rem;
  line-height: 1.8;
  color: rgba(224, 224, 224, 0.9);
}

.content h2 {
  font-family: "Playfair Display", var(--font-family-heading);
  font-size: 1.75rem;
  font-weight: 400;
  color: #F5F5DC;
  margin: 2em 0 1em;
  letter-spacing: -0.01em;
}

.content h3 {
  font-family: "Playfair Display", var(--font-family-heading);
  font-size: 1.4rem;
  font-weight: 400;
  color: rgba(245, 245, 220, 0.9);
  margin: 1.5em 0 0.75em;
}

.content p {
  margin: 1.5em 0;
  text-align: justify;
  hyphens: auto;
}

.content p {
  margin-bottom: var(--space-m);
}

.content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: var(--space-l) 0;
  transition: all 0.3s ease;
}

.content a {
  color: #A0522D; /* 赭石色链接 */
  text-decoration: none;
  transition: all 0.3s ease;
}

.content a:hover {
  color: #BDB76B; /* 悬停时变为淡金色 */
  text-decoration-line: underline;
  text-decoration-thickness: 1px;
  text-decoration-style: solid;
  text-underline-offset: 2px;
}

.content blockquote {
  border-left: 2px solid rgba(189, 183, 107, 0.4); /* 淡金色左边框 */
  padding: var(--space-m) var(--space-l);
  margin: 2em 0;
  font-style: italic;
  color: rgba(224, 224, 224, 0.85);
  background: rgba(189, 183, 107, 0.03);
  border-radius: 0 var(--border-radius-soft) var(--border-radius-soft) 0;
}

.content blockquote p {
  margin: 0.75em 0;
  line-height: 1.7;
  font-size: 1.05em;
}

.content ul, .content ol {
  margin: 1.5em 0;
  padding-left: 1.5em;
}

.content li {
  margin: 0.5em 0;
  line-height: 1.6;
}

.content strong {
  color: #F5F5DC;
  font-weight: 500;
}

.content em {
  font-style: italic;
  color: rgba(245, 245, 220, 0.9);
}

.content hr {
  margin: 2.5em 0;
  border: none;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(189, 183, 107, 0.2), transparent);
}

.sidebarTitle {
  font-family: var(--font-family-heading);
  font-size: calc(1rem * var(--font-scale-ratio));
  font-weight: var(--font-weight-heading-h3, 400);
  letter-spacing: var(--letter-spacing-heading);
  color: #F5F5DC; /* 保持与主标题相同的色调 */
  margin: 0 0 var(--space-m);
}

.tagList {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-s);
}

.tag {
  display: inline-block;
  padding: var(--space-xs) var(--space-s);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius-pill);
  color: rgba(224, 224, 224, 0.7);
  font-size: 0.85rem;
  font-weight: var(--font-weight-medium, 500);
  letter-spacing: var(--letter-spacing-meta, 0.03em);
  transition: all 0.3s ease;
  cursor: pointer;
}

.tag:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #E0E0E0;
  transform: translateY(-1px);
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 2px solid rgba(255, 255, 255, 0.05);
  border-top-color: rgba(189, 183, 107, 0.7); /* 淡金色加载指示器 */
  border-radius: 50%;
  animation: spin 1.2s cubic-bezier(0.4, 0.0, 0.2, 1) infinite;
}

.error {
  color: #8B0000; /* 暗调勃艮第红 */
  text-align: center;
  padding: var(--space-xl);
  grid-column: 1 / -1;
  font-family: var(--font-family-body);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 过渡动画 */
:global(.modal-fade-enter-active),
:global(.modal-fade-leave-active) {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

:global(.modal-fade-enter-from),
:global(.modal-fade-leave-to) {
  opacity: 0;
  transform: translateY(10px);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .modalContainer {
    grid-template-columns: 1fr;
    width: 100%;
    max-height: 85vh;
  }

  .articleSidebar {
    border-left: none;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
  }
}
/* 文章统计信息 */
.articleStats {
  margin-top: var(--space-xl);
}

.statsContent {
  margin-top: var(--space-m);
}

.statsContent p {
  margin: var(--space-s) 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statLabel {
  color: rgba(224, 224, 224, 0.7);
  font-size: 0.9rem;
}

.statValue {
  color: rgba(189, 183, 107, 0.9);
  font-weight: var(--font-weight-medium, 500);
}

/* 辅助介绍 */
.auxiliaryContent {
  margin-top: var(--space-xl);
}

.auxiliaryText {
  margin-top: var(--space-m);
  color: rgba(224, 224, 224, 0.8);
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 图片画廊 */
.imageGallery {
  margin-top: var(--space-xl);
}

.imageGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--space-m);
  margin-top: var(--space-m);
}

.galleryImage {
  width: 100%;
  aspect-ratio: 1;
  object-fit: cover;
  border-radius: var(--border-radius-soft);
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: zoom-in;
}

.galleryImage:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 24px rgba(0,0,0,0.4);
}
</style>