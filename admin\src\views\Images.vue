<template>
  <div class="images">
    <div class="page-header">
      <h1>图片管理</h1>
      <div class="header-actions">
        <el-button
          v-if="selectedImages.length > 0"
          type="danger"
          @click="batchDelete"
          :loading="batchDeleting"
        >
          <el-icon><Delete /></el-icon>
          批量删除 ({{ selectedImages.length }})
        </el-button>
        <el-upload
          ref="uploadRef"
          :action="uploadUrl"
          :headers="uploadHeaders"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-progress="handleUploadProgress"
          :before-upload="beforeUpload"
          :show-file-list="false"
          accept="image/*"
          multiple
          :limit="10"
        >
          <el-button type="primary" :loading="uploading">
            <el-icon><Upload /></el-icon>
            {{ uploading ? '上传中...' : '上传图片' }}
          </el-button>
        </el-upload>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="filter-card">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchQuery"
            placeholder="搜索图片..."
            :prefix-icon="Search"
            @input="handleSearch"
            clearable
          />
        </el-col>
        <el-col :span="4">
          <el-select v-model="sortBy" placeholder="排序方式" @change="loadImages">
            <el-option label="最新上传" value="created_at" />
            <el-option label="文件名" value="filename" />
            <el-option label="文件大小" value="size" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select v-model="filterType" placeholder="文件类型" @change="loadImages">
            <el-option label="全部类型" value="" />
            <el-option label="JPEG" value="image/jpeg" />
            <el-option label="PNG" value="image/png" />
            <el-option label="GIF" value="image/gif" />
            <el-option label="WebP" value="image/webp" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button @click="loadImages" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-col>
        <el-col :span="4">
          <el-checkbox v-model="selectAll" @change="handleSelectAll">
            全选
          </el-checkbox>
        </el-col>
      </el-row>
    </el-card>

    <!-- 上传进度 -->
    <el-card v-if="uploadProgress.length > 0" class="upload-progress-card">
      <template #header>
        <span>上传进度</span>
      </template>
      <div v-for="progress in uploadProgress" :key="progress.name" class="upload-item">
        <div class="upload-info">
          <span>{{ progress.name }}</span>
          <span>{{ progress.percent }}%</span>
        </div>
        <el-progress :percentage="progress.percent" :status="progress.status" />
      </div>
    </el-card>

    <!-- 图片网格 -->
    <el-card>
      <div class="images-grid" v-loading="loading">
        <div
          v-for="image in filteredImages"
          :key="image.id"
          class="image-item"
          :class="{ selected: selectedImages.includes(image.id) }"
        >
          <div class="image-checkbox">
            <el-checkbox
              :model-value="selectedImages.includes(image.id)"
              @change="toggleImageSelection(image.id)"
            />
          </div>
          <div class="image-preview" @click="previewImage(image)">
            <img :src="getImageUrl(image)" :alt="image.original_name" />
            <div class="image-overlay">
              <div class="image-actions">
                <el-button
                  size="small"
                  type="primary"
                  circle
                  @click.stop="previewImage(image)"
                  title="预览"
                >
                  <el-icon><ZoomIn /></el-icon>
                </el-button>
                <el-button
                  size="small"
                  type="success"
                  circle
                  @click.stop="copyImageUrl(image)"
                  title="复制链接"
                >
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
                <el-button
                  size="small"
                  type="info"
                  circle
                  @click.stop="showImageSizes(image)"
                  title="复制不同尺寸"
                >
                  <el-icon><PictureFilled /></el-icon>
                </el-button>
                <el-button
                  size="small"
                  type="danger"
                  circle
                  @click.stop="deleteImage(image)"
                  title="删除"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
          <div class="image-info">
            <div class="image-name" :title="image.original_name">{{ image.original_name }}</div>
            <div class="image-meta">
              <span class="image-size">{{ formatFileSize(image.size) }}</span>
              <span class="image-dimensions">{{ image.width }}×{{ image.height }}</span>
              <span class="image-date">{{ formatDate(image.created_at) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination" v-if="totalImages > 0">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalImages"
          :page-sizes="[12, 24, 48, 96]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadImages"
          @current-change="loadImages"
        />
      </div>
    </el-card>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="previewVisible" title="图片预览" width="80%" center>
      <div class="preview-container" v-if="previewImageData">
        <img :src="getImageUrl(previewImageData)" :alt="previewImageData.original_name" class="preview-image" />
        <div class="preview-info">
          <h3>{{ previewImageData.original_name }}</h3>
          <p><strong>尺寸:</strong> {{ previewImageData.width }}×{{ previewImageData.height }}</p>
          <p><strong>大小:</strong> {{ formatFileSize(previewImageData.size) }}</p>
          <p><strong>类型:</strong> {{ previewImageData.mime_type }}</p>
          <p><strong>上传时间:</strong> {{ formatDate(previewImageData.created_at) }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 图片尺寸选择对话框 -->
    <el-dialog v-model="sizesVisible" title="选择图片尺寸" width="60%">
      <div class="sizes-container" v-if="currentImage">
        <div class="size-option" v-for="size in imageSizes" :key="size.key">
          <div class="size-info">
            <strong>{{ size.label }}</strong>
            <span>{{ size.description }}</span>
          </div>
          <el-button @click="copyImageUrlWithSize(currentImage, size.key)">
            复制链接
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Upload,
  Delete,
  Search,
  Refresh,
  ZoomIn,
  CopyDocument,
  PictureFilled
} from '@element-plus/icons-vue'
import api from '@/api'

// 响应式数据
const loading = ref(false)
const uploading = ref(false)
const batchDeleting = ref(false)
const images = ref([])
const selectedImages = ref([])
const selectAll = ref(false)
const searchQuery = ref('')
const sortBy = ref('created_at')
const filterType = ref('')
const currentPage = ref(1)
const pageSize = ref(24)
const totalImages = ref(0)

// 上传进度
const uploadProgress = ref([])

// 对话框状态
const previewVisible = ref(false)
const previewImageData = ref(null)
const sizesVisible = ref(false)
const currentImage = ref(null)

// 图片尺寸选项
const imageSizes = [
  { key: 'original', label: '原图', description: '原始尺寸' },
  { key: 'large', label: '大图', description: '1200px 宽度' },
  { key: 'medium', label: '中图', description: '800px 宽度' },
  { key: 'small', label: '小图', description: '400px 宽度' },
  { key: 'thumbnail', label: '缩略图', description: '200px 宽度' }
]

const uploadUrl = computed(() => '/api/images/upload')
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${localStorage.getItem('admin_token')}`
}))

// 计算属性
const filteredImages = computed(() => {
  let filtered = images.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(image =>
      image.original_name.toLowerCase().includes(query) ||
      image.filename.toLowerCase().includes(query)
    )
  }

  // 类型过滤
  if (filterType.value) {
    filtered = filtered.filter(image => image.mime_type === filterType.value)
  }

  return filtered
})

// 工具函数
const getImageUrl = (image, size = 'original') => {
  // 使用相对路径，因为图片存储在public/articles/img目录下
  if (size === 'original') {
    return `/articles/img/${image.filename}`
  }
  // 对于不同尺寸，暂时返回原图（后续可以实现图片处理）
  return `/articles/img/${image.filename}`
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 加载图片列表
const loadImages = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      sort: sortBy.value,
      search: searchQuery.value,
      type: filterType.value
    }

    const response = await api.get('/images', { params })
    images.value = response.data.data.images || []
    totalImages.value = response.data.data.total || 0
  } catch (error) {
    console.error('获取图片列表失败:', error)
    ElMessage.error('获取图片列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  loadImages()
}

// 上传前验证
const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('图片大小不能超过 10MB!')
    return false
  }

  // 添加到上传进度列表
  uploadProgress.value.push({
    name: file.name,
    percent: 0,
    status: ''
  })

  uploading.value = true
  return true
}

// 上传进度
const handleUploadProgress = (event, file) => {
  const progress = uploadProgress.value.find(p => p.name === file.name)
  if (progress) {
    progress.percent = Math.round(event.percent)
  }
}

// 上传成功
const handleUploadSuccess = (response, file) => {
  const progress = uploadProgress.value.find(p => p.name === file.name)
  if (progress) {
    progress.percent = 100
    progress.status = 'success'
  }

  if (response.success) {
    ElMessage.success(`图片 ${file.name} 上传成功`)
    loadImages()
  } else {
    ElMessage.error(response.message || '上传失败')
  }

  // 延迟移除进度条
  setTimeout(() => {
    const index = uploadProgress.value.findIndex(p => p.name === file.name)
    if (index > -1) {
      uploadProgress.value.splice(index, 1)
    }
    if (uploadProgress.value.length === 0) {
      uploading.value = false
    }
  }, 2000)
}

// 上传失败
const handleUploadError = (error, file) => {
  const progress = uploadProgress.value.find(p => p.name === file.name)
  if (progress) {
    progress.status = 'exception'
  }

  console.error('上传失败:', error)
  ElMessage.error(`图片 ${file.name} 上传失败`)

  setTimeout(() => {
    const index = uploadProgress.value.findIndex(p => p.name === file.name)
    if (index > -1) {
      uploadProgress.value.splice(index, 1)
    }
    if (uploadProgress.value.length === 0) {
      uploading.value = false
    }
  }, 3000)
}

// 图片选择相关
const toggleImageSelection = (imageId) => {
  const index = selectedImages.value.indexOf(imageId)
  if (index > -1) {
    selectedImages.value.splice(index, 1)
  } else {
    selectedImages.value.push(imageId)
  }

  // 更新全选状态
  selectAll.value = selectedImages.value.length === filteredImages.value.length
}

const handleSelectAll = (checked) => {
  if (checked) {
    selectedImages.value = filteredImages.value.map(img => img.id)
  } else {
    selectedImages.value = []
  }
}

// 批量删除
const batchDelete = async () => {
  if (selectedImages.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedImages.value.length} 张图片吗？此操作不可恢复。`,
      '确认批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    batchDeleting.value = true

    // 并发删除
    const deletePromises = selectedImages.value.map(id =>
      api.delete(`/images/${id}`)
    )

    await Promise.all(deletePromises)

    ElMessage.success(`成功删除 ${selectedImages.value.length} 张图片`)
    selectedImages.value = []
    selectAll.value = false
    loadImages()

  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  } finally {
    batchDeleting.value = false
  }
}

// 复制图片链接
const copyImageUrl = async (image) => {
  const url = window.location.origin + getImageUrl(image)
  try {
    await navigator.clipboard.writeText(url)
    ElMessage.success('图片链接已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = url
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('图片链接已复制到剪贴板')
  }
}

// 显示图片尺寸选择
const showImageSizes = (image) => {
  currentImage.value = image
  sizesVisible.value = true
}

// 复制指定尺寸的图片链接
const copyImageUrlWithSize = async (image, size) => {
  const url = window.location.origin + getImageUrl(image, size)
  try {
    await navigator.clipboard.writeText(url)
    ElMessage.success(`${imageSizes.find(s => s.key === size)?.label}链接已复制`)
    sizesVisible.value = false
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 预览图片
const previewImage = (image) => {
  previewImageData.value = image
  previewVisible.value = true
}

// 删除单张图片
const deleteImage = async (image) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除图片"${image.original_name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.delete(`/images/${image.id}`)
    ElMessage.success('图片已删除')

    // 从选中列表中移除
    const index = selectedImages.value.indexOf(image.id)
    if (index > -1) {
      selectedImages.value.splice(index, 1)
    }

    loadImages()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除图片失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

onMounted(() => {
  loadImages()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.filter-card {
  margin-bottom: 20px;
}

.upload-progress-card {
  margin-bottom: 20px;
}

.upload-item {
  margin-bottom: 15px;
}

.upload-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
  min-height: 200px;
}

.image-item {
  border: 2px solid #e6e6e6;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
}

.image-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #409eff;
}

.image-item.selected {
  border-color: #67c23a;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
}

.image-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 2px;
}

.image-preview {
  position: relative;
  height: 180px;
  overflow: hidden;
  cursor: pointer;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.image-actions {
  display: flex;
  gap: 8px;
}

.image-info {
  padding: 12px;
  background: white;
}

.image-name {
  font-weight: 500;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333;
}

.image-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.image-size,
.image-dimensions,
.image-date {
  font-size: 12px;
  color: #909399;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

.preview-container {
  display: flex;
  gap: 20px;
}

.preview-image {
  max-width: 60%;
  max-height: 500px;
  object-fit: contain;
}

.preview-info {
  flex: 1;
}

.preview-info h3 {
  margin-top: 0;
  color: #333;
}

.preview-info p {
  margin: 8px 0;
  color: #666;
}

.sizes-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.size-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  transition: border-color 0.3s ease;
}

.size-option:hover {
  border-color: #409eff;
}

.size-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.size-info strong {
  color: #333;
}

.size-info span {
  font-size: 14px;
  color: #909399;
}
</style>
