import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 前端项目路径
const FRONTEND_PATH = path.resolve(__dirname, '../../../')
const ARTICLES_DATA_PATH = path.join(FRONTEND_PATH, 'src/data/articles.js')
const ARTICLES_DIR_PATH = path.join(FRONTEND_PATH, 'public/articles')

// 同步路径配置
// console.log('FRONTEND_PATH:', FRONTEND_PATH)
// console.log('ARTICLES_DATA_PATH:', ARTICLES_DATA_PATH)
// console.log('ARTICLES_DIR_PATH:', ARTICLES_DIR_PATH)

/**
 * 同步文章数据到前端
 * @param {Object} db - 数据库实例
 */
export async function syncArticlesToFrontend(db) {
  try {
    console.log('开始同步文章数据到前端...')
    
    // 获取所有已发布的文章
    const articles = db.prepare(`
      SELECT
        a.*,
        GROUP_CONCAT(DISTINCT c.name) as category_names,
        GROUP_CONCAT(DISTINCT t.name) as tag_names
      FROM articles a
      LEFT JOIN article_categories ac ON a.id = ac.article_id
      LEFT JOIN categories c ON ac.category_id = c.id
      LEFT JOIN article_tags at ON a.id = at.article_id
      LEFT JOIN tags t ON at.tag_id = t.id
      WHERE a.status = 'published'
      GROUP BY a.id
      ORDER BY a.created_at DESC
    `).all()

    // 为每篇文章获取配图信息
    const getArticleImages = db.prepare(`
      SELECT i.* FROM images i
      JOIN article_images ai ON i.id = ai.image_id
      WHERE ai.article_id = ?
      ORDER BY ai.sort_order
    `);

    articles.forEach(article => {
      article.articleImages = getArticleImages.all(article.id);
    });

    // 生成前端articles.js文件内容，包含新字段
    const articlesData = articles.map(article => ({
      slug: article.slug,
      title: article.title,
      date: new Date(article.created_at).toISOString().split('T')[0],
      description: article.description || '',
      tags: article.tag_names ? article.tag_names.split(',').map(tag => tag.trim()) : [],
      categories: article.category_names ? article.category_names.split(',').map(cat => cat.trim()) : [],
      featured: !!article.featured,
      coverImage: article.cover_image || '',
      auxiliary_content: article.auxiliary_content || '',
      articleImages: article.articleImages.map(img => ({
        id: img.id,
        filename: img.filename,
        original_name: img.original_name,
        url: `/articles/img/${img.filename}`,
        alt_text: img.alt_text,
        width: img.width,
        height: img.height
      }))
    }))

    // 写入articles.js文件，严格按照原始格式
    const articlesJsContent = `export const articles = ${JSON.stringify(articlesData, null, 2).replace(/"/g, "'")};
`

    await fs.writeFile(ARTICLES_DATA_PATH, articlesJsContent, 'utf8')
    console.log(`✓ 已更新 ${ARTICLES_DATA_PATH}`)

    // 同步Markdown文件
    await syncMarkdownFiles(articles)
    
    console.log('✓ 文章数据同步完成')
    return { success: true, count: articles.length }
    
  } catch (error) {
    console.error('同步文章数据失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 同步Markdown文件到前端public目录
 * @param {Array} articles - 文章数组
 */
async function syncMarkdownFiles(articles) {
  try {
    // 确保articles目录存在
    await fs.mkdir(ARTICLES_DIR_PATH, { recursive: true })
    
    // 获取现有的markdown文件
    const existingFiles = await fs.readdir(ARTICLES_DIR_PATH)
    const existingSlugs = existingFiles
      .filter(file => file.endsWith('.md'))
      .map(file => file.replace('.md', ''))
    
    // 当前应该存在的文件
    const currentSlugs = articles.map(a => a.slug)
    
    // 删除不再需要的文件
    for (const slug of existingSlugs) {
      if (!currentSlugs.includes(slug)) {
        const filePath = path.join(ARTICLES_DIR_PATH, `${slug}.md`)
        await fs.unlink(filePath)
        console.log(`✓ 已删除 ${slug}.md`)
      }
    }
    
    // 创建或更新markdown文件
    for (const article of articles) {
      const filePath = path.join(ARTICLES_DIR_PATH, `${article.slug}.md`)
      
      // 生成front matter
      const frontMatter = `---
title: "${article.title}"
description: "${article.description || ''}"
date: "${new Date(article.created_at).toISOString().split('T')[0]}"
category: "${article.category_names ? article.category_names.split(',')[0] : '未分类'}"
tags: [${article.tag_names ? article.tag_names.split(',').map(tag => `"${tag.trim()}"`).join(', ') : ''}]
featured: ${!!article.featured}
${article.cover_image ? `coverImage: "${article.cover_image}"` : ''}
${article.auxiliary_content ? `auxiliary_content: "${article.auxiliary_content.replace(/"/g, '\\"')}"` : ''}
articleImages: [${article.articleImages.map(img => `{id: ${img.id}, filename: "${img.filename}", url: "/articles/img/${img.filename}", alt_text: "${img.alt_text || ''}"}`).join(', ')}]
---

`
      
      const markdownContent = frontMatter + article.content
      await fs.writeFile(filePath, markdownContent, 'utf8')
      console.log(`✓ 已更新 ${article.slug}.md`)
    }
    
  } catch (error) {
    console.error('同步Markdown文件失败:', error)
    throw error
  }
}

/**
 * 计算阅读时间（基于字数）
 * @param {string} content - 文章内容
 * @returns {string} 阅读时间
 */
function calculateReadTime(content) {
  if (!content) return '1 min'
  
  // 中文字符数 + 英文单词数
  const chineseChars = (content.match(/[\u4e00-\u9fa5]/g) || []).length
  const englishWords = (content.match(/[a-zA-Z]+/g) || []).length
  
  // 假设中文200字/分钟，英文250词/分钟
  const readingTime = Math.ceil((chineseChars / 200) + (englishWords / 250))
  
  return `${Math.max(1, readingTime)} min`
}

/**
 * 同步单个文章
 * @param {Object} db - 数据库实例
 * @param {string} articleId - 文章ID
 */
export async function syncSingleArticle(db, articleId) {
  try {
    // 确保目录存在
    await fs.mkdir(ARTICLES_DIR_PATH, { recursive: true })
    await fs.mkdir(path.dirname(ARTICLES_DATA_PATH), { recursive: true })

    const article = db.prepare(`
      SELECT
        a.*,
        GROUP_CONCAT(DISTINCT c.name) as category_names,
        GROUP_CONCAT(DISTINCT t.name) as tag_names
      FROM articles a
      LEFT JOIN article_categories ac ON a.id = ac.article_id
      LEFT JOIN categories c ON ac.category_id = c.id
      LEFT JOIN article_tags at ON a.id = at.article_id
      LEFT JOIN tags t ON at.tag_id = t.id
      WHERE a.id = ?
      GROUP BY a.id
    `).get(articleId)

    if (article) {
      // 获取文章配图
      const getArticleImages = db.prepare(`
        SELECT i.* FROM images i
        JOIN article_images ai ON i.id = ai.image_id
        WHERE ai.article_id = ?
        ORDER BY ai.sort_order
      `);
      article.articleImages = getArticleImages.all(article.id);
    }

    if (!article) {
      throw new Error('文章不存在')
    }

    const filePath = path.join(ARTICLES_DIR_PATH, `${article.slug}.md`)
    
    if (article.status === 'published') {
      // 创建或更新文件
      const frontMatter = `---
title: "${article.title}"
description: "${article.description || ''}"
date: "${new Date(article.created_at).toISOString().split('T')[0]}"
category: "${article.category_names ? article.category_names.split(',')[0] : '未分类'}"
tags: [${article.tag_names ? article.tag_names.split(',').map(tag => `"${tag.trim()}"`).join(', ') : ''}]
featured: ${!!article.featured}
${article.cover_image ? `coverImage: "${article.cover_image}"` : ''}
${article.auxiliary_content ? `auxiliary_content: "${article.auxiliary_content.replace(/"/g, '\\"')}"` : ''}
articleImages: [${article.articleImages.map(img => `{id: ${img.id}, filename: "${img.filename}", url: "/articles/img/${img.filename}", alt_text: "${img.alt_text || ''}"}`).join(', ')}]
---

`
      
      const markdownContent = frontMatter + article.content
      await fs.writeFile(filePath, markdownContent, 'utf8')
      console.log(`✓ 已同步文章: ${article.slug}`)
    } else {
      // 删除文件（如果存在）
      try {
        await fs.unlink(filePath)
        console.log(`✓ 已删除草稿文章: ${article.slug}`)
      } catch (error) {
        // 文件不存在，忽略错误
      }
    }
    
    // 重新生成articles.js
    await syncArticlesToFrontend(db)
    
    return { success: true }
    
  } catch (error) {
    console.error('同步单个文章失败:', error)
    return { success: false, error: error.message }
  }
}

// 导出函数
export { syncArticlesToFrontend as syncToFrontend }
