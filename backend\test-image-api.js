import fetch from 'node-fetch';

async function testImageAPI() {
  try {
    // 登录
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123456'
      })
    });

    const loginResult = await loginResponse.json();
    const token = loginResult.data.token;

    // 获取图片列表
    const imagesResponse = await fetch('http://localhost:3001/api/images', {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    const imagesResult = await imagesResponse.json();
    
    console.log('图片API响应:');
    console.log(JSON.stringify(imagesResult, null, 2));
    
    if (imagesResult.data.images.length > 0) {
      console.log('\n第一张图片详情:');
      console.log(JSON.stringify(imagesResult.data.images[0], null, 2));
    }

  } catch (error) {
    console.error('测试失败:', error);
  }
}

testImageAPI();
