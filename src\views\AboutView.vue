<!-- /src/views/AboutView.vue -->
<template>
  <MainLayout>
    <div :class="$style.aboutContainer">
      <div :class="$style.emptyArea">
        <div :class="$style.floatingElement"></div>
      </div>
      <div :class="$style.contentArea">
        <div :class="$style.logoWrapper">
          <GlowingLogo :size="150" :glow-intensity="0.6" :hover-scale="1.1" />
        </div>

        <h1 :class="$style.pageTitle">关于黑猫船长 Z</h1>

        <div :class="$style.decorativeLine"></div>

        <div :class="$style.biographySection">
          <div :class="$style.chineseContent">
            <p :class="[$style.aboutParagraph, $style.greeting]">
              你好，欢迎步入我的空间。
            </p>
            <p :class="$style.aboutParagraph">
              你会发现，我并非那种能在人群中迅速热络、成为焦点的人。喧闹的场合常让我需要更多时间来适应，我更享受也更习惯在安静的角落，这份对安静的偏爱，并非内心空洞的象征。
            </p>
            <div :class="$style.quoteCard">
              <p :class="[$style.aboutParagraph, $style.highlight]">
                恰恰相反，它守护着一个持续思考与感受的世界，那里存着一种对更真诚的人际互动、更有温度的社会生态的深切向往，有我珍视并投入时间的"慢"追求。
              </p>
            </div>
          </div>

          <div :class="$style.languageDivider"></div>

          <div :class="$style.englishContent">
            <p :class="[$style.aboutParagraph, $style.greeting]">
              Hello, and welcome to my space.
            </p>
            <p :class="$style.aboutParagraph">
              You'll discover I'm not the kind of person who instantly energizes a crowd or naturally steps into the spotlight. I often need a little more time to settle into noisy or bustling settings. I find more solace and feel more myself in quieter corners.
            </p>
            <div :class="$style.quoteCard">
              <p :class="[$style.aboutParagraph, $style.highlight]">
                This fondness for quiet doesn't signify an inner void. Quite the opposite, it shields an inner world constantly alive with thoughts and emotions. Within this space resides a deep longing for more authentic connections and a warmer social environment. It's where I cherish and invest time in the 'slow' pursuits that truly matter to me.
              </p>
            </div>
          </div>
        </div>

        <div :class="$style.authorSignature">
          <svg :class="$style.signatureDash" width="40" height="2" viewBox="0 0 40 2" fill="none">
            <line x1="0" y1="1" x2="40" y2="1" stroke="currentColor" stroke-dasharray="4 2" />
          </svg>
          <span>黑猫船长 Z</span>
        </div>
      </div>
      <div :class="$style.sidebarArea"></div>
    </div>
  </MainLayout>
</template>

<script setup>
import MainLayout from '@/components/layout/MainLayout.vue';
import GlowingLogo from '@/components/ui/GlowingLogo.vue';
</script>

<style module>
.aboutContainer {
  display: grid;
  grid-template-columns: minmax(160px, 0.8fr) minmax(540px, 2fr) 240px; /* 与主页保持一致的三列布局 */
  gap: var(--space-l);
  padding: 0 var(--space-l);
  width: 100%;
}

.contentArea {
  grid-column: 2; /* 内容区域放在中间列 */
  padding: var(--space-xl) var(--space-m) var(--space-xl) var(--space-l);
  background: radial-gradient(
    circle at 20% 30%,
    rgba(var(--color-accent-rgb), 0.04) 0%,
    transparent 70%
  ); /* 添加微妙的径向渐变背景 */
  position: relative;
  max-width: 750px;
  animation: fadeIn 0.8s ease-out;
}

/* 装饰性背景元素 */
.contentArea::before {
  content: "";
  position: absolute;
  top: 5%;
  right: 0;
  width: 100px;
  height: 100px;
  background: radial-gradient(
    circle at center,
    rgba(var(--color-accent-rgb), 0.02) 0%,
    transparent 70%
  );
  z-index: -1;
  border-radius: 50%;
  animation: pulse 15s infinite alternate ease-in-out;
}

.contentArea::after {
  content: "";
  position: absolute;
  bottom: 10%;
  left: 5%;
  width: 150px;
  height: 150px;
  background: radial-gradient(
    circle at center,
    rgba(var(--color-accent-rgb), 0.01) 0%,
    transparent 70%
  );
  z-index: -1;
  border-radius: 50%;
  animation: pulse 18s infinite alternate-reverse ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pageTitle {
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-light);
  font-size: clamp(2rem, 5vw + 1rem, 2.8rem);
  letter-spacing: var(--letter-spacing-heading);
  color: var(--color-text-primary);
  margin-bottom: var(--space-l);
  display: inline-block;
  position: relative;
  animation: slideIn 0.6s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.decorativeLine {
  width: 80px;
  height: 1px;
  background: linear-gradient(to right, var(--color-accent), rgba(var(--color-accent-rgb), 0.1));
  margin-bottom: var(--space-xl);
  opacity: 0.8;
}

.biographySection {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
  position: relative;
}

.chineseContent, .englishContent {
  display: flex;
  flex-direction: column;
  gap: var(--space-m);
  animation: fadeIn 1s ease-out both;
}

.englishContent {
  animation-delay: 0.3s;
}

.aboutParagraph {
  font-family: var(--font-family-body);
  font-size: 1.05rem;
  line-height: 1.9;
  color: var(--color-text-primary);
  margin: 0;
  letter-spacing: var(--letter-spacing-body);
  text-align: justify;
}

.aboutParagraph:first-child {
  font-size: 1.25rem;
  color: var(--color-accent);
  font-weight: var(--font-weight-medium);
  letter-spacing: 0.02em;
}

.languageDivider {
  width: 100%;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(var(--color-accent-rgb), 0.2), transparent);
  margin: var(--space-s) 0;
}

.authorSignature {
  display: flex;
  align-items: center;
  gap: var(--space-s);
  margin-top: var(--space-xxl);
  color: var(--color-text-secondary);
  font-family: var(--font-family-heading);
  font-style: italic;
  justify-content: flex-end;
}

.signatureDash {
  color: var(--color-accent);
  opacity: 0.7;
}

.quoteCard {
  background: rgba(30, 30, 30, 0.3);
  border-radius: var(--border-radius-soft);
  padding: var(--space-l);
  border-left: 2px solid rgba(var(--color-accent-rgb), 0.4);
  margin: var(--space-s) 0;
  backdrop-filter: blur(3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.quoteCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  border-left: 2px solid rgba(var(--color-accent-rgb), 0.6);
}

.highlight {
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
  line-height: 2;
  font-size: 1.05rem;
}

.greeting {
  font-size: 1.35rem;
  color: var(--color-accent);
  font-weight: var(--font-weight-light);
  font-family: var(--font-family-heading);
  margin-bottom: var(--space-m);
  letter-spacing: 0.02em;
  transition: color 0.3s ease;
}

.greeting:hover {
  color: var(--color-accent-hover);
}

/* 添加左侧装饰区域样式 */
.emptyArea {
  grid-column: 1;
  display: none;
  position: relative;
}

.floatingElement {
  position: absolute;
  top: 40%;
  right: 20%;
  width: 100px;
  height: 100px;
  background: linear-gradient(
    135deg,
    rgba(var(--color-accent-rgb), 0.01) 0%,
    rgba(var(--color-accent-rgb), 0.05) 50%,
    rgba(var(--color-accent-rgb), 0.01) 100%
  );
  border-radius: 50%;
  opacity: 0.3;
  animation: float 20s infinite ease-in-out;
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
  100% {
    transform: translateY(0) rotate(360deg);
  }
}

.sidebarArea {
  grid-column: 3;
  display: none;
}

@media (min-width: 768px) {
  .emptyArea, .sidebarArea {
    display: block;
  }
}

@media (max-width: 767px) {
  .aboutContainer {
    display: block;
    padding: 0 var(--space-s);
  }

  .contentArea {
    padding: var(--space-l) var(--space-s);
    background: none;
  }

  .biographySection {
    gap: var(--space-l);
  }

  .aboutParagraph {
    font-size: 1rem;
  }

  .greeting {
    font-size: 1.2rem;
  }

  .quoteCard {
    padding: var(--space-m);
  }
}

/* 添加Logo相关样式 */
.logoContainer {
  display: flex;
  justify-content: center;
  margin-bottom: var(--space-xl);
  overflow: visible;
  position: relative;
}

.logoWrapper {
  display: flex;
  justify-content: center;
  margin-bottom: var(--space-l);
  position: relative;
  z-index: 2;
}

/* 确保移动端也有适当的样式 */
@media (max-width: 767px) {
  .logoWrapper {
    margin-bottom: var(--space-m);
  }
}
</style>