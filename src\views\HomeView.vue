<!-- /src/views/HomeView.vue -->
<template>
  <MainLayout>
    <div class="layoutGrid">
      <div :class="$style.homeContainer">
        <DecorationLine />
        <div :class="$style.mainArea">
          <!-- Logo -->
          <div :class="$style.logoWrapper">
            <GlowingLogo :size="150" :glow-intensity="0.6" :hover-scale="1.1" />
          </div>

          <!-- Page Header -->
          <PageHeader />

          <!-- Divider -->
          <Divider />

          <!-- Filter Status -->
          <FilterStatus
            :selected-year="navigationStore.selectedYear"
            :selected-category="navigationStore.selectedCategory"
            @clear="clearFilters"
          />

          <!-- Article List -->
          <ArticleList
            :selected-year="navigationStore.selectedYear"
            :selected-category="navigationStore.selectedCategory"
          />
        </div>

        <!-- Sidebar -->
        <div :class="$style.sidebarArea">
          <TableOfContents />
        </div>
      </div>
    </div>
  </MainLayout>
</template>

<script setup>
import MainLayout from '@/components/layout/MainLayout.vue';
import ArticleList from '@/components/features/ArticleList.vue';
import TableOfContents from '@/components/features/TableOfContents.vue';
import GlowingLogo from '@/components/ui/GlowingLogo.vue';
import FilterStatus from '@/components/features/FilterStatus.vue';
import PageHeader from '@/components/features/PageHeader.vue';
import Divider from '@/components/ui/Divider.vue';
import DecorationLine from '@/components/ui/DecorationLine.vue';
import { useNavigationStore } from '@/stores/navigation';

const navigationStore = useNavigationStore();

const clearFilters = () => {
  navigationStore.updateYear('');
  navigationStore.updateCategory('');
};
</script>

<style module>
.layoutGrid {
  width: 100%;
  max-width: var(--max-width);
  margin: 0 auto;
}

.homeContainer {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-l);
  align-items: start;
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--space-m);
}

@media (min-width: 768px) {
  .homeContainer {
    grid-template-columns: minmax(160px, 0.8fr) minmax(540px, 2fr) 240px;
    padding: 0 var(--space-l);
    gap: var(--space-l);
  }

  .mainArea {
    grid-column: 2;
  }

  .sidebarArea {
    grid-column: 3;
  }
}

@media (max-width: 767px) {
  .homeContainer {
    padding: 0 var(--space-s);
    display: grid;
    grid-template-columns: 1fr;
  }

  .mainArea {
    padding: 0;
    background: none;
  }
}

.mainArea {
  width: 100%;
  min-width: 0;
  padding: 0 var(--space-m) 0 var(--space-l);
  position: relative;
  background: radial-gradient(
    circle at 30% 20%,
    rgba(var(--color-accent-rgb), 0.03) 0%,
    transparent 60%
  );
}

.sidebarArea {
  width: 100%;
  padding-top: var(--space-xl);
  position: relative;
}

.logoWrapper {
  margin-bottom: var(--space-l);
  position: relative;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  z-index: 2;
}

.logoWrapper::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 60px;
  width: 160px;
  height: 160px;
  border-radius: 50%;
  background: radial-gradient(
    circle at center,
    rgba(var(--color-accent-rgb), 0.08) 0%,
    transparent 70%
  );
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
  z-index: -1;
  transition: transform 0.6s ease-out, opacity 0.6s ease-out;
}

.logoWrapper:hover::before {
  transform: translate(-50%, -50%) scale(1.2);
  opacity: 1;
}
</style>