<!-- /src/components/common/LinkWrapper.vue -->
<template>
  <!-- 标准 HTML 注释：根据链接类型渲染 RouterLink 或普通 a 标签 -->
  <RouterLink v-if="isInternal" :to="href" :class="[$style.link, className]">
    <!-- 标准 HTML 注释：链接内容通过 slot 传入 -->
    <slot></slot>
    <!-- 标准 HTML 注释：用于实现动画下划线的 span -->
    <span :class="$style.underline"></span>
  </RouterLink>
  <a v-else :href="href" :class="[$style.link, className]" target="_blank" rel="noopener noreferrer">
    <slot></slot>
    <span :class="$style.underline"></span>
  </a>
</template>

<script setup>
import { computed } from 'vue';
// 显式导入 RouterLink，即使全局注册了也更安全
import { RouterLink } from 'vue-router';

const props = defineProps({
  href: { type: String, required: true }, // 链接地址
  className: { type: String, default: '' } // 允许外部传入额外 class
});

// 判断是否为内部链接 (以 / 或 # 开头)
const isInternal = computed(() => props.href.startsWith('/') || props.href.startsWith('#'));
</script>

<style module>
.link {
  color: var(--color-accent);
  text-decoration: none;
  position: relative; /* 为下划线定位 */
  transition: color var(--transition-duration) var(--transition-timing-function);
  cursor: pointer;
  /* 可选：为触摸设备增加点击区域 */
  /* padding: var(--space-xs) 0; */
  border-radius: 2px; /* 用于焦点轮廓 */
  font-weight: var(--font-weight-regular); /* 保持与正文一致的字重 */
  letter-spacing: 0.01em; /* 微妙的字间距调整 */
}

.underline {
  position: absolute;
  bottom: -2px; /* 下划线与文字的距离调整 */
  left: 0;
  width: 100%;
  height: 1px; /* 极纤细下划线 */
  background-color: var(--color-accent-hover);
  transform: scaleX(0); /* 默认隐藏 */
  transform-origin: left; /* 从左侧展开 */
  transition: transform var(--transition-duration) var(--transition-timing-function-in-out);
  pointer-events: none; /* 不干扰链接点击 */
  opacity: 0.8; /* 轻微透明度，更加微妙 */
}

/* 悬停效果 - 符合设计规范中的微妙过渡 */
.link:hover {
  color: var(--color-accent-hover);
  transition: color 0.2s ease; /* 更快的颜色过渡 */
}
.link:hover .underline {
  transform: scaleX(1); /* 显示下划线 */
  transition: transform 0.3s ease-out; /* 平滑展开效果 */
}

/* 键盘焦点效果 (A11y) */
.link:focus-visible {
   outline: 2px solid var(--color-highlight); /* 使用高亮色 */
   outline-offset: 2px;
   /* 保持与 hover 一致的视觉反馈 */
   color: var(--color-accent-hover);
}
.link:focus-visible .underline {
    transform: scaleX(1); /* 显示下划线 */
}
</style>