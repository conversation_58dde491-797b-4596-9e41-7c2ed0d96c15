# 墨影心流博客

一个基于Vue 3 + Vite构建的单页应用博客，采用『墨影心流』(Ink Shadow Heartstream)设计风格。

## 项目特点

- 静谧深邃，心绪流淌的设计风格
- 响应式布局，适配各种设备
- 著作集展示功能
- 书籍阅读界面
- 文章展示与搜索功能

## 开发环境

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 构建与部署

```bash
# 构建项目
npm run build

# 预览构建结果
npm run preview
```

## 路由说明

本项目使用Vue Router的哈希路由模式（Hash Mode），URL格式为`http://hmjz.327gzs.top/#/路径`（如`/#/books`、`/#/book/book-1`等）。

### 哈希路由模式的优势

哈希路由模式不需要服务器配置就能正常工作，因为哈希（#）后面的内容不会被发送到服务器，而是由前端JavaScript处理。这避免了在直接访问非根路径URL时出现404或403错误的问题。

### 解决方案

请参考项目根目录下的`DEPLOYMENT.md`文件，其中提供了针对不同服务器环境的配置说明：

- Nginx
- Apache
- IIS
- Netlify
- Vercel
- 其他静态文件服务器

## 项目结构

```
moying-blog/
├── public/             # 静态资源
│   ├── articles/       # 文章资源
│   ├── books/          # 书籍资源
│   └── images/         # 图片资源
├── src/                # 源代码
│   ├── assets/         # 项目资源
│   ├── components/     # 组件
│   ├── data/           # 数据文件
│   ├── router/         # 路由配置
│   ├── stores/         # Pinia状态管理
│   ├── styles/         # 样式文件
│   └── views/          # 页面视图
├── .htaccess           # Apache配置
├── DEPLOYMENT.md       # 部署指南
├── index.html          # 入口HTML
├── netlify.toml        # Netlify配置
├── nginx.conf          # Nginx配置
├── package.json        # 项目配置
├── vercel.json         # Vercel配置
├── vite.config.js      # Vite配置
└── web.config          # IIS配置
```

## 许可证

[MIT](LICENSE)