// 错误处理中间件
export const errorHandler = (err, req, res, next) => {
  console.error('错误详情:', err);

  // 默认错误信息
  let error = {
    message: err.message || '服务器内部错误',
    status: err.status || 500
  };

  // JWT 错误处理
  if (err.name === 'JsonWebTokenError') {
    error = {
      message: '无效的访问令牌',
      status: 401
    };
  } else if (err.name === 'TokenExpiredError') {
    error = {
      message: '访问令牌已过期',
      status: 401
    };
  }

  // 数据库错误处理
  if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
    error = {
      message: '数据已存在，请检查唯一性约束',
      status: 409
    };
  }

  // 文件上传错误处理
  if (err.code === 'LIMIT_FILE_SIZE') {
    error = {
      message: '文件大小超出限制',
      status: 413
    };
  }

  // 验证错误处理
  if (err.name === 'ValidationError') {
    error = {
      message: '数据验证失败',
      status: 400,
      details: err.details
    };
  }

  // 开发环境下返回详细错误信息
  const response = {
    success: false,
    message: error.message,
    ...(process.env.NODE_ENV === 'development' && { 
      stack: err.stack,
      details: error.details 
    })
  };

  res.status(error.status).json(response);
};

// 404 处理中间件
export const notFound = (req, res) => {
  res.status(404).json({
    success: false,
    message: `路由 ${req.originalUrl} 不存在`
  });
};
