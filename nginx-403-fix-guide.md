# 修复墨影心流博客403 Forbidden错误指南

## 问题描述

网站主域名 http://hmjz.327gzs.top 可以正常访问，但直接访问非根路径URL（如 http://hmjz.327gzs.top/books 或 http://hmjz.327gzs.top/book/book-1）时，服务器返回403 Forbidden错误。

## 原因分析

403 Forbidden错误通常由以下原因导致：

1. Nginx配置中的location块设置不正确
2. 文件/目录权限问题
3. SELinux或其他安全策略限制

## 解决方案

### 1. 修改Nginx配置文件

请找到网站对应的Nginx配置文件（通常位于 `/etc/nginx/sites-available/` 或 `/etc/nginx/conf.d/` 目录下），并确保包含以下配置：

```nginx
server {
    listen 80;
    server_name hmjz.327gzs.top;
    
    # 网站根目录，替换为实际部署路径
    root /path/to/your/website/dist;
    index index.html;
    
    # 启用gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # 缓存静态资源
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|woff|woff2)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        try_files $uri =404;
    }
    
    # 关键配置：将所有非静态资源的请求重定向到index.html
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 错误页面配置
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
```

### 2. 检查并修复文件权限

确保Nginx用户（通常是www-data或nginx）对网站文件有正确的读取权限：

```bash
# 假设网站文件位于 /path/to/your/website/dist
# 设置目录权限
sudo find /path/to/your/website/dist -type d -exec chmod 755 {} \;

# 设置文件权限
sudo find /path/to/your/website/dist -type f -exec chmod 644 {} \;

# 设置所有者（将www-data替换为您服务器上Nginx使用的用户名）
sudo chown -R www-data:www-data /path/to/your/website/dist
```

### 3. 检查SELinux设置（如果适用）

如果服务器使用SELinux，需要设置正确的上下文：

```bash
# 检查SELinux状态
sestatus

# 如果启用了SELinux，设置正确的上下文
sudo semanage fcontext -a -t httpd_sys_content_t "/path/to/your/website/dist(/.*)?"
sudo restorecon -Rv /path/to/your/website/dist
```

### 4. 应用配置并重启Nginx

修改配置后，测试并重启Nginx：

```bash
sudo nginx -t  # 测试配置文件语法
sudo systemctl restart nginx  # 重启Nginx
```

## 验证修复

修改完成后，请尝试直接访问以下URL，确认问题是否解决：

- http://hmjz.327gzs.top/books
- http://hmjz.327gzs.top/book/book-1

## 常见问题排查

如果修改后仍然出现403错误，请检查：

1. Nginx错误日志（通常位于 `/var/log/nginx/error.log`）
2. 确认网站文件的实际路径是否正确
3. 检查服务器防火墙或安全组设置
4. 确认Nginx配置文件中的server_name与实际域名匹配
