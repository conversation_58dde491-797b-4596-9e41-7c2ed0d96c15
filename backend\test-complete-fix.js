import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';
import Database from 'better-sqlite3';
import { syncArticlesToFrontend } from './src/utils/syncToFrontend.js';

const BASE_URL = 'http://localhost:3001/api';
const db = new Database('database.sqlite');

// 测试用的管理员凭据
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123456'
};

let authToken = '';

// 登录获取token
async function login() {
  try {
    const response = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(ADMIN_CREDENTIALS)
    });

    const result = await response.json();
    if (result.success) {
      authToken = result.data.token;
      console.log('✓ 登录成功');
      return true;
    } else {
      console.log('✗ 登录失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('✗ 登录请求失败:', error.message);
    return false;
  }
}

// 测试1：图片上传功能
async function testImageUpload() {
  try {
    console.log('\n=== 测试1：图片上传功能 ===');
    
    // 创建测试图片文件
    const testImagePath = path.join(process.cwd(), 'test-complete.jpg');
    const existingImagePath = path.join(process.cwd(), '..', 'public', 'articles', 'img', '1.jpg');
    
    if (fs.existsSync(existingImagePath)) {
      fs.copyFileSync(existingImagePath, testImagePath);
    } else {
      console.log('✗ 没有找到测试图片文件');
      return false;
    }

    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));

    const response = await fetch(`${BASE_URL}/images/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        ...formData.getHeaders()
      },
      body: formData
    });

    const result = await response.json();
    
    // 清理测试文件
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
    }

    if (result.success) {
      console.log('✓ 图片上传成功');
      console.log(`  文件名: ${result.data.filename}`);
      console.log(`  URL: ${result.data.url}`);
      
      // 测试图片访问
      const imageUrl = `http://localhost:3001${result.data.url}`;
      const imageResponse = await fetch(imageUrl);
      if (imageResponse.ok) {
        console.log('✓ 上传的图片可以正常访问');
      } else {
        console.log('✗ 上传的图片无法访问');
      }
      
      return result.data.id;
    } else {
      console.log('✗ 图片上传失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('✗ 图片上传测试失败:', error.message);
    return false;
  }
}

// 测试2：图片列表API
async function testImageList() {
  try {
    console.log('\n=== 测试2：图片列表API ===');
    
    const response = await fetch(`${BASE_URL}/images`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    const result = await response.json();
    if (result.success) {
      console.log(`✓ 图片列表API正常，共 ${result.data.images.length} 张图片`);
      
      if (result.data.images.length > 0) {
        const firstImage = result.data.images[0];
        console.log(`  示例图片: ${firstImage.filename}`);
        console.log(`  URL格式: ${firstImage.url}`);
        
        // 测试图片静态文件访问
        const imageUrl = `http://localhost:3001${firstImage.url}`;
        const imageResponse = await fetch(imageUrl);
        if (imageResponse.ok) {
          console.log('✓ 图片静态文件服务正常');
        } else {
          console.log('✗ 图片静态文件服务异常');
        }
      }
      return true;
    } else {
      console.log('✗ 图片列表API失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('✗ 图片列表API测试失败:', error.message);
    return false;
  }
}

// 测试3：文章创建与配图关联
async function testArticleWithImages(uploadedImageId) {
  try {
    console.log('\n=== 测试3：文章创建与配图关联 ===');
    
    const testArticle = {
      title: '完整功能测试文章',
      slug: 'complete-test-article-' + Date.now(),
      description: '这是一个完整功能测试文章',
      content: '# 测试文章\n\n这是测试内容，包含配图和辅助信息。',
      auxiliary_content: '这是辅助介绍内容，用于测试数据持久化。',
      status: 'published',
      featured: true,
      categories: [],
      tags: [],
      articleImages: uploadedImageId ? [1, uploadedImageId] : [1, 2]
    };

    const response = await fetch(`${BASE_URL}/articles`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(testArticle)
    });

    const result = await response.json();
    if (result.success) {
      console.log('✓ 文章创建成功');
      console.log(`  文章ID: ${result.data.id}`);
      console.log(`  标题: ${result.data.title}`);
      console.log(`  辅助内容: ${result.data.auxiliary_content}`);
      
      // 验证配图关联
      const articleImages = db.prepare(`
        SELECT COUNT(*) as count FROM article_images WHERE article_id = ?
      `).get(result.data.id);
      
      console.log(`  配图关联数量: ${articleImages.count}`);
      
      return result.data.id;
    } else {
      console.log('✗ 文章创建失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('✗ 文章创建测试失败:', error.message);
    return false;
  }
}

// 测试4：数据同步功能
async function testDataSync() {
  try {
    console.log('\n=== 测试4：数据同步功能 ===');
    
    const result = await syncArticlesToFrontend(db);
    if (result.success) {
      console.log(`✓ 数据同步成功，同步了 ${result.count} 篇文章`);
      
      // 检查生成的articles.js文件
      const articlesPath = path.join(process.cwd(), '..', 'src', 'data', 'articles.js');
      if (fs.existsSync(articlesPath)) {
        const content = fs.readFileSync(articlesPath, 'utf8');
        const hasAuxiliaryContent = content.includes('auxiliary_content');
        const hasArticleImages = content.includes('articleImages');
        
        console.log(`  包含辅助内容字段: ${hasAuxiliaryContent ? '✓' : '✗'}`);
        console.log(`  包含配图字段: ${hasArticleImages ? '✓' : '✗'}`);
        
        return hasAuxiliaryContent && hasArticleImages;
      } else {
        console.log('✗ 未找到生成的articles.js文件');
        return false;
      }
    } else {
      console.log('✗ 数据同步失败:', result.error);
      return false;
    }
  } catch (error) {
    console.log('✗ 数据同步测试失败:', error.message);
    return false;
  }
}

// 清理测试数据
async function cleanup(imageId, articleId) {
  console.log('\n=== 清理测试数据 ===');
  
  if (articleId) {
    try {
      await fetch(`${BASE_URL}/articles/${articleId}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${authToken}` }
      });
      console.log('✓ 测试文章已清理');
    } catch (error) {
      console.log('⚠ 清理测试文章失败');
    }
  }
  
  if (imageId) {
    try {
      await fetch(`${BASE_URL}/images/${imageId}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${authToken}` }
      });
      console.log('✓ 测试图片已清理');
    } catch (error) {
      console.log('⚠ 清理测试图片失败');
    }
  }
}

// 主测试函数
async function runCompleteTest() {
  console.log('🚀 开始完整功能测试...\n');
  
  let uploadedImageId = null;
  let createdArticleId = null;
  
  try {
    // 登录
    const loginSuccess = await login();
    if (!loginSuccess) {
      console.log('❌ 测试终止：无法登录');
      return;
    }
    
    // 测试图片上传
    uploadedImageId = await testImageUpload();
    
    // 测试图片列表
    const imageListSuccess = await testImageList();
    
    // 测试文章创建与配图关联
    createdArticleId = await testArticleWithImages(uploadedImageId);
    
    // 测试数据同步
    const syncSuccess = await testDataSync();
    
    // 输出测试结果
    console.log('\n🎯 测试结果汇总:');
    console.log(`图片上传功能: ${uploadedImageId ? '✅ 通过' : '❌ 失败'}`);
    console.log(`图片列表功能: ${imageListSuccess ? '✅ 通过' : '❌ 失败'}`);
    console.log(`文章配图关联: ${createdArticleId ? '✅ 通过' : '❌ 失败'}`);
    console.log(`数据同步功能: ${syncSuccess ? '✅ 通过' : '❌ 失败'}`);
    
    const allTestsPassed = uploadedImageId && imageListSuccess && createdArticleId && syncSuccess;
    console.log(`\n🏆 整体测试结果: ${allTestsPassed ? '✅ 全部通过' : '❌ 存在问题'}`);
    
  } finally {
    // 清理测试数据
    await cleanup(uploadedImageId, createdArticleId);
    db.close();
  }
}

runCompleteTest().catch(console.error);
