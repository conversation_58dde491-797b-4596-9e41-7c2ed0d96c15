server {
    listen 80;
    server_name hmjz.327gzs.top;
    
    # 网站根目录，替换为您的实际部署路径
    root /path/to/your/website/dist;
    index index.html;
    
    # 启用gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # 缓存静态资源
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|woff|woff2)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
        # 重要：确保这些文件可以被访问
        try_files $uri =404;
    }
    
    # 关键配置：将所有非静态资源的请求重定向到index.html
    location / {
        # 尝试直接提供请求的文件，如果不存在则回退到index.html
        try_files $uri $uri/ /index.html;
        
        # 重要：添加这些头部，允许访问
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    }
    
    # 错误页面配置
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
