server {
    listen 80;
    # 替换为您的域名
    server_name example.com;
    
    # 网站根目录，替换为您的实际部署路径
    root /var/www/html;
    index index.html;
    
    # 启用gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # 缓存静态资源
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }
    
    # 处理API请求（如果有）
    # location /api/ {
    #     proxy_pass http://your-api-server;
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Real-IP $remote_addr;
    # }
    
    # 关键配置：将所有非静态资源的请求重定向到index.html
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 错误页面配置
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
