// /src/composables/useArticle.js
import { ref } from 'vue';
import MarkdownIt from 'markdown-it';
import fm from 'front-matter';

const md = new MarkdownIt({ html: true });

export function useArticle() {
  const article = ref(null);
  const loading = ref(false);
  const error = ref(null);

  const loadArticle = async (slug) => {
    console.log('调用了loadArticle，slug:', slug);
    loading.value = true;
    error.value = null;
    
    try {
      const url = `/articles/${slug}.md`;
      console.log('请求文章路径:', url);
      const response = await fetch(url);
      if (!response.ok) {
        console.error('文章请求失败，状态码:', response.status);
        throw new Error(`文章加载失败: ${response.statusText}`);
      }
      const rawContent = await response.text();
      const { attributes, body } = fm(rawContent);
      const htmlContent = md.render(body);

      article.value = {
        ...attributes, // 将 Frontmatter 属性合并到 article 对象
        content: htmlContent, // 存储渲染后的 HTML
        slug
      };
      console.log('文章内容已加载:', article.value);
    } catch (e) {
      error.value = e.message;
      console.error('文章加载错误:', e);
    } finally {
      loading.value = false;
    }
  };

  return {
    article,
    loading,
    error,
    loadArticle
  };
}