<!-- /src/components/layout/MainLayout.vue -->
<!-- 标准 HTML 注释：可选的布局包装器，用于包裹需要特定布局（如网格列）的视图 -->
<template>
  <main :class="$style.mainContent">
    <!-- 标准 HTML 注释：RouterView 渲染的组件将插入到这里 -->
    <slot></slot>
    <div :class="$style.socialLinks">
      <a href="#" target="_blank" rel="noopener noreferrer" :class="$style.socialLink">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="24" height="24" fill="currentColor">
          <path d="M512 64c-70.7 0-128 57.3-128 128 0 35.3 14.3 67.3 37.5 90.5C355.7 310.7 320 370.2 320 438.4c0 70.7 57.3 128 128 128s128-57.3 128-128c0-68.2-35.7-127.7-101.5-155.9C625.7 259.3 640 227.3 640 192c0-70.7-57.3-128-128-128zm0 576c-105.9 0-192 86.1-192 192h384c0-105.9-86.1-192-192-192z"/>
        </svg>
      </a>
      <a href="#" target="_blank" rel="noopener noreferrer" :class="$style.socialLink">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="24" height="24" fill="currentColor">
          <path d="M256 192h512c35.3 0 64 28.7 64 64v512c0 35.3-28.7 64-64 64H256c-35.3 0-64-28.7-64-64V256c0-35.3 28.7-64 64-64zm0-64c-70.7 0-128 57.3-128 128v512c0 70.7 57.3 128 128 128h512c70.7 0 128-57.3 128-128V256c0-70.7-57.3-128-128-128H256zm128 320c17.7 0 32 14.3 32 32v128c0 17.7-14.3 32-32 32s-32-14.3-32-32V480c0-17.7 14.3-32 32-32zm256 0c17.7 0 32 14.3 32 32v128c0 17.7-14.3 32-32 32s-32-14.3-32-32V480c0-17.7 14.3-32 32-32z"/>
        </svg>
      </a>
      <a href="#" target="_blank" rel="noopener noreferrer" :class="$style.socialLink">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="24" height="24" fill="currentColor">
          <path d="M512 64C264.6 64 64 238.7 64 448c0 114.9 66.9 216.1 170.9 282.2-4.4 16.5-28.4 106.5-29.2 110.1 0 0-0.6 2.5 1.3 3.4 1.9 0.9 3.7-0.4 3.7-0.4 4.9-0.7 94.2-61.9 109.7-72.2 56.1 20.7 119.5 32.9 191.6 32.9 247.4 0 448-174.7 448-384S759.4 64 512 64z"/>
        </svg>
      </a>
    </div>
    <BackToTop />
  </main>
</template>

<script setup>
import BackToTop from '@/components/ui/BackToTop.vue';
</script>

<style module>
.mainContent {
  /* 网格定位 */
  grid-column: 1; /* 移动端：占据唯一内容列 */
  grid-row: 2 / 3;    /* 占据第二行 (Header 和 Footer 之间) */
  /* 设置内边距，避免内容紧贴 Header/Footer */
  padding-top: var(--space-xl);
  padding-bottom: var(--space-xl);
  /* 确保内容区域至少有一定高度，防止页脚在内容少时跳上来 */
  min-height: calc(100vh - 250px); /* 估算值，根据 Header/Footer 实际高度调整 */
  position: relative;
}

.socialLinks {
  position: relative;
  margin-top: var(--space-xl);
  display: flex;
  gap: var(--space-m);
}

.socialLink {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(30, 32, 44, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--color-text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
}

.socialLink:hover {
  background-color: rgba(30, 32, 44, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  color: var(--color-accent);
}

@media (min-width: 1024px) { /* 桌面断点 */
  .mainContent {
    /* 桌面端：占据中间内容列 (由 .layoutGrid 定义) */
    grid-column: 2;
  }
}

@media (max-width: 768px) {
  .socialLinks {
    bottom: var(--space-l);
    left: var(--space-l);
  }

  .socialLink {
    width: 36px;
    height: 36px;
  }
}
</style>