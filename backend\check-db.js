import Database from 'better-sqlite3';

const db = new Database('database.sqlite');

console.log('Articles table schema:');
const schema = db.prepare('PRAGMA table_info(articles)').all();
schema.forEach(col => {
  console.log(`${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.dflt_value ? `DEFAULT ${col.dflt_value}` : ''}`);
});

console.log('\nArticle_images table schema:');
try {
  const imageSchema = db.prepare('PRAGMA table_info(article_images)').all();
  imageSchema.forEach(col => {
    console.log(`${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.dflt_value ? `DEFAULT ${col.dflt_value}` : ''}`);
  });
} catch (error) {
  console.log('Table does not exist');
}

console.log('\nTest article data:');
try {
  const article = db.prepare('SELECT * FROM articles WHERE id = 19').get();
  console.log(article);
} catch (error) {
  console.log('No article found');
}

db.close();
