// /src/stores/navigation.js (Pinia Store)
import { defineStore } from 'pinia';
import { ref } from 'vue';
import router from '@/router'; // 导入 router 实例

export const useNavigationStore = defineStore('navigation', () => {
  // State: 是否显示导航菜单
  const isMenuOpen = ref(false);

  // 筛选条件
  const selectedYear = ref('');
  const selectedCategory = ref('');

  function updateYear(year) {
    selectedYear.value = year;
  }

  function updateCategory(category) {
    selectedCategory.value = category;
  }

  // Action: 切换菜单显示状态
  function toggleMenu() {
    isMenuOpen.value = !isMenuOpen.value;
    // 可选：菜单打开时阻止背景滚动
    // document.body.style.overflow = isMenuOpen.value ? 'hidden' : '';
  }

  // Action: 打开菜单
  function openMenu() {
    if (!isMenuOpen.value) {
      isMenuOpen.value = true;
      // document.body.style.overflow = 'hidden';
    }
  }

  // Action: 关闭菜单
  function closeMenu() {
    if (isMenuOpen.value) {
      isMenuOpen.value = false;
      // document.body.style.overflow = '';
    }
  }

  // 监听路由变化，自动关闭菜单 (提升用户体验)
  // 注意：在 store 初始化时 router 可能还未完全准备好，
  // 使用 afterEach 可以确保在导航完成后执行。
  let removeGuard = null;
  const registerRouteChangeHook = () => {
      if (removeGuard) removeGuard(); // 防止重复注册
      removeGuard = router.afterEach(() => {
          closeMenu();
      });
  };

  // 在 store 初始化时注册钩子
  registerRouteChangeHook();


  // 返回 state 和 actions
  return { 
    isMenuOpen, 
    toggleMenu, 
    openMenu, 
    closeMenu, 
    selectedYear, 
    selectedCategory, 
    updateYear, 
    updateCategory 
  };
});