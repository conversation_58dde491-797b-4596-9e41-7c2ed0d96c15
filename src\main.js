// /src/main.js
import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'

// 引入全局 CSS
import '@/styles/global.css'

// --- 可选: Web Font Loader ---
// import WebFont from 'webfontloader';
// WebFont.load({
//   google: { families: ['Source Serif Pro:400,700,400italic', 'Inter:400,700'] }
//   // custom: { families: ['MyFont'], urls: ['/fonts.css'] }
// });
// --- 结束: Web Font Loader ---

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)

app.mount('#app')