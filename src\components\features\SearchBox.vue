<!-- /src/components/features/SearchBox.vue -->
<template>
  <div :class="$style.searchContainer">
    <SearchResultModal
      :is-open="showResults"
      :query="searchQuery"
      @close="closeResults"
      @select-article="handleSelectArticle"
    />
    <ArticleModal
      v-if="showArticleModal"
      :slug="selectedSlug"
      :is-open="showArticleModal"
      @close="closeArticleModal"
    />
    <button
      :class="$style.searchButton"
      @click="toggleSearch"
      :aria-expanded="isOpen.toString()"
      aria-label="搜索"
      type="button"
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
      >
        <circle cx="11" cy="11" r="8"></circle>
        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
      </svg>
    </button>
    <Transition name="fade">
      <div v-if="isOpen" :class="$style.searchOverlay" @click="closeSearch"></div>
    </Transition>
    <Transition name="search-panel">
      <div
        v-if="isOpen"
        :class="$style.searchPanel"
      >
        <div :class="$style.searchHeader">
          <h2 :class="$style.searchTitle">搜索文章</h2>
          <button
            :class="$style.closeButton"
            @click="closeSearch"
            aria-label="关闭搜索"
            type="button"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div :class="$style.searchInputWrapper">
          <svg
            :class="$style.searchIcon"
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <circle cx="11" cy="11" r="8"></circle>
            <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
          </svg>
          <input
            type="search"
            :class="$style.searchInput"
            v-model="searchQuery"
            placeholder="输入关键词搜索..."
            @keyup.enter="handleSearch"
            @keyup.esc="closeSearch"
          />
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref, useCssModule } from 'vue';
const $style = useCssModule();
import { useRouter } from 'vue-router';
import SearchResultModal from './SearchResultModal.vue';
import ArticleModal from './ArticleModal.vue';

const router = useRouter();

const isOpen = ref(false);
const selectedSlug = ref('');
const showArticleModal = ref(false);
const searchQuery = ref('');
const showResults = ref(false);

const toggleSearch = () => {
  isOpen.value = !isOpen.value;
  if (isOpen.value) {
    // 在下一个 tick 聚焦输入框
    setTimeout(() => {
      document.querySelector('input[type="search"]')?.focus();
    });
    // 禁止背景滚动
    document.body.style.overflow = 'hidden';
  } else {
    // 恢复背景滚动
    document.body.style.overflow = '';
  }
};

const closeSearch = () => {
  isOpen.value = false;
  document.body.style.overflow = '';
};

const handleSearch = () => {
  const query = searchQuery.value.trim();
  if (query) {
    console.log('执行搜索，关键词:', query);
    // 先关闭搜索面板
    isOpen.value = false;
    // 确保搜索结果模态框关闭后再打开
    showResults.value = false;
    // 强制刷新弹窗，确保重新加载搜索结果
    setTimeout(() => {
      showResults.value = true;
      document.body.style.overflow = 'hidden'; // 保持背景不可滚动
    }, 100);
  }
};

const closeResults = () => {
  showResults.value = false;
};

const handleSelectArticle = (slug) => {
  console.log('选中文章slug:', slug);
  showResults.value = false;
  isOpen.value = false;
  document.body.style.overflow = '';
  selectedSlug.value = slug;
  showArticleModal.value = true;
};

const closeArticleModal = () => {
  showArticleModal.value = false;
};
</script>

<style module>
.searchContainer {
  position: relative;
}

.searchOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 999;
}

.searchButton {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(30, 32, 44, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: var(--color-text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(4px);
  cursor: pointer;
}

.searchButton:hover {
  background-color: rgba(30, 32, 44, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  color: var(--color-accent);
}

.searchPanel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(30, 32, 44, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-soft);
  padding: var(--space-l);
  backdrop-filter: blur(12px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  min-width: 480px;
}

.searchHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-m);
}

.searchTitle {
  font-size: 1.5rem;
  font-weight: 500;
  color: var(--color-text-primary);
  margin: 0;
  letter-spacing: 0.02em;
}

.closeButton {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: var(--space-xs);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.closeButton:hover {
  color: var(--color-text-primary);
  background-color: rgba(255, 255, 255, 0.1);
}

.searchInputWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.searchIcon {
  position: absolute;
  left: var(--space-m);
  color: var(--color-text-secondary);
  pointer-events: none;
}

.searchInput {
  width: 100%;
  height: 48px;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-soft);
  color: var(--color-text-primary);
  padding: 0 var(--space-l) 0 calc(var(--space-l) + 24px);
  font-size: 1.1rem;
  transition: all 0.3s ease;
  letter-spacing: 0.02em;
}

.searchInput:focus {
  outline: none;
  border-color: var(--color-accent);
  background-color: rgba(255, 255, 255, 0.1);
}

.searchInput::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.search-panel-enter-active,
.search-panel-leave-active {
  transition: all 0.3s ease;
}

.search-panel-enter-from,
.search-panel-leave-to {
  opacity: 0;
  transform: translate(-50%, -48%) scale(0.95);
}

@media (max-width: 768px) {
  .searchPanel {
    min-width: 300px;
    width: 90%;
    max-width: 480px;
  }

  .searchInput {
    height: 42px;
    font-size: 1rem;
  }

  .searchButton {
    width: 36px;
    height: 36px;
  }

  .searchPanel {
    padding: var(--space-m);
  }
}
</style>