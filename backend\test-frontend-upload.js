import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';

const BASE_URL = 'http://localhost:3001/api';

// 测试用的管理员凭据
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123456'
};

let authToken = '';

// 登录获取token
async function login() {
  try {
    const response = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(ADMIN_CREDENTIALS)
    });

    const result = await response.json();
    if (result.success) {
      authToken = result.data.token;
      console.log('✓ 登录成功，Token:', authToken.substring(0, 20) + '...');
      return true;
    } else {
      console.log('✗ 登录失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('✗ 登录请求失败:', error.message);
    return false;
  }
}

// 测试API健康检查
async function testHealthCheck() {
  try {
    console.log('\n=== 测试API健康检查 ===');
    const response = await fetch(`${BASE_URL}/health`);
    const result = await response.json();
    console.log('✓ API健康检查通过:', result.status);
    return true;
  } catch (error) {
    console.log('✗ API健康检查失败:', error.message);
    return false;
  }
}

// 测试认证状态
async function testAuthStatus() {
  try {
    console.log('\n=== 测试认证状态 ===');
    const response = await fetch(`${BASE_URL}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (response.ok) {
      const result = await response.json();
      console.log('✓ 认证状态正常，用户:', result.data.username);
      return true;
    } else {
      console.log('✗ 认证状态异常，状态码:', response.status);
      return false;
    }
  } catch (error) {
    console.log('✗ 认证状态检查失败:', error.message);
    return false;
  }
}

// 模拟前端图片上传请求
async function testFrontendStyleUpload() {
  try {
    console.log('\n=== 模拟前端图片上传请求 ===');
    
    // 创建测试图片文件
    const testImagePath = path.join(process.cwd(), 'test-frontend.jpg');
    const existingImagePath = path.join(process.cwd(), '..', 'public', 'articles', 'img', '1.jpg');
    
    if (fs.existsSync(existingImagePath)) {
      fs.copyFileSync(existingImagePath, testImagePath);
      console.log('✓ 测试图片文件准备完成');
    } else {
      console.log('✗ 没有找到测试图片文件');
      return false;
    }

    // 使用FormData模拟前端上传
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));

    console.log('发送上传请求到:', `${BASE_URL}/images/upload`);
    console.log('认证头:', `Bearer ${authToken.substring(0, 20)}...`);

    const response = await fetch(`${BASE_URL}/images/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        ...formData.getHeaders()
      },
      body: formData
    });

    console.log('响应状态码:', response.status);
    console.log('响应头:', Object.fromEntries(response.headers.entries()));

    const result = await response.json();
    
    // 清理测试文件
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
    }

    if (result.success) {
      console.log('✓ 前端风格图片上传成功');
      console.log(`  文件名: ${result.data.filename}`);
      console.log(`  URL: ${result.data.url}`);
      return result.data.id;
    } else {
      console.log('✗ 前端风格图片上传失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('✗ 前端风格图片上传异常:', error.message);
    console.log('错误详情:', error);
    return false;
  }
}

// 测试CORS配置
async function testCORS() {
  try {
    console.log('\n=== 测试CORS配置 ===');
    
    // 模拟浏览器的预检请求
    const optionsResponse = await fetch(`${BASE_URL}/images/upload`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:5175',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'authorization,content-type'
      }
    });

    console.log('OPTIONS请求状态码:', optionsResponse.status);
    console.log('CORS响应头:');
    const corsHeaders = {};
    optionsResponse.headers.forEach((value, key) => {
      if (key.toLowerCase().includes('access-control')) {
        corsHeaders[key] = value;
      }
    });
    console.log(corsHeaders);

    return optionsResponse.status === 200 || optionsResponse.status === 204;
  } catch (error) {
    console.log('✗ CORS测试失败:', error.message);
    return false;
  }
}

// 检查网络连接
async function testNetworkConnection() {
  try {
    console.log('\n=== 测试网络连接 ===');
    
    // 测试基础连接
    const baseResponse = await fetch('http://localhost:3001');
    console.log('基础连接状态码:', baseResponse.status);
    
    // 测试API基础路径
    const apiResponse = await fetch(`${BASE_URL}/health`);
    console.log('API路径连接状态码:', apiResponse.status);
    
    return true;
  } catch (error) {
    console.log('✗ 网络连接测试失败:', error.message);
    return false;
  }
}

// 清理测试数据
async function cleanup(imageId) {
  if (!imageId) return;
  
  try {
    await fetch(`${BASE_URL}/images/${imageId}`, {
      method: 'DELETE',
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    console.log('✓ 测试图片已清理');
  } catch (error) {
    console.log('⚠ 清理测试图片失败');
  }
}

// 主测试函数
async function runDiagnostics() {
  console.log('🔍 开始图片上传网络错误诊断...\n');
  
  let uploadedImageId = null;
  
  try {
    // 1. 测试网络连接
    const networkOk = await testNetworkConnection();
    
    // 2. 测试API健康检查
    const healthOk = await testHealthCheck();
    
    // 3. 登录
    const loginOk = await login();
    if (!loginOk) {
      console.log('❌ 诊断终止：无法登录');
      return;
    }
    
    // 4. 测试认证状态
    const authOk = await testAuthStatus();
    
    // 5. 测试CORS配置
    const corsOk = await testCORS();
    
    // 6. 模拟前端图片上传
    uploadedImageId = await testFrontendStyleUpload();
    
    // 输出诊断结果
    console.log('\n🎯 诊断结果汇总:');
    console.log(`网络连接: ${networkOk ? '✅ 正常' : '❌ 异常'}`);
    console.log(`API健康检查: ${healthOk ? '✅ 正常' : '❌ 异常'}`);
    console.log(`用户认证: ${authOk ? '✅ 正常' : '❌ 异常'}`);
    console.log(`CORS配置: ${corsOk ? '✅ 正常' : '❌ 异常'}`);
    console.log(`图片上传: ${uploadedImageId ? '✅ 正常' : '❌ 异常'}`);
    
    const allOk = networkOk && healthOk && authOk && corsOk && uploadedImageId;
    console.log(`\n🏆 整体诊断结果: ${allOk ? '✅ 系统正常' : '❌ 存在问题'}`);
    
    if (!allOk) {
      console.log('\n💡 建议检查项目:');
      if (!networkOk) console.log('- 检查后端服务器是否正常运行');
      if (!healthOk) console.log('- 检查API路由配置');
      if (!authOk) console.log('- 检查认证Token和权限');
      if (!corsOk) console.log('- 检查CORS配置');
      if (!uploadedImageId) console.log('- 检查图片上传API和文件处理');
    }
    
  } finally {
    // 清理测试数据
    if (uploadedImageId) {
      await cleanup(uploadedImageId);
    }
  }
}

runDiagnostics().catch(console.error);
