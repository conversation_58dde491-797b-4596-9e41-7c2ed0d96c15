<!-- /src/components/features/ArticleTags.vue -->
<template>
  <div :class="$style.tags">
    <span v-for="tag in tags" :key="tag" :class="$style.tag">#{{ tag }}</span>
  </div>
</template>

<script setup>
defineProps({
  tags: {
    type: Array,
    required: true
  }
});
</script>

<style module>
.tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-s);
  margin-top: var(--space-m);
  position: relative;
}

.tags:before {
  content: '';
  position: absolute;
  top: -10px;
  left: 0;
  width: 30px;
  height: 1px;
  background: rgba(var(--color-accent-rgb), 0.2);
}

.tag {
  color: var(--color-text-secondary);
  font-size: 0.85rem;
  font-weight: var(--font-weight-light);
  letter-spacing: var(--letter-spacing-meta);
  transition: all 0.25s ease;
  padding: 2px 8px;
  border-radius: 4px;
  display: inline-block;
  background: rgba(0, 0, 0, 0.15);
}

.tag:hover {
  color: var(--color-accent);
  transform: translateY(-1px);
  background-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style> 