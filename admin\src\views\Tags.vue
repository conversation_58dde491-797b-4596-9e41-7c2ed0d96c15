<template>
  <div class="tags">
    <div class="page-header">
      <h1>标签管理</h1>
      <el-button type="primary" @click="showDialog()">
        <el-icon><Plus /></el-icon>
        新建标签
      </el-button>
    </div>
    
    <el-card>
      <el-table :data="tags" v-loading="loading">
        <el-table-column prop="name" label="名称" />
        <el-table-column prop="slug" label="Slug" />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button size="small" @click="showDialog(row)">
              编辑
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="deleteTag(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 新建/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
    >
      <el-form :model="tagForm" :rules="rules" ref="formRef" label-width="80px">
        <el-form-item label="名称" prop="name">
          <el-input v-model="tagForm.name" placeholder="请输入标签名称" />
        </el-form-item>
        <el-form-item label="Slug" prop="slug">
          <el-input v-model="tagForm.slug" placeholder="URL友好的标识符" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveTag" :loading="saving">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import api from '@/api'

const loading = ref(false)
const saving = ref(false)
const tags = ref([])
const dialogVisible = ref(false)
const formRef = ref()
const editingTag = ref(null)

const tagForm = reactive({
  name: '',
  slug: ''
})

const rules = {
  name: [
    { required: true, message: '请输入标签名称', trigger: 'blur' }
  ],
  slug: [
    { required: true, message: '请输入Slug', trigger: 'blur' }
  ]
}

const dialogTitle = computed(() => {
  return editingTag.value ? '编辑标签' : '新建标签'
})

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取标签列表
const fetchTags = async () => {
  loading.value = true
  try {
    const response = await api.get('/tags')
    tags.value = response.data.data
  } catch (error) {
    console.error('获取标签列表失败:', error)
    ElMessage.error('获取标签列表失败')
  } finally {
    loading.value = false
  }
}

// 显示对话框
const showDialog = (tag = null) => {
  editingTag.value = tag
  
  if (tag) {
    Object.keys(tagForm).forEach(key => {
      tagForm[key] = tag[key] || ''
    })
  } else {
    Object.keys(tagForm).forEach(key => {
      tagForm[key] = ''
    })
  }
  
  dialogVisible.value = true
}

// 保存标签
const saveTag = async () => {
  if (!formRef.value) return
  
  await formRef.value.validate(async (valid) => {
    if (!valid) return
    
    saving.value = true
    try {
      if (editingTag.value) {
        await api.put(`/tags/${editingTag.value.id}`, tagForm)
        ElMessage.success('标签更新成功')
      } else {
        await api.post('/tags', tagForm)
        ElMessage.success('标签创建成功')
      }
      
      dialogVisible.value = false
      fetchTags()
    } catch (error) {
      console.error('保存标签失败:', error)
      ElMessage.error('保存失败')
    } finally {
      saving.value = false
    }
  })
}

// 删除标签
const deleteTag = async (tag) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除标签"${tag.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await api.delete(`/tags/${tag.id}`)
    ElMessage.success('标签已删除')
    fetchTags()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除标签失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

onMounted(() => {
  fetchTags()
})
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}
</style>
