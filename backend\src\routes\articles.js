import express from 'express';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';
import db from '../config/database.js';
import { syncSingleArticle, syncArticlesToFrontend } from '../utils/syncToFrontend.js';

const router = express.Router();

// 获取文章列表
router.get('/', (req, res, next) => {
  try {
    const { page = 1, limit = 10, status, featured, search } = req.query;
    const offset = (page - 1) * limit;

    let query = 'SELECT * FROM articles';
    let countQuery = 'SELECT COUNT(*) as total FROM articles';
    const params = [];
    const conditions = [];

    // 添加筛选条件
    if (status) {
      conditions.push('status = ?');
      params.push(status);
    }

    if (featured !== undefined) {
      conditions.push('featured = ?');
      params.push(featured === 'true' ? 1 : 0);
    }

    if (search) {
      conditions.push('(title LIKE ? OR description LIKE ? OR content LIKE ?)');
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    if (conditions.length > 0) {
      const whereClause = ' WHERE ' + conditions.join(' AND ');
      query += whereClause;
      countQuery += whereClause;
    }

    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), parseInt(offset));

    // 获取文章列表
    const articles = db.prepare(query).all(...params);
    
    // 获取总数
    const countParams = params.slice(0, -2); // 移除 limit 和 offset
    const { total } = db.prepare(countQuery).get(...countParams);

    res.json({
      success: true,
      data: {
        articles,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

// 获取单篇文章
router.get('/:id', (req, res, next) => {
  try {
    const { id } = req.params;
    
    const article = db.prepare('SELECT * FROM articles WHERE id = ? OR slug = ?').get(id, id);
    
    if (!article) {
      return res.status(404).json({
        success: false,
        message: '文章不存在'
      });
    }

    // 获取文章的分类和标签
    const categories = db.prepare(`
      SELECT c.* FROM categories c
      JOIN article_categories ac ON c.id = ac.category_id
      WHERE ac.article_id = ?
    `).all(article.id);

    const tags = db.prepare(`
      SELECT t.* FROM tags t
      JOIN article_tags at ON t.id = at.tag_id
      WHERE at.article_id = ?
    `).all(article.id);

    // 获取文章配图
    const articleImages = db.prepare(`
      SELECT i.*, ai.sort_order FROM images i
      JOIN article_images ai ON i.id = ai.image_id
      WHERE ai.article_id = ?
      ORDER BY ai.sort_order
    `).all(article.id);

    // 为配图添加URL
    const imagesWithUrl = articleImages.map(img => ({
      ...img,
      url: `/articles/img/${img.filename}`
    }));

    res.json({
      success: true,
      data: {
        ...article,
        categories,
        tags,
        articleImages: imagesWithUrl
      }
    });
  } catch (error) {
    next(error);
  }
});

// 创建文章（需要认证）
router.post('/', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const {
      slug,
      title,
      description,
      content,
      cover_image,
      auxiliary_content,
      word_count,
      reading_time,
      status = 'draft',
      featured = false,
      categories = [],
      tags = [],
      articleImages = []
    } = req.body;

    if (!slug || !title || !content) {
      return res.status(400).json({
        success: false,
        message: '标题、slug 和内容不能为空'
      });
    }

    // 检查 slug 是否已存在
    const existingArticle = db.prepare('SELECT id FROM articles WHERE slug = ?').get(slug);
    if (existingArticle) {
      return res.status(409).json({
        success: false,
        message: 'Slug 已存在'
      });
    }

    // 开始事务
    const transaction = db.transaction(() => {
      // 插入文章
      const insertArticle = db.prepare(`
        INSERT INTO articles (slug, title, description, content, cover_image, auxiliary_content, word_count, reading_time, status, featured)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      const result = insertArticle.run(slug, title, description, content, cover_image, auxiliary_content, word_count, reading_time, status, featured ? 1 : 0);
      const articleId = result.lastInsertRowid;

      // 处理分类关联
      if (categories.length > 0) {
        const insertCategory = db.prepare('INSERT OR IGNORE INTO article_categories (article_id, category_id) VALUES (?, ?)');
        categories.forEach(categoryId => {
          insertCategory.run(articleId, categoryId);
        });
      }

      // 处理标签关联
      if (tags.length > 0) {
        const insertTag = db.prepare('INSERT OR IGNORE INTO article_tags (article_id, tag_id) VALUES (?, ?)');
        tags.forEach(tagId => {
          insertTag.run(articleId, tagId);
        });
      }

      // 处理文章配图关联
      if (articleImages.length > 0) {
        const insertArticleImage = db.prepare('INSERT OR IGNORE INTO article_images (article_id, image_id, sort_order) VALUES (?, ?, ?)');
        articleImages.forEach((imageId, index) => {
          insertArticleImage.run(articleId, imageId, index);
        });
      }

      return articleId;
    });

    const articleId = transaction();

    // 获取创建的文章
    const newArticle = db.prepare('SELECT * FROM articles WHERE id = ?').get(articleId);

    // 如果文章已发布，同步到前端
    if (status === 'published') {
      syncSingleArticle(db, articleId).catch(err => {
        console.error('同步文章到前端失败:', err);
      });
    }

    res.status(201).json({
      success: true,
      message: '文章创建成功',
      data: newArticle
    });
  } catch (error) {
    next(error);
  }
});

// 更新文章（需要认证）
router.put('/:id', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      slug,
      title,
      description,
      content,
      cover_image,
      auxiliary_content,
      word_count,
      reading_time,
      status,
      featured,
      categories = [],
      tags = [],
      articleImages = []
    } = req.body;

    if (!title || !content) {
      return res.status(400).json({
        success: false,
        message: '标题和内容不能为空'
      });
    }

    // 检查文章是否存在
    const existingArticle = db.prepare('SELECT * FROM articles WHERE id = ? OR slug = ?').get(id, id);
    if (!existingArticle) {
      return res.status(404).json({
        success: false,
        message: '文章不存在'
      });
    }

    // 如果更新了slug，检查新slug是否已存在
    if (slug && slug !== existingArticle.slug) {
      const slugExists = db.prepare('SELECT id FROM articles WHERE slug = ? AND id != ?').get(slug, existingArticle.id);
      if (slugExists) {
        return res.status(409).json({
          success: false,
          message: 'Slug 已存在'
        });
      }
    }

    // 开始事务
    const transaction = db.transaction(() => {
      // 更新文章
      const updateArticle = db.prepare(`
        UPDATE articles
        SET slug = ?, title = ?, description = ?, content = ?, cover_image = ?,
            auxiliary_content = ?, word_count = ?, reading_time = ?,
            status = ?, featured = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `);

      updateArticle.run(
        slug || existingArticle.slug,
        title,
        description || existingArticle.description,
        content,
        cover_image || existingArticle.cover_image,
        auxiliary_content || existingArticle.auxiliary_content,
        word_count || existingArticle.word_count,
        reading_time || existingArticle.reading_time,
        status || existingArticle.status,
        featured !== undefined ? (featured ? 1 : 0) : existingArticle.featured,
        existingArticle.id
      );

      // 删除现有的分类、标签和配图关联
      db.prepare('DELETE FROM article_categories WHERE article_id = ?').run(existingArticle.id);
      db.prepare('DELETE FROM article_tags WHERE article_id = ?').run(existingArticle.id);
      db.prepare('DELETE FROM article_images WHERE article_id = ?').run(existingArticle.id);

      // 重新添加分类关联
      if (categories.length > 0) {
        const insertCategory = db.prepare('INSERT OR IGNORE INTO article_categories (article_id, category_id) VALUES (?, ?)');
        categories.forEach(categoryId => {
          insertCategory.run(existingArticle.id, categoryId);
        });
      }

      // 重新添加标签关联
      if (tags.length > 0) {
        const insertTag = db.prepare('INSERT OR IGNORE INTO article_tags (article_id, tag_id) VALUES (?, ?)');
        tags.forEach(tagId => {
          insertTag.run(existingArticle.id, tagId);
        });
      }

      // 重新添加文章配图关联
      if (articleImages.length > 0) {
        const insertArticleImage = db.prepare('INSERT OR IGNORE INTO article_images (article_id, image_id, sort_order) VALUES (?, ?, ?)');
        articleImages.forEach((imageId, index) => {
          insertArticleImage.run(existingArticle.id, imageId, index);
        });
      }

      return existingArticle.id;
    });

    transaction();

    // 获取更新后的文章
    const updatedArticle = db.prepare('SELECT * FROM articles WHERE id = ?').get(existingArticle.id);

    // 同步到前端（无论是否发布，都需要同步以处理状态变化）
    syncSingleArticle(db, existingArticle.id).catch(err => {
      console.error('同步文章到前端失败:', err);
    });

    res.json({
      success: true,
      message: '文章更新成功',
      data: updatedArticle
    });
  } catch (error) {
    next(error);
  }
});

// 删除文章（需要认证）
router.delete('/:id', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const { id } = req.params;

    const article = db.prepare('SELECT * FROM articles WHERE id = ? OR slug = ?').get(id, id);

    if (!article) {
      return res.status(404).json({
        success: false,
        message: '文章不存在'
      });
    }

    // 删除文章（级联删除会自动删除关联的分类和标签）
    db.prepare('DELETE FROM articles WHERE id = ?').run(article.id);

    res.json({
      success: true,
      message: '文章删除成功'
    });
  } catch (error) {
    next(error);
  }
});

// 发布文章
router.post('/:id/publish', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const { id } = req.params;

    const article = db.prepare('SELECT * FROM articles WHERE id = ? OR slug = ?').get(id, id);

    if (!article) {
      return res.status(404).json({
        success: false,
        message: '文章不存在'
      });
    }

    // 更新文章状态为已发布
    db.prepare(`
      UPDATE articles
      SET status = 'published', published_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(article.id);

    const updatedArticle = db.prepare('SELECT * FROM articles WHERE id = ?').get(article.id);

    // 同步到前端
    syncSingleArticle(db, article.id).catch(err => {
      console.error('同步文章到前端失败:', err);
    });

    res.json({
      success: true,
      message: '文章发布成功',
      data: updatedArticle
    });
  } catch (error) {
    next(error);
  }
});

// 取消发布文章
router.post('/:id/unpublish', authenticateToken, requireAdmin, (req, res, next) => {
  try {
    const { id } = req.params;

    const article = db.prepare('SELECT * FROM articles WHERE id = ? OR slug = ?').get(id, id);

    if (!article) {
      return res.status(404).json({
        success: false,
        message: '文章不存在'
      });
    }

    // 更新文章状态为草稿
    db.prepare(`
      UPDATE articles
      SET status = 'draft', updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(article.id);

    const updatedArticle = db.prepare('SELECT * FROM articles WHERE id = ?').get(article.id);

    // 同步到前端（删除已发布的文件）
    syncSingleArticle(db, article.id).catch(err => {
      console.error('同步文章到前端失败:', err);
    });

    res.json({
      success: true,
      message: '文章已设为草稿',
      data: updatedArticle
    });
  } catch (error) {
    next(error);
  }
});

// 手动同步所有文章到前端
router.post('/sync', authenticateToken, requireAdmin, async (req, res, next) => {
  try {
    const result = await syncArticlesToFrontend(db);

    if (result.success) {
      res.json({
        success: true,
        message: `成功同步 ${result.count} 篇文章到前端`,
        data: result
      });
    } else {
      res.status(500).json({
        success: false,
        message: '同步失败',
        error: result.error
      });
    }
  } catch (error) {
    next(error);
  }
});

export default router;
