import db from './src/config/database.js';

console.log('检查数据库中的auxiliary_content字段...\n');

try {
  // 检查articles表结构
  const tableInfo = db.prepare("PRAGMA table_info(articles)").all();
  console.log('Articles表结构:');
  tableInfo.forEach(column => {
    console.log(`  ${column.name}: ${column.type} ${column.notnull ? 'NOT NULL' : 'NULL'} ${column.dflt_value ? `DEFAULT ${column.dflt_value}` : ''}`);
  });

  // 检查是否有auxiliary_content字段
  const hasAuxiliaryContent = tableInfo.some(column => column.name === 'auxiliary_content');
  console.log(`\nAuxiliary_content字段存在: ${hasAuxiliaryContent ? '✓' : '✗'}`);

  if (hasAuxiliaryContent) {
    // 查询所有文章的auxiliary_content
    const articles = db.prepare(`
      SELECT id, title, auxiliary_content, status 
      FROM articles 
      WHERE auxiliary_content IS NOT NULL AND auxiliary_content != ''
      ORDER BY created_at DESC
    `).all();

    console.log(`\n包含辅助内容的文章数量: ${articles.length}`);
    
    if (articles.length > 0) {
      console.log('\n文章辅助内容详情:');
      articles.forEach(article => {
        console.log(`\n文章ID: ${article.id}`);
        console.log(`标题: ${article.title}`);
        console.log(`状态: ${article.status}`);
        console.log(`辅助内容: ${article.auxiliary_content}`);
        console.log('---');
      });
    } else {
      console.log('\n没有找到包含辅助内容的文章');
    }

    // 检查所有文章的auxiliary_content字段
    const allArticles = db.prepare(`
      SELECT id, title, auxiliary_content, status 
      FROM articles 
      ORDER BY created_at DESC
    `).all();

    console.log(`\n所有文章的auxiliary_content字段状态:`);
    allArticles.forEach(article => {
      const hasContent = article.auxiliary_content && article.auxiliary_content.trim() !== '';
      console.log(`  ${article.title}: ${hasContent ? '有内容' : '空/NULL'} (${article.auxiliary_content || 'NULL'})`);
    });
  }

} catch (error) {
  console.error('检查过程中发生错误:', error.message);
}
