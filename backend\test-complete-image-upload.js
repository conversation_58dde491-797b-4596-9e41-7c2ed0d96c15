import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';

const BASE_URL = 'http://localhost:3001/api';
const FRONTEND_URL = 'http://localhost:5174';

async function testCompleteImageUpload() {
  try {
    console.log('🔧 完整测试图片上传功能...\n');
    
    // 1. 登录获取Token
    console.log('=== 步骤1：登录获取Token ===');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123456'
      })
    });

    const loginResult = await loginResponse.json();
    
    if (!loginResult.success) {
      console.log('✗ 登录失败:', loginResult.message);
      return false;
    }

    const token = loginResult.data.token;
    console.log('✓ 登录成功');
    console.log(`  Token: ${token.substring(0, 30)}...`);

    // 2. 检查图片目录
    console.log('\n=== 步骤2：检查图片目录 ===');
    const uploadDir = path.join(process.cwd(), '..', 'public', 'articles', 'img');
    console.log('图片上传目录:', uploadDir);
    
    if (!fs.existsSync(uploadDir)) {
      console.log('✗ 图片目录不存在，正在创建...');
      fs.mkdirSync(uploadDir, { recursive: true });
      console.log('✓ 图片目录创建成功');
    } else {
      console.log('✓ 图片目录存在');
    }

    // 3. 准备测试图片
    console.log('\n=== 步骤3：准备测试图片 ===');
    const testImagePath = path.join(process.cwd(), 'test-complete-upload.jpg');
    const existingImagePath = path.join(uploadDir, '1.jpg');
    
    if (fs.existsSync(existingImagePath)) {
      fs.copyFileSync(existingImagePath, testImagePath);
      console.log('✓ 测试图片准备完成');
    } else {
      console.log('✗ 没有找到测试图片文件');
      return false;
    }

    // 4. 模拟前端上传请求
    console.log('\n=== 步骤4：模拟前端上传请求 ===');
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));

    console.log('发送上传请求...');
    console.log(`  URL: ${BASE_URL}/images/upload`);
    console.log(`  Origin: ${FRONTEND_URL}`);
    
    const uploadResponse = await fetch(`${BASE_URL}/images/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Origin': FRONTEND_URL,
        ...formData.getHeaders()
      },
      body: formData
    });

    console.log(`响应状态码: ${uploadResponse.status}`);
    
    const uploadResult = await uploadResponse.json();
    console.log('上传结果:', JSON.stringify(uploadResult, null, 2));
    
    // 清理测试文件
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
    }

    if (!uploadResult.success) {
      console.log('✗ 图片上传失败:', uploadResult.message);
      return false;
    }

    console.log('✓ 图片上传成功');
    const uploadedImage = uploadResult.data;
    console.log(`  文件名: ${uploadedImage.filename}`);
    console.log(`  URL: ${uploadedImage.url}`);
    console.log(`  保存路径: ${uploadedImage.path}`);

    // 5. 验证文件是否保存到正确位置
    console.log('\n=== 步骤5：验证文件保存 ===');
    const savedFilePath = path.join(uploadDir, uploadedImage.filename);
    if (fs.existsSync(savedFilePath)) {
      console.log('✓ 图片文件已保存到项目目录');
      console.log(`  保存路径: ${savedFilePath}`);
      
      const stats = fs.statSync(savedFilePath);
      console.log(`  文件大小: ${stats.size} bytes`);
    } else {
      console.log('✗ 图片文件未保存到项目目录');
      return false;
    }

    // 6. 测试图片访问
    console.log('\n=== 步骤6：测试图片访问 ===');
    const imageUrl = `http://localhost:3001${uploadedImage.url}`;
    console.log(`测试图片访问: ${imageUrl}`);
    
    const imageResponse = await fetch(imageUrl);
    console.log(`图片访问状态码: ${imageResponse.status}`);
    
    if (imageResponse.status === 200) {
      console.log('✓ 图片可以正常访问');
      console.log(`  Content-Type: ${imageResponse.headers.get('content-type')}`);
    } else {
      console.log('✗ 图片无法访问');
      return false;
    }

    // 7. 测试获取图片列表
    console.log('\n=== 步骤7：测试获取图片列表 ===');
    const listResponse = await fetch(`${BASE_URL}/images`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    const listResult = await listResponse.json();
    if (listResult.success) {
      console.log('✓ 图片列表获取成功');
      console.log(`  总图片数: ${listResult.data.total}`);
      
      // 查找刚上传的图片
      const foundImage = listResult.data.images.find(img => img.id === uploadedImage.id);
      if (foundImage) {
        console.log('✓ 刚上传的图片在列表中找到');
      } else {
        console.log('✗ 刚上传的图片在列表中未找到');
      }
    } else {
      console.log('✗ 图片列表获取失败:', listResult.message);
    }

    // 8. 清理测试图片
    console.log('\n=== 步骤8：清理测试图片 ===');
    try {
      const deleteResponse = await fetch(`${BASE_URL}/images/${uploadedImage.id}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      const deleteResult = await deleteResponse.json();
      if (deleteResult.success) {
        console.log('✓ 测试图片已清理');
      } else {
        console.log('⚠ 清理测试图片失败，请手动删除');
      }
    } catch (error) {
      console.log('⚠ 清理测试图片时出错:', error.message);
    }

    return true;
    
  } catch (error) {
    console.log('✗ 测试过程中发生错误:', error.message);
    console.log('错误详情:', error.stack);
    return false;
  }
}

// 运行测试
testCompleteImageUpload().then(success => {
  console.log('\n🏆 完整图片上传测试结果:', success ? '✅ 成功' : '❌ 失败');
  
  if (success) {
    console.log('\n🎉 图片上传功能完全正常！');
    console.log('\n📋 功能确认:');
    console.log('1. ✅ 后端接收图片上传请求');
    console.log('2. ✅ 图片保存到 public/articles/img 目录');
    console.log('3. ✅ 图片信息保存到数据库');
    console.log('4. ✅ 图片可以通过URL正常访问');
    console.log('5. ✅ 图片列表API正常工作');
    console.log('\n💡 前端使用说明:');
    console.log('1. 确保前端服务运行在 http://localhost:5174');
    console.log('2. 登录后访问图片管理页面');
    console.log('3. 使用上传按钮上传图片');
    console.log('4. 上传的图片会自动显示在图片列表中');
  } else {
    console.log('\n❌ 图片上传功能存在问题，请检查:');
    console.log('1. 后端服务器是否正常运行');
    console.log('2. 数据库连接是否正常');
    console.log('3. 图片目录权限是否正确');
    console.log('4. 网络连接是否正常');
  }
}).catch(console.error);
