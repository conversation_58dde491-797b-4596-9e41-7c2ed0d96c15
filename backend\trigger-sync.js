import { syncArticlesToFrontend } from './src/utils/syncToFrontend.js';
import db from './src/config/database.js';
import fs from 'fs';

console.log('手动触发数据同步...\n');

try {
  await syncArticlesToFrontend(db);
  console.log('\n✓ 数据同步完成');

  // 验证同步结果
  console.log('\n验证同步结果...');

  // 检查articles.js文件是否包含auxiliary_content
  const articlesPath = '../src/data/articles.js';
  
  if (fs.existsSync(articlesPath)) {
    const content = fs.readFileSync(articlesPath, 'utf8');
    const hasAuxiliaryContent = content.includes('auxiliary_content');
    console.log(`Articles.js包含auxiliary_content字段: ${hasAuxiliaryContent ? '✓' : '✗'}`);
    
    if (hasAuxiliaryContent) {
      // 提取auxiliary_content的值
      const matches = content.match(/auxiliary_content:\s*'([^']*)'|auxiliary_content:\s*"([^"]*)"/g);
      if (matches && matches.length > 0) {
        console.log('\n找到的auxiliary_content字段:');
        matches.forEach((match, index) => {
          console.log(`  ${index + 1}. ${match}`);
        });
      }
    }
  } else {
    console.log('✗ Articles.js文件不存在');
  }
  
} catch (error) {
  console.error('同步过程中发生错误:', error.message);
}
