// vite.config.js (项目根目录)
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path' // 确保导入 Node.js path 模块
import postcssNesting from 'postcss-nesting';
import autoprefixer from 'autoprefixer';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      // 确保路径别名 '@' 指向 './src' 目录
      '@': path.resolve(__dirname, './src'),
    },
  },
  css: {
    postcss: {
      plugins: [
        postcssNesting(), // 启用 CSS 嵌套
        autoprefixer(),   // 启用 autoprefixer
        // 其他 PostCSS 插件...
      ],
    },
    modules: {
      // 可选：自定义 CSS Modules 类名格式
      // generateScopedName: "[name]__[local]___[hash:base64:5]",
    }
  },
  server: {
    port: 5173, // 开发服务器端口
    open: true, // 自动打开浏览器 (可选)
    historyApiFallback: true // 启用HTML5历史模式
  },
  preview: {
    port: 4173,
    open: true,
    historyApiFallback: true // 确保预览服务器也支持HTML5历史模式
  },
  // 构建配置
  build: {
    // 生成静态资源的存放路径（相对于 outDir)
    assetsDir: 'assets',
    // 小于此阈值的导入或引用资源将内联为 base64 编码
    assetsInlineLimit: 4096,
    // 启用/禁用 CSS 代码拆分
    cssCodeSplit: true,
    // 构建后是否生成 source map 文件
    sourcemap: false,
    // 自定义底层的 Rollup 打包配置
    rollupOptions: {
      output: {
        // 静态资源分类打包
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
      }
    }
  }
})