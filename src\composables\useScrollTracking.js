// /src/composables/useScrollTracking.js
import { ref, onMounted, onUnmounted } from 'vue';

/**
 * 滚动跟踪composable，用于实现元素跟随页面滚动的效果
 * @param {Object} options 配置选项
 * @param {Number} options.threshold 滚动阈值，默认为100
 * @param {Number} options.offset 偏移量，默认为0
 * @returns {Object} 滚动跟踪相关的状态和方法
 */
export function useScrollTracking(options = {}) {
  const { 
    threshold = 100,
    offset = 0
  } = options;
  
  const elementRef = ref(null);
  const isSticky = ref(false);
  const initialTopOffset = ref(0);
  
  // 防抖函数
  function debounce(fn, delay) {
    let timer = null;
    return function() {
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => {
        fn.apply(this, arguments);
      }, delay);
    };
  }
  
  // 处理滚动事件
  const handleScroll = () => {
    if (!elementRef.value) return;
    
    const scrollPosition = window.scrollY;
    
    // 如果滚动位置超过初始位置加阈值，则启用粘性定位
    if (scrollPosition > initialTopOffset.value - threshold) {
      isSticky.value = true;
      
      // 计算最大滚动位置，防止元素超出页面底部
      const maxScroll = document.body.scrollHeight - window.innerHeight - elementRef.value.offsetHeight;
      const scrollTop = Math.min(
        scrollPosition - initialTopOffset.value + threshold + offset, 
        maxScroll
      );
      
      // 应用平滑的跟随效果
      elementRef.value.style.transform = `translateY(${scrollTop > 0 ? scrollTop : 0}px)`;
    } else {
      isSticky.value = false;
      elementRef.value.style.transform = 'translateY(0)';
    }
  };
  
  // 优化后的滚动事件处理函数
  const debouncedHandleScroll = debounce(handleScroll, 10);
  
  // 重新计算初始位置
  const recalculatePosition = () => {
    if (elementRef.value) {
      initialTopOffset.value = elementRef.value.getBoundingClientRect().top + window.scrollY;
      handleScroll();
    }
  };
  
  // 组件挂载时的处理
  onMounted(() => {
    // 获取初始位置
    if (elementRef.value) {
      initialTopOffset.value = elementRef.value.getBoundingClientRect().top + window.scrollY;
      
      // 设置初始样式
      elementRef.value.style.position = 'relative';
      elementRef.value.style.willChange = 'transform';
      elementRef.value.style.transition = 'transform 0.3s ease';
    }
    
    // 添加滚动监听，使用requestAnimationFrame提高性能
    window.addEventListener('scroll', () => {
      requestAnimationFrame(debouncedHandleScroll);
    }, { passive: true });
    
    // 初始化处理一次
    handleScroll();
    
    // 窗口大小变化时重新计算位置
    window.addEventListener('resize', recalculatePosition);
  });
  
  // 组件卸载时的处理
  onUnmounted(() => {
    // 移除滚动监听
    window.removeEventListener('scroll', () => {
      requestAnimationFrame(debouncedHandleScroll);
    });
    
    // 移除窗口大小变化监听
    window.removeEventListener('resize', recalculatePosition);
  });
  
  return {
    elementRef,
    isSticky,
    recalculatePosition
  };
}
