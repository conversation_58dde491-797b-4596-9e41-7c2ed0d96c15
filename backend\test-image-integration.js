import fetch from 'node-fetch';
import fs from 'fs/promises';
import path from 'path';
import FormData from 'form-data';

const BASE_URL = 'http://localhost:3001/api';
const FRONTEND_URL = 'http://localhost:5175';
const ADMIN_URL = 'http://localhost:5174';
let authToken = '';

// 端到端图片集成测试
async function testImageIntegration() {
  console.log('🖼️ 开始端到端图片集成测试...\n');

  try {
    // 1. 登录获取token
    console.log('1. 管理员登录');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123456'
      })
    });

    const loginResult = await loginResponse.json();
    if (!loginResult.success) {
      throw new Error('登录失败');
    }

    authToken = loginResult.data.token;
    console.log('✓ 登录成功');

    // 2. 测试图片API访问
    console.log('\n2. 测试图片API和静态文件访问');
    await testImageAPI();

    // 3. 测试管理界面图片显示
    console.log('\n3. 测试管理界面图片显示');
    await testAdminImageDisplay();

    // 4. 测试前端博客图片显示
    console.log('\n4. 测试前端博客图片显示');
    await testFrontendImageDisplay();

    // 5. 测试图片上传功能
    console.log('\n5. 测试图片上传功能');
    await testImageUpload();

    // 6. 测试文章中的图片集成
    console.log('\n6. 测试文章中的图片集成');
    await testArticleImageIntegration();

    console.log('\n🎉 端到端图片集成测试完成！');

  } catch (error) {
    console.error('❌ 图片集成测试失败:', error.message);
  }
}

// 测试图片API
async function testImageAPI() {
  try {
    // 测试图片列表API
    const imagesResponse = await fetch(`${BASE_URL}/images`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    const imagesResult = await imagesResponse.json();
    if (imagesResult.success) {
      console.log(`✓ 图片列表API正常，共 ${imagesResult.data.images?.length || 0} 张图片`);
      
      if (imagesResult.data.images.length > 0) {
        const firstImage = imagesResult.data.images[0];
        console.log(`✓ 图片URL格式: ${firstImage.url}`);
        
        // 测试图片静态文件访问
        const imageResponse = await fetch(`http://localhost:3001${firstImage.url}`);
        if (imageResponse.ok) {
          console.log('✓ 后端静态文件服务正常');
        } else {
          console.log('✗ 后端静态文件服务异常');
        }
      }
    } else {
      console.log('✗ 图片列表API异常');
    }
  } catch (error) {
    console.log('⚠ 图片API测试失败:', error.message);
  }
}

// 测试管理界面图片显示
async function testAdminImageDisplay() {
  try {
    const adminResponse = await fetch(ADMIN_URL);
    if (adminResponse.ok) {
      console.log('✓ 管理界面访问正常');
      
      // 测试管理界面的图片页面
      const imagesPageResponse = await fetch(`${ADMIN_URL}/images`);
      if (imagesPageResponse.ok) {
        console.log('✓ 管理界面图片页面访问正常');
      } else {
        console.log('⚠ 管理界面图片页面访问异常');
      }
    } else {
      console.log('✗ 管理界面访问异常');
    }
  } catch (error) {
    console.log('⚠ 管理界面测试失败:', error.message);
  }
}

// 测试前端博客图片显示
async function testFrontendImageDisplay() {
  try {
    const frontendResponse = await fetch(FRONTEND_URL);
    if (frontendResponse.ok) {
      console.log('✓ 前端博客访问正常');
      
      // 测试前端图片静态文件访问
      const testImageResponse = await fetch(`${FRONTEND_URL}/articles/img/30.jpg`);
      if (testImageResponse.ok) {
        console.log('✓ 前端图片静态文件访问正常');
      } else {
        console.log('⚠ 前端图片静态文件访问异常');
      }
    } else {
      console.log('✗ 前端博客访问异常');
    }
  } catch (error) {
    console.log('⚠ 前端博客测试失败:', error.message);
  }
}

// 测试图片上传功能
async function testImageUpload() {
  try {
    // 创建一个测试图片文件（1x1像素的PNG）
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00,
      0xFF, 0xFF, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xE2, 0x21, 0xBC, 0x33,
      0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    const formData = new FormData();
    formData.append('image', testImageBuffer, {
      filename: 'test-upload.png',
      contentType: 'image/png'
    });

    const uploadResponse = await fetch(`${BASE_URL}/images/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        ...formData.getHeaders()
      },
      body: formData
    });

    const uploadResult = await uploadResponse.json();
    if (uploadResult.success) {
      console.log('✓ 图片上传功能正常');
      
      // 测试上传的图片是否可以访问
      const uploadedImageResponse = await fetch(`http://localhost:3001${uploadResult.data.url}`);
      if (uploadedImageResponse.ok) {
        console.log('✓ 上传的图片可以正常访问');
        
        // 清理测试图片
        await cleanupTestImage(uploadResult.data.id);
      } else {
        console.log('⚠ 上传的图片无法访问');
      }
    } else {
      console.log('⚠ 图片上传功能异常:', uploadResult.message);
    }
  } catch (error) {
    console.log('⚠ 图片上传测试失败:', error.message);
  }
}

// 测试文章中的图片集成
async function testArticleImageIntegration() {
  try {
    // 获取一个现有图片
    const imagesResponse = await fetch(`${BASE_URL}/images?limit=1`, {
      headers: { 'Authorization': `Bearer ${authToken}` }
    });

    const imagesResult = await imagesResponse.json();
    if (imagesResult.success && imagesResult.data.images.length > 0) {
      const testImage = imagesResult.data.images[0];
      
      // 创建包含图片的测试文章
      const testArticle = {
        title: '图片集成测试文章',
        slug: 'image-integration-test-' + Date.now(),
        description: '这是一篇用于测试图片集成功能的文章',
        content: `# 图片集成测试

这是一篇测试文章，包含以下图片：

![测试图片](${testImage.url})

图片应该能够正常显示。`,
        status: 'published',
        featured: false,
        categories: [],
        tags: []
      };

      const articleResponse = await fetch(`${BASE_URL}/articles`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(testArticle)
      });

      const articleResult = await articleResponse.json();
      if (articleResult.success) {
        console.log('✓ 包含图片的文章创建成功');
        
        // 等待同步完成
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // 清理测试文章
        await cleanupTestArticle(articleResult.data.id);
        console.log('✓ 文章图片集成测试完成');
      } else {
        console.log('⚠ 文章创建失败');
      }
    } else {
      console.log('⚠ 没有可用的图片进行测试');
    }
  } catch (error) {
    console.log('⚠ 文章图片集成测试失败:', error.message);
  }
}

// 清理测试图片
async function cleanupTestImage(imageId) {
  try {
    await fetch(`${BASE_URL}/images/${imageId}`, {
      method: 'DELETE',
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    console.log('✓ 测试图片已清理');
  } catch (error) {
    console.log('⚠ 清理测试图片失败:', error.message);
  }
}

// 清理测试文章
async function cleanupTestArticle(articleId) {
  try {
    await fetch(`${BASE_URL}/articles/${articleId}`, {
      method: 'DELETE',
      headers: { 'Authorization': `Bearer ${authToken}` }
    });
    console.log('✓ 测试文章已清理');
  } catch (error) {
    console.log('⚠ 清理测试文章失败:', error.message);
  }
}

// 运行测试
testImageIntegration();
