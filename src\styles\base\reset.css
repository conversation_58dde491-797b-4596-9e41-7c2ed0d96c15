/* /src/styles/base/reset.css */
*, *::before, *::after { box-sizing: border-box; margin: 0; padding: 0; }
html { line-height: var(--line-height-base); font-size: var(--font-base-size); -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; text-rendering: optimizeLegibility; }
body { min-height: 100vh; background-color: var(--color-background); color: var(--color-text-primary); font-family: var(--font-family-sans); overflow-x: hidden; }
img, picture, video, canvas, svg { display: block; max-width: 100%; }
input, button, textarea, select { font: inherit; }
h1, h2, h3, h4, h5, h6 { line-height: var(--line-height-heading); margin-bottom: var(--space-m); }