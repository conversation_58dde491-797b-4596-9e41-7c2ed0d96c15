<!-- /src/components/ui/ImageWrapper.vue -->
<template>
  <!-- 标准 HTML 注释：图片包装器根元素 -->
  <div
    ref="wrapperRef"
    :class="[$style.wrapper, $style[cornerStyle], { [$style.isLoaded]: isLoaded }, className]"
    :style="{ aspectRatio: aspectRatioComputed }"
  >
    <!-- 标准 HTML 注释：占位符图片的过渡效果 -->
    <Transition name="placeholder-fade">
      <img
        v-if="placeholderSrcComputed && !isLoaded && !hasError"
        :src="placeholderSrcComputed"
        alt=""
        :class="$style.placeholder"
        aria-hidden="true"
        loading="lazy"
      />
      <!-- ！！！ <Transition> 内部已无注释 ！！！ -->
    </Transition>

    <!-- 标准 HTML 注释：实际图片的过渡效果 -->
    <Transition name="image-fade">
      <img
        v-if="shouldLoadImage"
        :src="src"
        :alt="alt"
        :class="$style.image"
        @load="onImageLoad"
        @error="onImageError"
        loading="lazy"
        :width="width"
        :height="height"
      />
       <!-- ！！！ <Transition> 内部已无注释 ！！！ -->
    </Transition>

    <!-- 标准 HTML 注释：错误提示信息 -->
    <div v-if="hasError" :class="$style.errorState">图片加载失败</div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { usePrefersReducedMotion } from '@/composables/usePrefersReducedMotion';

// 组件属性
const props = defineProps({
  // 图片源URL
  src: { type: String, required: true },
  // 图片替代文本
  alt: { type: String, required: true },
  // 低质量图片预览 (LQIP) 或 BlurHash
  lqip: { type: String, default: null },
  // 图片宽度
  width: { type: [String, Number], default: null },
  // 图片高度
  height: { type: [String, Number], default: null },
  // 图片圆角样式 ('soft' 或 'sharp')
  cornerStyle: {
    type: String,
    default: 'soft',
    validator: (val) => ['soft', 'sharp'].includes(val)
  },
  // 自定义宽高比
  aspectRatio: { type: [String, Number], default: null },
  // 允许外部传入 class
  className: { type: String, default: '' }
});

// 状态变量
const isLoaded = ref(false);
const hasError = ref(false);
const shouldLoadImage = ref(true);
const wrapperRef = ref(null);
const prefersReducedMotion = usePrefersReducedMotion();

// 计算属性
const placeholderSrcComputed = computed(() => {
  return props.lqip || null;
});

const aspectRatioComputed = computed(() => {
  if (props.aspectRatio) return props.aspectRatio;
  if (props.width && props.height) return `${props.width} / ${props.height}`;
  return 'auto'; // 默认自动
});

// 方法
const onImageLoad = () => {
  isLoaded.value = true;
};

const onImageError = () => {
  hasError.value = true;
  isLoaded.value = false;
};

// 生命周期钩子
onMounted(() => {
  // 可以在这里添加交叉观察器逻辑，实现懒加载
});
</script>

<style module>
.wrapper {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: auto;
  background-color: rgba(0, 0, 0, 0.1); /* 极淡背景色 */
  margin: var(--space-l) 0; /* 上下外边距 */
  /* 四周留有更充裕的"暗空间"，如同画框 */
  padding: var(--space-m);
  box-sizing: border-box;
  transition: transform 0.3s ease, filter 0.3s ease;
}

/* 圆角样式 */
.soft {
  border-radius: 6px; /* 柔和圆角 */
}

.sharp {
  border-radius: 0; /* 直角 */
}

/* 图片样式 */
.image {
  display: block;
  width: 100%;
  height: auto;
  object-fit: cover;
  opacity: 0; /* 初始不可见 */
  transition: opacity 0.5s ease; /* 淡入效果 */
}

/* 占位符样式 */
.placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: blur(10px); /* 模糊效果 */
  transform: scale(1.05); /* 稍微放大以覆盖模糊边缘 */
  opacity: 0.8;
}

/* 加载完成状态 */
.isLoaded .image {
  opacity: 1;
}

/* 错误状态 */
.errorState {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--color-text-secondary);
  font-size: 0.9rem;
}

/* 微妙的悬停效果 - 符合设计规范 */
.wrapper:hover {
  transform: scale(1.01); /* 极微小放大 */
  filter: brightness(1.02); /* 极细微亮度提升 */
}

/* 过渡动画 */
.placeholder-fade-leave-active {
  transition: opacity 0.5s ease;
}
.placeholder-fade-leave-to {
  opacity: 0;
}
.image-fade-enter-active {
  transition: opacity 0.5s ease;
}
.image-fade-enter-from {
  opacity: 0;
}

/* 响应式调整 */
@media (min-width: 768px) {
  .wrapper {
    margin: var(--space-xl) 0; /* 桌面端更大的间距 */
  }
}
</style>