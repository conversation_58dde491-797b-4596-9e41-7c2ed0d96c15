<!-- /src/components/layout/TheFooter.vue -->
<template>
  <footer :class="$style.footer">
    <!-- 标准 HTML 注释：版权信息，年份动态获取 -->
    <p>© {{ currentYear }} 墨影心流. All Rights Reserved.</p>
    <!-- 标准 HTML 注释：可以添加其他页脚链接 -->
    <!-- <nav :class="$style.footerNav">
      <LinkWrapper href="/privacy">Privacy Policy</LinkWrapper>
      <LinkWrapper href="/terms">Terms of Service</LinkWrapper>
    </nav> -->
  </footer>
</template>

<script setup>
import { computed } from 'vue';
// 如果用到 LinkWrapper
// import LinkWrapper from '@/components/common/LinkWrapper.vue';

// 计算当前年份
const currentYear = computed(() => new Date().getFullYear());
</script>

<style module>
.footer {
  /* 网格定位 */
  grid-column: 1 / -1; /* 移动端撑满 */
  grid-row: 3 / 4;     /* 占据第三行 */
  text-align: center;
  padding: var(--space-xl) var(--layout-padding-horizontal);
  margin-top: var(--space-xxl); /* 与主体内容的大间距 */
  border-top: var(--border-width) solid rgba(var(--color-accent-rgb), 0.1); /* 更微妙的顶部细线 */
  color: var(--color-text-secondary);
  font-size: 0.85rem; /* 更小的字号 */
  font-family: var(--font-family-body); /* 使用正文字体 */
  font-weight: var(--font-weight-light); /* 更细的字重 */
  letter-spacing: 0.02em; /* 微调字间距 */
  opacity: 0.8; /* 轻微透明度，实现"若隐若现" */
}

.footer p {
    margin-bottom: var(--space-s); /* 段落下间距 */
    max-width: none; /* 取消段落最大宽度限制 */
}

.footerNav {
  display: flex;
  justify-content: center;
  gap: var(--space-m);
}
.footerNav a { /* 如果使用 LinkWrapper，样式可能需要调整 */
   font-size: 0.85rem;
}

@media (min-width: 1024px) { /* 桌面断点 */
   .footer {
     /* 桌面端与主内容对齐 */
     grid-column: 2 / 3;
     text-align: left;
   }
   .footerNav {
     justify-content: flex-start;
   }
}
</style>