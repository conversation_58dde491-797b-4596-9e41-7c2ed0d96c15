/* /src/styles/global.css */
@import './variables.css';
@import './fonts.css'; /* 确保字体文件存在 */
@import './base/reset.css';
@import './base/typography.css';

/* 入场淡入+微上移动画 */
.fade-slide-up-enter-active {
  transition: opacity 0.4s ease-out, transform 0.4s ease-out;
}
.fade-slide-up-enter-from {
  opacity: 0;
  transform: translateY(10px);
}
.fade-slide-up-enter-to {
  opacity: 1;
  transform: translateY(0);
}

body {
  background-color: var(--color-background);
  color: var(--color-text-primary);
  font-family: var(--font-family-serif);
  position: relative;
}

/* 背景纹理实现 (根据方案 - CSS 伪元素) */
.app-container::before {
  content: ""; /* 伪元素必需 */
  position: fixed; /* 固定在视口，不随滚动条滚动 */
  top: 0;
  left: 0;
  width: 100vw; /* 视口宽度 */
  height: 100vh; /* 视口高度 */

  /* --- 关键：纹理图片 --- */
  /* 纹理图片存放在 /src/assets/textures/backgrounds/ 目录下 */
  background-image: url('../assets/textures/backgrounds/paper-background-1.png'); /* 使用纸张纹理背景 */
  background-repeat: repeat; /* 平铺纹理 */
  opacity: 0.08; /* 极低透明度，符合设计规范 0.05-0.1 */
  mix-blend-mode: overlay; /* 使用overlay混合模式，更自然地融入背景 */
  z-index: -1; /* 将伪元素置于内容层之下 */
  pointer-events: none; /* 确保伪元素不干扰鼠标事件 */
}

/* 核心布局网格 (应用于 App.vue 的根元素) */
.layoutGrid {
  display: grid;
  /* 移动端默认：单列 */
  grid-template-columns: 1fr;
  gap: 0;
  padding: 0 var(--layout-padding-horizontal);
  min-height: 100vh;
  /* 定义网格行，方便页头页脚定位 */
  grid-template-rows: auto 1fr auto; /* Header, Main Content, Footer */
}

/* 桌面端非对称网格布局 */
@media (min-width: 1024px) { /* 使用合适的断点 */
  .layoutGrid {
    grid-template-columns: 1fr minmax(auto, var(--content-max-width)) calc(1fr * var(--desktop-aside-width-ratio));
    gap: var(--space-l);
  }
}

/* 全局页面转场动画类 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-duration) var(--transition-timing-function);
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 滑动转场示例 (根据路由 meta 定义) */
.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  transition: transform var(--transition-duration) var(--transition-timing-function-in-out),
              opacity var(--transition-duration) var(--transition-timing-function);
  /* 使用绝对定位确保平滑过渡 */
  position: absolute;
  width: 100%;
  top: 0; left: 0;
}
.slide-left-enter-from { opacity: 0; transform: translateX(50px); }
.slide-left-leave-to { opacity: 0; transform: translateX(-50px); }
.slide-right-enter-from { opacity: 0; transform: translateX(-50px); }
.slide-right-leave-to { opacity: 0; transform: translateX(50px); }

/* 辅助类：视觉隐藏但屏幕阅读器可读 */
.visually-hidden {
  position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px;
  overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap; border: 0;
}.progressive-image {
  position: relative;
  overflow: hidden;
  border-radius: 6px; /* 柔和圆角，4-8px */
  background-color: #111; /* 暗色背景，营造画框感 */
  margin: 1.5em 0;
}

.progressive-image img {
  width: 100%;
  display: block;
  transition: opacity 0.6s ease, transform 0.3s ease, filter 0.3s ease;
  filter: blur(20px);
  transform: scale(1);
}

/* 高清图加载完成后，去除模糊 */
.progressive-image img.loaded {
  filter: blur(0);
  opacity: 1;
}

/* 细腻悬停反馈 */
.progressive-image:hover img {
  transform: scale(1.01);
  filter: brightness(1.02);
}

