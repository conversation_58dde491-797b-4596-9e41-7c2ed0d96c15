// 简单的API测试脚本
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:3001/api';

// 测试登录
async function testLogin() {
  try {
    console.log('测试登录...');
    
    const response = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123456'
      })
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ 登录成功');
      console.log('Token:', data.data.token);
      return data.data.token;
    } else {
      console.log('❌ 登录失败:', data.message);
      return null;
    }
  } catch (error) {
    console.error('❌ 登录请求失败:', error.message);
    return null;
  }
}

// 测试获取文章列表
async function testGetArticles(token) {
  try {
    console.log('\n测试获取文章列表...');
    
    const response = await fetch(`${BASE_URL}/articles`, {
      headers: token ? {
        'Authorization': `Bearer ${token}`
      } : {}
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ 获取文章列表成功');
      console.log(`共 ${data.data.articles.length} 篇文章`);
      data.data.articles.forEach(article => {
        console.log(`- ${article.title} (${article.slug})`);
      });
    } else {
      console.log('❌ 获取文章列表失败:', data.message);
    }
  } catch (error) {
    console.error('❌ 获取文章列表请求失败:', error.message);
  }
}

// 测试获取分类和标签
async function testGetCategoriesAndTags() {
  try {
    console.log('\n测试获取分类...');
    
    const categoriesResponse = await fetch(`${BASE_URL}/categories`);
    const categoriesData = await categoriesResponse.json();
    
    if (categoriesResponse.ok) {
      console.log('✅ 获取分类成功');
      console.log(`共 ${categoriesData.data.length} 个分类`);
      categoriesData.data.forEach(category => {
        console.log(`- ${category.name} (${category.slug})`);
      });
    }

    console.log('\n测试获取标签...');
    
    const tagsResponse = await fetch(`${BASE_URL}/tags`);
    const tagsData = await tagsResponse.json();
    
    if (tagsResponse.ok) {
      console.log('✅ 获取标签成功');
      console.log(`共 ${tagsData.data.length} 个标签`);
      tagsData.data.forEach(tag => {
        console.log(`- ${tag.name} (${tag.slug})`);
      });
    }
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
}

// 运行测试
async function runTests() {
  console.log('🚀 开始API测试...\n');
  
  const token = await testLogin();
  await testGetArticles(token);
  await testGetCategoriesAndTags();
  
  console.log('\n✨ 测试完成');
}

runTests();
