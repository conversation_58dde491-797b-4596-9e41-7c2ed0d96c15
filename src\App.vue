<!-- /src/App.vue -->
<template>
  <div class="app-container layoutGrid">
    <TheHeader />

    <!-- RouterView 用于渲染当前路由匹配的组件 -->
    <RouterView v-slot="{ Component, route }">
      <!-- 标准 HTML 注释：包裹路由视图的过渡效果 -->
      <!-- mode="out-in" 表示先旧组件过渡离开，新组件再过渡进入 -->
      <Transition :name="route.meta.transition || 'fade'" mode="out-in">
        <!-- 标准 HTML 注释：动态渲染路由组件，key 对于相同组件不同参数的路由切换过渡很重要 -->
        <component :is="Component" :key="route.path" />
        <!-- ！！！ 确保 Transition 内部只有这一个 component ！！！ -->
      </Transition>
    </RouterView>

    <!-- 侧边目录导航栏，仅在文章页面显示 -->
    <aside :class="$style.asideArea" v-if="isArticlePage">
      <TableOfContents :showFilters="false" title="文章目录" />
    </aside>

    <TheFooter />
  </div>
</template>

<script setup>
// 导入布局组件
import TheHeader from '@/components/layout/TheHeader.vue';
import TheFooter from '@/components/layout/TheFooter.vue';
import TableOfContents from '@/components/features/TableOfContents.vue';
import { onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';

// onMounted 钩子可以执行组件挂载后的逻辑
// 获取当前路由
const route = useRoute();

// 计算属性：判断是否为文章页面
const isArticlePage = computed(() => {
  return route.name === 'article';
});

onMounted(() => {
  // 示例：应用加载动画的类 (需要在 CSS 中定义 .app-loaded)
  // document.body.classList.add('app-loaded');
  console.log('App component mounted.');
});
</script>

<style module>
/* App.vue 的局部样式 */
/* 侧边栏的网格定位 */
.asideArea {
  display: none;
  grid-row: 2 / 3;
}
@media (min-width: 1024px) {
  .asideArea {
    display: block;
    grid-column: 3 / 4;
    padding-top: var(--space-xl);
  }
}
</style>

<style>
/* 全局非作用域样式 (如果 global.css 不方便放) */
/* 例如：定义 .app-loaded 动画 */
/*
@keyframes fadeInApp { from { opacity: 0; } to { opacity: 1; } }
.app-loaded { animation: fadeInApp 0.5s ease-out forwards; }
*/
</style>