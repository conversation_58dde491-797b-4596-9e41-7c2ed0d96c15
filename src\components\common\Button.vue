<!-- /src/components/common/Button.vue -->
<template>
  <!-- 标准 HTML 注释：基础按钮组件，支持不同变体 -->
  <button
    :class="[$style.button, $style[variant], className]"
    @click="$emit('click', $event)"
    :type="type"
  >
    <!-- 标准 HTML 注释：按钮内容通过 slot 传入 -->
    <slot></slot>
  </button>
</template>

<script setup>
defineProps({
  // 按钮变体 ('bordered' 或 'subtleBg')
  variant: {
    type: String,
    default: 'bordered',
    validator: (val) => ['bordered', 'subtleBg'].includes(val)
  },
  // 允许外部传入 class
  className: { type: String, default: '' },
  // HTML button 类型
  type: { type: String, default: 'button' }
});
// 声明组件会触发 'click' 事件
defineEmits(['click']);
</script>

<style module>
.button {
  /* 基础样式 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-s) var(--space-m);
  border: var(--border-width) solid transparent; /* 默认透明边框 */
  border-radius: var(--border-radius-soft);
  background-color: transparent;
  color: var(--color-text-primary);
  font-family: var(--font-family-sans);
  font-weight: 500;
  cursor: pointer;
  text-align: center;
  transition: background-color var(--transition-duration) ease,
              border-color var(--transition-duration) ease,
              color var(--transition-duration) ease,
              box-shadow var(--transition-duration) ease;
  /* 最小触摸区域 (A11y) */
  min-height: 44px;
  min-width: 44px;
  user-select: none; /* 防止文本选中 */
}

/* 变体：带边框 - 符合极简主义设计 */
.bordered {
  border-color: var(--color-border); /* 默认极纤细边框 */
  border-width: 1px; /* 确保边框极纤细 */
}
.bordered:hover {
  border-color: var(--color-accent);
  color: var(--color-accent);
  box-shadow: 0 0 8px rgba(var(--color-accent-rgb), 0.15); /* 微妙光晕效果 */
  transition: all 0.3s ease; /* 平滑过渡 */
}
.bordered:focus-visible { /* 键盘焦点 */
   outline: none; /* 使用边框代替轮廓 */
   border-color: var(--color-highlight);
   color: var(--color-highlight);
   box-shadow: 0 0 0 2px rgba(var(--color-highlight-rgb), 0.2); /* 更微妙的外发光 */
}

/* 变体：细微背景 */
.subtleBg {
  background-color: rgba(var(--color-accent-rgb), 0.05); /* 非常淡的背景 (Affordance) */
}
.subtleBg:hover {
  background-color: rgba(var(--color-accent-rgb), 0.15);
  color: var(--color-accent-hover);
}
.subtleBg:focus-visible { /* 键盘焦点 */
   outline: none;
   color: var(--color-accent-hover);
   box-shadow: 0 0 0 2px var(--color-background), /* 内描边效果 */
               0 0 0 4px var(--color-highlight);  /* 外描边效果 */
}

/* 可选：按钮按下状态 */
.button:active {
  transform: translateY(1px); /* 轻微下沉 */
  filter: brightness(0.95);
}
</style>