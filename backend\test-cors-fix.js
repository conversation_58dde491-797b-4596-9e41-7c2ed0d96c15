import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';

const BASE_URL = 'http://localhost:3001/api';

// 测试用的管理员凭据
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123456'
};

let authToken = '';

// 登录获取token
async function login() {
  try {
    const response = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(ADMIN_CREDENTIALS)
    });

    const result = await response.json();
    if (result.success) {
      authToken = result.data.token;
      console.log('✓ 登录成功');
      return true;
    } else {
      console.log('✗ 登录失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('✗ 登录请求失败:', error.message);
    return false;
  }
}

// 测试修复后的CORS配置
async function testCORSFix() {
  try {
    console.log('\n=== 测试修复后的CORS配置 ===');
    
    // 模拟浏览器的预检请求，使用正确的Origin
    const optionsResponse = await fetch(`${BASE_URL}/images/upload`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:5175',  // 使用正确的前端端口
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'authorization,content-type'
      }
    });

    console.log('OPTIONS请求状态码:', optionsResponse.status);
    console.log('CORS响应头:');
    const corsHeaders = {};
    optionsResponse.headers.forEach((value, key) => {
      if (key.toLowerCase().includes('access-control')) {
        corsHeaders[key] = value;
      }
    });
    console.log(corsHeaders);

    // 检查是否允许正确的Origin
    const allowedOrigin = corsHeaders['access-control-allow-origin'];
    if (allowedOrigin === 'http://localhost:5175') {
      console.log('✓ CORS配置已修复，允许正确的Origin');
      return true;
    } else {
      console.log('✗ CORS配置仍有问题，当前允许的Origin:', allowedOrigin);
      return false;
    }
  } catch (error) {
    console.log('✗ CORS测试失败:', error.message);
    return false;
  }
}

// 测试图片上传（模拟前端请求）
async function testImageUploadWithCorrectOrigin() {
  try {
    console.log('\n=== 测试图片上传（使用正确的Origin） ===');
    
    // 创建测试图片文件
    const testImagePath = path.join(process.cwd(), 'test-cors-fix.jpg');
    const existingImagePath = path.join(process.cwd(), '..', 'public', 'articles', 'img', '1.jpg');
    
    if (fs.existsSync(existingImagePath)) {
      fs.copyFileSync(existingImagePath, testImagePath);
      console.log('✓ 测试图片文件准备完成');
    } else {
      console.log('✗ 没有找到测试图片文件');
      return false;
    }

    // 使用FormData模拟前端上传，包含正确的Origin头
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));

    console.log('发送上传请求到:', `${BASE_URL}/images/upload`);
    console.log('使用Origin: http://localhost:5175');

    const response = await fetch(`${BASE_URL}/images/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Origin': 'http://localhost:5175',  // 模拟前端的Origin
        ...formData.getHeaders()
      },
      body: formData
    });

    console.log('响应状态码:', response.status);
    
    // 检查CORS响应头
    const corsResponseHeaders = {};
    response.headers.forEach((value, key) => {
      if (key.toLowerCase().includes('access-control')) {
        corsResponseHeaders[key] = value;
      }
    });
    console.log('响应CORS头:', corsResponseHeaders);

    const result = await response.json();
    
    // 清理测试文件
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
    }

    if (result.success) {
      console.log('✓ 图片上传成功（CORS问题已解决）');
      console.log(`  文件名: ${result.data.filename}`);
      console.log(`  URL: ${result.data.url}`);
      
      // 清理上传的测试图片
      try {
        await fetch(`${BASE_URL}/images/${result.data.id}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${authToken}` }
        });
        console.log('✓ 测试图片已清理');
      } catch (error) {
        console.log('⚠ 清理测试图片失败');
      }
      
      return true;
    } else {
      console.log('✗ 图片上传失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('✗ 图片上传异常:', error.message);
    console.log('错误详情:', error);
    return false;
  }
}

// 主测试函数
async function runCORSFixTest() {
  console.log('🔧 开始测试CORS修复...\n');
  
  try {
    // 1. 登录
    const loginOk = await login();
    if (!loginOk) {
      console.log('❌ 测试终止：无法登录');
      return;
    }
    
    // 2. 测试CORS配置
    const corsOk = await testCORSFix();
    
    // 3. 测试图片上传
    const uploadOk = await testImageUploadWithCorrectOrigin();
    
    // 输出测试结果
    console.log('\n🎯 CORS修复测试结果:');
    console.log(`CORS配置: ${corsOk ? '✅ 已修复' : '❌ 仍有问题'}`);
    console.log(`图片上传: ${uploadOk ? '✅ 正常' : '❌ 失败'}`);
    
    const allOk = corsOk && uploadOk;
    console.log(`\n🏆 整体结果: ${allOk ? '✅ CORS问题已解决' : '❌ 仍需修复'}`);
    
    if (allOk) {
      console.log('\n💡 修复说明:');
      console.log('- 已将CORS_ORIGIN从 http://localhost:5173 修改为 http://localhost:5175');
      console.log('- 前端现在可以正常访问后端API进行图片上传');
      console.log('- net::ERR_FAILED 错误应该已经解决');
    }
    
  } catch (error) {
    console.log('❌ 测试过程中发生错误:', error.message);
  }
}

runCORSFixTest().catch(console.error);
