<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紧急修复 - 图片上传问题</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .alert-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .alert-warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .alert-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        .btn {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            min-width: 150px;
        }
        .btn:hover { background-color: #218838; }
        .btn:disabled { background-color: #6c757d; cursor: not-allowed; }
        
        .btn-secondary { background-color: #007bff; }
        .btn-secondary:hover { background-color: #0056b3; }
        
        .btn-danger { background-color: #dc3545; }
        .btn-danger:hover { background-color: #c82333; }
        
        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        
        .log {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 15px 0;
            border: 1px solid #ddd;
        }
        
        .status-box {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
            background-color: #f8f9fa;
        }
        
        .upload-test {
            border: 2px dashed #ddd;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .upload-test.dragover {
            border-color: #007bff;
            background-color: #e3f2fd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 紧急修复 - 图片上传问题</h1>
        
        <div id="status" class="alert alert-info">
            正在诊断问题...
        </div>
        
        <div class="step">
            <h3>🔍 问题诊断</h3>
            <div id="diagnosis" class="status-box">
                <div>正在检查...</div>
            </div>
        </div>
        
        <div class="step">
            <h3>🔧 一键修复</h3>
            <button id="emergencyFix" class="btn" onclick="emergencyFix()">
                🚀 立即修复所有问题
            </button>
            <button class="btn btn-secondary" onclick="manualLogin()">
                🔑 手动重新登录
            </button>
            <button class="btn btn-danger" onclick="clearAll()">
                🗑️ 清除所有数据
            </button>
        </div>
        
        <div class="step">
            <h3>🧪 测试上传</h3>
            <div class="upload-test" id="uploadTest">
                <p>拖拽图片到这里或点击选择文件</p>
                <input type="file" id="testFile" accept="image/*" style="display: none;">
                <button class="btn btn-secondary" onclick="document.getElementById('testFile').click()">
                    选择图片文件
                </button>
                <button class="btn" onclick="testUpload()">
                    测试上传
                </button>
            </div>
            <div id="uploadResult" class="status-box" style="display: none;"></div>
        </div>
        
        <div class="step">
            <h3>📝 操作日志</h3>
            <div id="log" class="log"></div>
            <button class="btn btn-secondary" onclick="clearLog()">清除日志</button>
        </div>
        
        <div class="step">
            <h3>🎯 修复完成后</h3>
            <p>1. <a href="/images" target="_blank">返回图片管理页面</a></p>
            <p>2. <a href="/login" target="_blank">重新登录</a></p>
            <p>3. 如果问题仍然存在，请刷新页面后重试</p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        let selectedFile = null;
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
            logDiv.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `alert alert-${type}`;
        }
        
        function updateDiagnosis(html) {
            document.getElementById('diagnosis').innerHTML = html;
        }
        
        async function diagnoseProblems() {
            log('开始诊断问题...');
            
            let diagnosis = '<strong>诊断结果：</strong><br>';
            let hasProblems = false;
            
            // 检查localStorage
            const token = localStorage.getItem('token');
            const adminToken = localStorage.getItem('admin_token');
            
            diagnosis += `• token: ${token ? '存在' : '❌ 不存在'}<br>`;
            diagnosis += `• admin_token: ${adminToken ? '存在' : '❌ 不存在'}<br>`;
            
            if (!adminToken) {
                hasProblems = true;
                diagnosis += `<span style="color: red;">⚠️ 缺少admin_token，这是主要问题！</span><br>`;
            } else {
                try {
                    const payload = JSON.parse(atob(adminToken.split('.')[1]));
                    const exp = new Date(payload.exp * 1000);
                    const now = new Date();
                    
                    if (exp < now) {
                        hasProblems = true;
                        diagnosis += `<span style="color: red;">⚠️ admin_token已过期：${exp.toLocaleString()}</span><br>`;
                    } else {
                        diagnosis += `<span style="color: green;">✅ admin_token有效，过期时间：${exp.toLocaleString()}</span><br>`;
                    }
                } catch (error) {
                    hasProblems = true;
                    diagnosis += `<span style="color: red;">⚠️ admin_token格式无效</span><br>`;
                }
            }
            
            // 测试API连接
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    diagnosis += `<span style="color: green;">✅ 后端API连接正常</span><br>`;
                } else {
                    hasProblems = true;
                    diagnosis += `<span style="color: red;">❌ 后端API连接失败</span><br>`;
                }
            } catch (error) {
                hasProblems = true;
                diagnosis += `<span style="color: red;">❌ 无法连接后端API</span><br>`;
            }
            
            updateDiagnosis(diagnosis);
            
            if (hasProblems) {
                updateStatus('发现问题，需要修复', 'warning');
                log('诊断完成，发现问题需要修复', 'warning');
            } else {
                updateStatus('系统状态正常', 'success');
                log('诊断完成，系统状态正常', 'success');
            }
        }
        
        async function emergencyFix() {
            const button = document.getElementById('emergencyFix');
            button.disabled = true;
            button.textContent = '🔄 修复中...';
            
            try {
                updateStatus('正在执行紧急修复...', 'info');
                log('开始紧急修复流程');
                
                // 步骤1：清除旧的token
                localStorage.removeItem('token');
                log('✅ 已清除旧的token');
                
                // 步骤2：登录获取新的admin_token
                log('正在登录获取新的admin_token...');
                const loginResponse = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123456'
                    })
                });

                const loginResult = await loginResponse.json();
                
                if (loginResult.success) {
                    localStorage.setItem('admin_token', loginResult.data.token);
                    log('✅ 登录成功，admin_token已设置');
                    
                    // 步骤3：测试token有效性
                    const testResponse = await fetch(`${API_BASE}/auth/me`, {
                        headers: {
                            'Authorization': `Bearer ${loginResult.data.token}`
                        }
                    });
                    
                    if (testResponse.ok) {
                        log('✅ Token验证成功');
                        
                        // 步骤4：测试图片上传
                        const uploadTest = await testImageUpload(loginResult.data.token);
                        
                        if (uploadTest) {
                            updateStatus('🎉 修复成功！图片上传功能已恢复', 'success');
                            log('🎉 紧急修复完成，所有功能正常');
                        } else {
                            updateStatus('Token已修复，但上传测试失败', 'warning');
                            log('⚠️ Token已修复，但上传功能仍有问题');
                        }
                    } else {
                        updateStatus('Token设置成功，但验证失败', 'warning');
                        log('⚠️ Token设置成功，但验证失败');
                    }
                } else {
                    updateStatus(`登录失败：${loginResult.message}`, 'error');
                    log(`❌ 登录失败：${loginResult.message}`, 'error');
                }
                
            } catch (error) {
                updateStatus(`修复失败：${error.message}`, 'error');
                log(`❌ 修复过程中发生错误：${error.message}`, 'error');
            } finally {
                button.disabled = false;
                button.textContent = '🚀 立即修复所有问题';
                
                // 重新诊断
                setTimeout(diagnoseProblems, 1000);
            }
        }
        
        async function testImageUpload(token) {
            try {
                log('测试图片上传功能...');
                
                // 创建测试图片
                const canvas = document.createElement('canvas');
                canvas.width = 1;
                canvas.height = 1;
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = '#00ff00';
                ctx.fillRect(0, 0, 1, 1);
                
                return new Promise((resolve) => {
                    canvas.toBlob(async (blob) => {
                        const formData = new FormData();
                        formData.append('image', blob, 'test-emergency.png');

                        const response = await fetch(`${API_BASE}/images/upload`, {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${token}`
                            },
                            body: formData
                        });

                        const result = await response.json();
                        
                        if (result.success) {
                            log(`✅ 图片上传测试成功：${result.data.filename}`);
                            
                            // 清理测试图片
                            try {
                                await fetch(`${API_BASE}/images/${result.data.id}`, {
                                    method: 'DELETE',
                                    headers: { 'Authorization': `Bearer ${token}` }
                                });
                                log('✅ 测试图片已清理');
                            } catch (error) {
                                log('⚠️ 清理测试图片失败');
                            }
                            
                            resolve(true);
                        } else {
                            log(`❌ 图片上传测试失败：${result.message}`);
                            resolve(false);
                        }
                    }, 'image/png');
                });
                
            } catch (error) {
                log(`❌ 图片上传测试异常：${error.message}`);
                return false;
            }
        }
        
        async function manualLogin() {
            const username = prompt('请输入用户名:', 'admin');
            const password = prompt('请输入密码:', 'admin123456');
            
            if (username && password) {
                try {
                    log(`尝试登录用户: ${username}`);
                    
                    const response = await fetch(`${API_BASE}/auth/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ username, password })
                    });

                    const result = await response.json();
                    
                    if (result.success) {
                        localStorage.setItem('admin_token', result.data.token);
                        updateStatus('手动登录成功', 'success');
                        log('✅ 手动登录成功，admin_token已设置');
                        diagnoseProblems();
                    } else {
                        updateStatus(`登录失败：${result.message}`, 'error');
                        log(`❌ 登录失败：${result.message}`, 'error');
                    }
                } catch (error) {
                    updateStatus(`登录请求失败：${error.message}`, 'error');
                    log(`❌ 登录请求失败：${error.message}`, 'error');
                }
            }
        }
        
        function clearAll() {
            if (confirm('确定要清除所有localStorage数据吗？')) {
                localStorage.clear();
                updateStatus('所有数据已清除', 'warning');
                log('⚠️ 所有localStorage数据已清除');
                diagnoseProblems();
            }
        }
        
        async function testUpload() {
            const adminToken = localStorage.getItem('admin_token');
            const resultDiv = document.getElementById('uploadResult');
            
            if (!selectedFile) {
                resultDiv.innerHTML = '<span style="color: red;">请先选择一个图片文件</span>';
                resultDiv.style.display = 'block';
                return;
            }
            
            if (!adminToken) {
                resultDiv.innerHTML = '<span style="color: red;">没有admin_token，请先点击修复</span>';
                resultDiv.style.display = 'block';
                return;
            }

            try {
                resultDiv.innerHTML = '<span style="color: blue;">正在上传...</span>';
                resultDiv.style.display = 'block';
                
                const formData = new FormData();
                formData.append('image', selectedFile);

                log(`上传文件: ${selectedFile.name} (${selectedFile.size} bytes)`);

                const response = await fetch(`${API_BASE}/images/upload`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    },
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `<span style="color: green;">✅ 上传成功: ${result.data.filename}</span>`;
                    log(`✅ 文件上传成功: ${result.data.filename}`, 'success');
                    updateStatus('图片上传功能正常！', 'success');
                } else {
                    resultDiv.innerHTML = `<span style="color: red;">❌ 上传失败: ${result.message}</span>`;
                    log(`❌ 文件上传失败: ${result.message}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">❌ 上传异常: ${error.message}</span>`;
                log(`❌ 文件上传异常: ${error.message}`, 'error');
            }
        }
        
        function clearLog() {
            document.getElementById('log').textContent = '';
        }
        
        // 文件选择处理
        document.getElementById('testFile').addEventListener('change', function(e) {
            if (e.target.files[0]) {
                selectedFile = e.target.files[0];
                document.querySelector('.upload-test p').textContent = `已选择: ${selectedFile.name}`;
                log(`文件已选择: ${selectedFile.name} (${selectedFile.size} bytes)`);
            }
        });
        
        // 拖拽上传
        const uploadTest = document.getElementById('uploadTest');
        uploadTest.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadTest.classList.add('dragover');
        });
        
        uploadTest.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadTest.classList.remove('dragover');
        });
        
        uploadTest.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadTest.classList.remove('dragover');
            
            if (e.dataTransfer.files[0]) {
                selectedFile = e.dataTransfer.files[0];
                document.querySelector('.upload-test p').textContent = `已选择: ${selectedFile.name}`;
                log(`文件已拖拽选择: ${selectedFile.name} (${selectedFile.size} bytes)`);
            }
        });
        
        // 页面加载时自动诊断
        window.onload = function() {
            log('紧急修复工具已加载');
            diagnoseProblems();
        };
    </script>
</body>
</html>
