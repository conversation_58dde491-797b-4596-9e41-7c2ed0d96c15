import fs from 'fs/promises';
import path from 'path';
import sharp from 'sharp';
import db from './src/config/database.js';

async function importExistingImages() {
  console.log('开始导入现有图片...');
  
  try {
    const imagesDir = path.resolve('../public/articles/img');
    const files = await fs.readdir(imagesDir);
    const imageFiles = files.filter(file => 
      /\.(jpg|jpeg|png|gif|webp)$/i.test(file)
    );

    console.log(`发现 ${imageFiles.length} 个图片文件`);

    for (const filename of imageFiles) {
      const filePath = path.join(imagesDir, filename);
      const stats = await fs.stat(filePath);
      
      // 检查是否已存在
      const existing = db.prepare('SELECT id FROM images WHERE filename = ?').get(filename);
      if (existing) {
        console.log(`跳过已存在的图片: ${filename}`);
        continue;
      }

      try {
        // 获取图片元数据
        const metadata = await sharp(filePath).metadata();
        
        // 插入到数据库
        const insertImage = db.prepare(`
          INSERT INTO images (filename, original_name, path, size, mime_type, width, height)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `);

        insertImage.run(
          filename,
          filename,
          filePath,
          stats.size,
          `image/${metadata.format}`,
          metadata.width || 0,
          metadata.height || 0
        );

        console.log(`✓ 导入图片: ${filename} (${metadata.width}x${metadata.height})`);
      } catch (error) {
        console.error(`✗ 导入图片失败 ${filename}:`, error.message);
      }
    }

    console.log('图片导入完成！');
  } catch (error) {
    console.error('导入图片时出错:', error);
  }
}

importExistingImages();
