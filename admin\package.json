{"name": "moyin-blog-admin", "version": "1.0.0", "description": "Admin interface for Moyin Blog", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "keywords": ["blog", "admin", "vue", "element-plus"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"@element-plus/icons-vue": "^2.3.2", "@vitejs/plugin-vue": "^6.0.1", "@vueup/vue-quill": "^1.2.0", "axios": "^1.11.0", "element-plus": "^2.11.2", "pinia": "^3.0.3", "quill": "^2.0.3", "vite": "^7.1.5", "vue": "^3.5.21", "vue-router": "^4.5.1"}}