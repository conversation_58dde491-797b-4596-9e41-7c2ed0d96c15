import bcrypt from 'bcryptjs';
import db from '../config/database.js';

// 创建默认管理员账户
export const createDefaultAdmin = async () => {
  try {
    // 检查是否已存在管理员账户
    const existingAdmin = db.prepare('SELECT id FROM users WHERE role = ?').get('admin');
    
    if (existingAdmin) {
      console.log('管理员账户已存在，跳过创建');
      return;
    }

    const username = process.env.DEFAULT_ADMIN_USERNAME || 'admin';
    const email = process.env.DEFAULT_ADMIN_EMAIL || '<EMAIL>';
    const password = process.env.DEFAULT_ADMIN_PASSWORD || 'admin123456';

    // 加密密码
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // 插入管理员账户
    const insertAdmin = db.prepare(`
      INSERT INTO users (username, email, password_hash, role)
      VALUES (?, ?, ?, ?)
    `);

    insertAdmin.run(username, email, passwordHash, 'admin');

    console.log('✅ 默认管理员账户创建成功');
    console.log(`   用户名: ${username}`);
    console.log(`   邮箱: ${email}`);
    console.log(`   密码: ${password}`);
    console.log('⚠️  请在生产环境中及时修改默认密码！');
  } catch (error) {
    console.error('❌ 创建默认管理员账户失败:', error.message);
  }
};

// 如果直接运行此文件，则执行初始化
if (import.meta.url === `file://${process.argv[1]}`) {
  await createDefaultAdmin();
  process.exit(0);
}
