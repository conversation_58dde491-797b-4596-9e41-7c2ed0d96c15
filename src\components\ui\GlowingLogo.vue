<!-- /src/components/ui/GlowingLogo.vue -->
<template>
  <RouterLink
    :to="targetRoute"
    :class="$style.logoLink"
    :title="isHomePage ? '前往关于页面' : '返回首页'"
  >
    <div :class="$style.logoContainer" ref="logoContainerRef">
      <img
        src="@/assets/images/logo.svg"
        alt="黑猫船长 Z Logo"
        :class="$style.logoImage"
        ref="logoImageRef"
        :style="{
          height: props.size + 'px',
          width: 'auto',
          transform: `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(${scale})`,
          transition: `transform ${transitionDuration}s ease, filter 0.5s ease, box-shadow 0.5s ease`
        }"
      />
    </div>
  </RouterLink>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';

const props = defineProps({
  size: {
    type: Number,
    default: 150
  },
  glowColor: {
    type: String,
    default: 'var(--color-accent)'
  },
  glowIntensity: {
    type: Number,
    default: 0.4
  },
  hoverScale: {
    type: Number,
    default: 1.1
  }
});

// 路由相关
const route = useRoute();

// 计算当前是否在首页
const isHomePage = computed(() => {
  return route.path === '/' || route.name === 'home';
});

// 计算目标路由
const targetRoute = computed(() => {
  return isHomePage.value ? '/about' : '/';
});

// DOM引用
const logoContainerRef = ref(null);
const logoImageRef = ref(null);

// 3D效果状态
const rotateX = ref(0);
const rotateY = ref(0);
const scale = ref(1);
const transitionDuration = ref(0.3);

// 鼠标移动处理函数
const handleMouseMove = (event) => {
  if (!logoContainerRef.value || !logoImageRef.value) return;

  const rect = logoContainerRef.value.getBoundingClientRect();
  const centerX = rect.width / 2;
  const centerY = rect.height / 2;
  const mouseX = event.clientX - rect.left;
  const mouseY = event.clientY - rect.top;

  // 计算旋转角度
  rotateY.value = ((mouseX - centerX) / centerX) * 15;
  rotateX.value = ((centerY - mouseY) / centerY) * 15;

  // 更快的响应
  transitionDuration.value = 0.1;

  // 计算阴影方向，使其与鼠标位置相关
  if (logoImageRef.value) {
    // 计算阴影偏移方向，使光源看起来像是从鼠标方向来的
    const shadowX = ((centerX - mouseX) / centerX) * 5;
    const shadowY = ((centerY - mouseY) / centerY) * 5;

    // 应用阴影效果
    logoImageRef.value.style.filter = `drop-shadow(${shadowX}px ${shadowY}px 5px rgba(var(--color-accent-rgb), ${props.glowIntensity}))`;
  }
};

// 鼠标进入处理函数
const handleMouseEnter = () => {
  scale.value = props.hoverScale;

  // 默认阴影效果，当鼠标刚进入时使用
  if (logoImageRef.value) {
    logoImageRef.value.style.filter = `drop-shadow(0px 0px 8px rgba(var(--color-accent-rgb), ${props.glowIntensity}))`;
  }
};

// 鼠标离开处理函数
const handleMouseLeave = () => {
  rotateX.value = 0;
  rotateY.value = 0;
  scale.value = 1;
  transitionDuration.value = 0.3;

  // 移除阴影效果
  if (logoImageRef.value) {
    logoImageRef.value.style.filter = 'none';
  }
};

// 组件挂载时添加事件监听
onMounted(() => {
  if (logoContainerRef.value) {
    logoContainerRef.value.addEventListener('mousemove', handleMouseMove);
    logoContainerRef.value.addEventListener('mouseenter', handleMouseEnter);
    logoContainerRef.value.addEventListener('mouseleave', handleMouseLeave);
  }
});

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  if (logoContainerRef.value) {
    logoContainerRef.value.removeEventListener('mousemove', handleMouseMove);
    logoContainerRef.value.removeEventListener('mouseenter', handleMouseEnter);
    logoContainerRef.value.removeEventListener('mouseleave', handleMouseLeave);
  }
});
</script>

<style module>
.logoLink {
  display: inline-block;
  text-decoration: none;
  outline: none;
  border-radius: 50%;
  position: relative;
}

.logoLink:focus-visible {
  outline: 2px solid var(--color-accent);
  outline-offset: 4px;
}

.logoContainer {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 40%;
  overflow: visible;
  z-index: 1;
}

.logoImage {
  position: relative;
  z-index: 2;
  will-change: transform;
  transition: transform 0.3s ease, filter 0.5s ease;
  filter: none;
}



/* 响应式调整 */
@media (max-width: 768px) {
  .logoContainer:hover .logoImage {
    transform: scale(1.05) !important;
  }
}
</style>
