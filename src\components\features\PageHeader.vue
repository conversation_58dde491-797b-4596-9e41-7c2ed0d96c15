<!-- /src/components/features/PageHeader.vue -->
<template>
  <div :class="$style.pageHeader">
    <h1 :class="$style.title">
      <slot name="title">黑猫船长 Z</slot>
    </h1>
    <p :class="$style.intro">
      <slot name="intro">青春是那种朦胧又执拗的、难以言说的渴望与微愁，<br>带着一丝遥远的甜，与一缕切近的酸。</slot>
    </p>
  </div>
</template>

<script setup>
// 组件逻辑
</script>

<style module>
.pageHeader {
  margin-bottom: var(--space-xl);
}

.title {
  font-family: var(--font-family-heading);
  font-weight: var(--font-weight-heading-h1);
  font-size: clamp(1.8rem, 4vw + 1rem, 2.5rem);
  letter-spacing: var(--letter-spacing-heading);
  color: var(--color-text-primary);
  margin-bottom: var(--space-m);
  position: relative;
  display: inline-block;
  padding-bottom: var(--space-s);
}

.title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 1px;
  background: linear-gradient(to right, var(--color-accent), rgba(var(--color-accent-rgb), 0.1));
}

.intro {
  font-family: var(--font-family-body);
  font-size: var(--font-base-size);
  line-height: var(--line-height-base);
  color: var(--color-text-secondary);
  max-width: 650px;
  font-style: italic;
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.05);
}
</style> 