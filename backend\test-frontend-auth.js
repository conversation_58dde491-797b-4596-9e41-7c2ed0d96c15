import fetch from 'node-fetch';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';

const BASE_URL = 'http://localhost:3001/api';

// 测试用的管理员凭据
const ADMIN_CREDENTIALS = {
  username: 'admin',
  password: 'admin123456'
};

// 测试1：登录并获取新Token
async function testLogin() {
  try {
    console.log('=== 测试1：用户登录 ===');
    
    const response = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'http://localhost:5175'
      },
      body: JSON.stringify(ADMIN_CREDENTIALS)
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('✓ 登录成功');
      console.log(`  Token: ${result.data.token.substring(0, 50)}...`);
      console.log(`  用户: ${result.data.user.username}`);
      console.log(`  角色: ${result.data.user.role}`);
      return result.data.token;
    } else {
      console.log('✗ 登录失败:', result.message);
      return null;
    }
  } catch (error) {
    console.log('✗ 登录请求失败:', error.message);
    return null;
  }
}

// 测试2：验证Token有效性
async function testTokenValidation(token) {
  try {
    console.log('\n=== 测试2：Token验证 ===');
    
    const response = await fetch(`${BASE_URL}/auth/me`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Origin': 'http://localhost:5175'
      }
    });

    const result = await response.json();
    
    if (response.ok && result.success) {
      console.log('✓ Token验证成功');
      console.log(`  用户ID: ${result.data.id}`);
      console.log(`  用户名: ${result.data.username}`);
      console.log(`  角色: ${result.data.role}`);
      return true;
    } else {
      console.log('✗ Token验证失败:', result.message);
      console.log(`  状态码: ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log('✗ Token验证请求失败:', error.message);
    return false;
  }
}

// 测试3：模拟前端图片上传请求
async function testImageUploadWithAuth(token) {
  try {
    console.log('\n=== 测试3：图片上传认证 ===');
    
    // 创建测试图片文件
    const testImagePath = path.join(process.cwd(), 'test-auth.jpg');
    const existingImagePath = path.join(process.cwd(), '..', 'public', 'articles', 'img', '1.jpg');
    
    if (fs.existsSync(existingImagePath)) {
      fs.copyFileSync(existingImagePath, testImagePath);
      console.log('✓ 测试图片文件准备完成');
    } else {
      console.log('✗ 没有找到测试图片文件');
      return false;
    }

    // 使用FormData模拟前端上传
    const formData = new FormData();
    formData.append('image', fs.createReadStream(testImagePath));

    console.log('发送上传请求...');
    console.log(`  URL: ${BASE_URL}/images/upload`);
    console.log(`  Token: Bearer ${token.substring(0, 20)}...`);
    console.log(`  Origin: http://localhost:5175`);

    const response = await fetch(`${BASE_URL}/images/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Origin': 'http://localhost:5175',
        ...formData.getHeaders()
      },
      body: formData
    });

    console.log(`响应状态码: ${response.status}`);
    
    // 清理测试文件
    if (fs.existsSync(testImagePath)) {
      fs.unlinkSync(testImagePath);
    }

    const result = await response.json();
    console.log('响应内容:', result);
    
    if (result.success) {
      console.log('✓ 图片上传成功');
      console.log(`  文件名: ${result.data.filename}`);
      console.log(`  URL: ${result.data.url}`);
      
      // 清理上传的测试图片
      try {
        await fetch(`${BASE_URL}/images/${result.data.id}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${token}` }
        });
        console.log('✓ 测试图片已清理');
      } catch (error) {
        console.log('⚠ 清理测试图片失败');
      }
      
      return true;
    } else {
      console.log('✗ 图片上传失败:', result.message);
      return false;
    }
  } catch (error) {
    console.log('✗ 图片上传异常:', error.message);
    console.log('错误详情:', error);
    return false;
  }
}

// 测试4：检查CORS头是否包含Authorization
async function testCORSHeaders() {
  try {
    console.log('\n=== 测试4：CORS头检查 ===');
    
    const response = await fetch(`${BASE_URL}/images/upload`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:5175',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'authorization,content-type'
      }
    });

    console.log(`OPTIONS请求状态码: ${response.status}`);
    
    const corsHeaders = {};
    response.headers.forEach((value, key) => {
      if (key.toLowerCase().includes('access-control')) {
        corsHeaders[key] = value;
      }
    });
    
    console.log('CORS响应头:', corsHeaders);
    
    const allowedHeaders = corsHeaders['access-control-allow-headers'];
    const hasAuthHeader = allowedHeaders && allowedHeaders.toLowerCase().includes('authorization');
    
    if (hasAuthHeader) {
      console.log('✓ CORS允许Authorization头');
      return true;
    } else {
      console.log('✗ CORS不允许Authorization头');
      return false;
    }
  } catch (error) {
    console.log('✗ CORS检查失败:', error.message);
    return false;
  }
}

// 主测试函数
async function runAuthTest() {
  console.log('🔐 开始前端认证问题诊断...\n');
  
  try {
    // 1. 测试登录
    const token = await testLogin();
    if (!token) {
      console.log('\n❌ 无法获取有效Token，测试终止');
      return;
    }
    
    // 2. 验证Token
    const tokenValid = await testTokenValidation(token);
    
    // 3. 检查CORS
    const corsOk = await testCORSHeaders();
    
    // 4. 测试图片上传
    const uploadOk = await testImageUploadWithAuth(token);
    
    // 输出诊断结果
    console.log('\n🎯 前端认证诊断结果:');
    console.log(`用户登录: ${token ? '✅ 成功' : '❌ 失败'}`);
    console.log(`Token验证: ${tokenValid ? '✅ 有效' : '❌ 无效'}`);
    console.log(`CORS配置: ${corsOk ? '✅ 正确' : '❌ 错误'}`);
    console.log(`图片上传: ${uploadOk ? '✅ 成功' : '❌ 失败'}`);
    
    const allOk = token && tokenValid && corsOk && uploadOk;
    console.log(`\n🏆 整体状态: ${allOk ? '✅ 认证系统正常' : '❌ 存在认证问题'}`);
    
    if (!allOk) {
      console.log('\n💡 解决建议:');
      if (!token) console.log('- 检查用户名密码是否正确');
      if (!tokenValid) console.log('- Token可能已过期，需要重新登录');
      if (!corsOk) console.log('- 检查CORS配置是否允许Authorization头');
      if (!uploadOk) console.log('- 检查前端是否正确发送Authorization头');
      
      console.log('\n🔧 前端修复步骤:');
      console.log('1. 在浏览器中访问 http://localhost:5175/login');
      console.log('2. 重新登录以获取新的Token');
      console.log('3. 检查浏览器控制台是否有错误信息');
      console.log('4. 验证localStorage中是否存储了有效的token');
    } else {
      console.log('\n🎉 认证系统工作正常！如果前端仍有问题，请检查:');
      console.log('1. 浏览器localStorage中的token是否存在');
      console.log('2. 前端是否正确读取和发送token');
      console.log('3. 是否有浏览器缓存问题');
    }
    
  } catch (error) {
    console.log('❌ 认证测试过程中发生错误:', error.message);
  }
}

runAuthTest().catch(console.error);
