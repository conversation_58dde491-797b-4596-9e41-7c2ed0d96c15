<template>
  <Teleport to="body">
    <Transition name="modal-fade">
      <div v-if="props.isOpen" :class="$style.modalOverlay" @click.self="closeModal">
        <div :class="$style.modalContainer">
          <button :class="$style.closeButton" @click="closeModal" aria-label="关闭搜索结果">×</button>
          <div v-if="loading" :class="$style.loading">
            <div :class="$style.loadingSpinner"></div>
          </div>
          <div v-else-if="error" :class="$style.error">{{ error }}</div>
          <div v-else :class="$style.searchResults">
            <h2 :class="$style.searchTitle">搜索结果: {{ props.query }}</h2>
            <div v-if="noResults" :class="$style.noResults">
              <p>未找到相关文章</p>
              <p :class="$style.searchTips">建议：</p>
              <ul>
                <li>检查输入是否正确</li>
                <li>尝试使用不同的关键词</li>
                <li>使用更简短的搜索词</li>
              </ul>
            </div>
            <div v-else :class="$style.resultsList">
              <div v-for="article in searchResults" :key="article.slug" :class="$style.resultItem" @click="openArticle(article.slug)">
                <h3 :class="$style.articleTitle" v-html="highlightText(article.title, props.query)"></h3>
                <p :class="$style.articleExcerpt" v-html="highlightText(article.excerpt, props.query)"></p>
                <div :class="$style.articleMeta">
                  <span v-if="article.readingTime">{{ article.readingTime }} 分钟阅读</span>
                  <div v-if="article.tags?.length" :class="$style.tags">
                    <span v-for="tag in article.tags" :key="tag" :class="$style.tag" v-html="highlightText('#' + tag, props.query)"></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import { useArticle } from '@/composables/useArticle';

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
  query: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['close', 'select-article']);

const loading = ref(false);
const error = ref(null);
const searchResults = ref([]);
const noResults = ref(false);

const closeModal = () => {
  emit('close');
};

const openArticle = (slug) => {
  emit('select-article', slug);
  closeModal();
};

// 处理ESC键关闭模态框
const handleKeydown = (event) => {
  if (event.key === 'Escape' && props.isOpen) {
    closeModal();
  }
};

// 高亮搜索结果中的匹配文本
const highlightText = (text, query) => {
  if (!query.trim()) return text;
  const regex = new RegExp(`(${query.trim()})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
};

// 防抖函数
const debounce = (fn, delay) => {
  let timer = null;
  return function (...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => fn.apply(this, args), delay);
  };
};

// 搜索文章
const searchArticles = async (query) => {
  try {
    console.log('开始搜索，关键词:', query);
    // 尝试从 /articles/index.json 获取数据
    let res;
    try {
      res = await fetch('/articles/index.json');
      if (!res.ok) {
        console.error('索引请求失败，状态码:', res.status);
        throw new Error('索引加载失败');
      }
    } catch (fetchError) {
      console.error('获取索引文件失败:', fetchError);
      // 如果获取失败，尝试从 src/data/articles.js 导入数据
      throw new Error('无法获取文章索引');
    }

    const data = await res.json();
    console.log('获取到的文章数据:', data.length, '条');

    const queryLower = query.trim().toLowerCase();

    if (!queryLower) {
      searchResults.value = [];
      noResults.value = false;
      return;
    }

    const results = data.filter(article => {
      // 确保所有属性存在再进行搜索
      const title = article.title || '';
      const excerpt = article.excerpt || '';
      const tags = article.tags || [];

      const titleMatch = title.toLowerCase().includes(queryLower);
      const excerptMatch = excerpt.toLowerCase().includes(queryLower);
      const tagsMatch = tags.some(tag =>
        tag.toLowerCase().includes(queryLower)
      );

      const isMatch = titleMatch || excerptMatch || tagsMatch;
      if (isMatch) {
        console.log('匹配到文章:', article.title);
      }
      return isMatch;
    });

    console.log('搜索结果数量:', results.length);

    // 根据匹配度排序结果
    results.sort((a, b) => {
      const aTitle = (a.title || '').toLowerCase();
      const bTitle = (b.title || '').toLowerCase();
      // 标题完全匹配的排在最前面
      if (aTitle === queryLower && bTitle !== queryLower) return -1;
      if (bTitle === queryLower && aTitle !== queryLower) return 1;
      // 标题包含搜索词的排在其次
      if (aTitle.includes(queryLower) && !bTitle.includes(queryLower)) return -1;
      if (bTitle.includes(queryLower) && !aTitle.includes(queryLower)) return 1;
      return 0;
    });

    searchResults.value = results;
    noResults.value = results.length === 0;
  } catch (err) {
    console.error('搜索错误:', err);
    error.value = '搜索失败，请稍后重试: ' + err.message;
  }
};

// 防抖处理的搜索函数
const debouncedSearch = debounce(searchArticles, 300);

// 当模态框打开时禁止背景滚动
watch(() => props.isOpen, (newValue) => {
  if (newValue) {
    document.body.style.overflow = 'hidden';
    loading.value = true;
    error.value = null;
    noResults.value = false;
    // 立即调用搜索
    searchArticles(props.query).finally(() => {
      loading.value = false;
    });
  } else {
    document.body.style.overflow = '';
  }
});

// 监听搜索词变化
watch(() => props.query, (newQuery) => {
  if (props.isOpen) {
    loading.value = true;
    debouncedSearch(newQuery).finally(() => {
      loading.value = false;
    });
  }
});

onMounted(() => {
  window.addEventListener('keydown', handleKeydown);
});

onBeforeUnmount(() => {
  window.removeEventListener('keydown', handleKeydown);
  document.body.style.overflow = '';
});
</script>

<style module>
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(30, 30, 30, 0.97);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(12px);
  padding: var(--space-m);
}

.modalContainer {
  position: relative;
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  background: rgba(25, 25, 25, 0.8);
  border-radius: var(--border-radius-soft);
  padding: var(--space-xl);
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
}

.modalContainer::-webkit-scrollbar {
  width: 6px;
}

.modalContainer::-webkit-scrollbar-track {
  background: transparent;
}

.modalContainer::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.closeButton {
  position: absolute;
  top: var(--space-m);
  right: var(--space-m);
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(255, 255, 255, 0.05);
  color: var(--color-text-secondary);
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border-radius: 50%;
  backdrop-filter: blur(4px);
  z-index: 10;
}

.closeButton:hover {
  color: var(--color-text-primary);
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.searchTitle {
  font-family: "Playfair Display", var(--font-family-heading);
  font-size: 2rem;
  color: #F5F5DC;
  margin-bottom: var(--space-xl);
  text-align: center;
}

.resultsList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-l);
}

.resultItem {
  padding: var(--space-l);
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--border-radius-soft);
  border: 1px solid rgba(255, 255, 255, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
}

.resultItem:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.articleTitle {
  font-family: "Playfair Display", var(--font-family-heading);
  font-size: 1.5rem;
  color: var(--color-text-primary);
  margin: 0 0 var(--space-s);
}

.articleExcerpt {
  color: var(--color-text-secondary);
  font-size: 0.95rem;
  line-height: 1.6;
  margin: var(--space-s) 0;
}

.articleMeta {
  display: flex;
  gap: var(--space-m);
  color: var(--color-text-secondary);
  font-size: 0.9rem;
  margin-top: var(--space-m);
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-s);
}

.tag {
  color: var(--color-text-secondary);
  font-size: 0.85rem;
  font-weight: var(--font-weight-light);
  letter-spacing: var(--letter-spacing-meta);
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-accent);
  animation: spin 1s linear infinite;
}

.error {
  color: var(--color-error, #ff6b6b);
  text-align: center;
  padding: var(--space-xl);
}

.noResults {
  text-align: center;
  padding: var(--space-xl);
  color: var(--color-text-secondary);
}

.searchTips {
  margin-top: var(--space-l);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.noResults ul {
  list-style: none;
  padding: 0;
  margin-top: var(--space-m);
}

.noResults li {
  margin: var(--space-s) 0;
  font-size: 0.9em;
}

mark {
  background-color: rgba(255, 255, 255, 0.15);
  color: var(--color-accent, #ffd700);
  padding: 0.1em 0.2em;
  border-radius: 2px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .modalContainer {
    padding: var(--space-l);
  }

  .searchTitle {
    font-size: 1.5rem;
  }

  .articleTitle {
    font-size: 1.25rem;
  }

  .resultItem {
    padding: var(--space-m);
  }
}
</style>