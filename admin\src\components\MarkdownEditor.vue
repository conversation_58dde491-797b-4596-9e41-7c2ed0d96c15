<template>
  <div class="markdown-editor">
    <div class="editor-toolbar">
      <el-button-group>
        <el-button size="small" @click="insertText('**', '**')" title="粗体">
          <strong>B</strong>
        </el-button>
        <el-button size="small" @click="insertText('*', '*')" title="斜体">
          <em>I</em>
        </el-button>
        <el-button size="small" @click="insertText('`', '`')" title="代码">
          <el-icon><Document /></el-icon>
        </el-button>
        <el-button size="small" @click="insertText('\n```\n', '\n```\n')" title="代码块">
          <el-icon><DocumentCopy /></el-icon>
        </el-button>
      </el-button-group>
      
      <el-button-group>
        <el-button size="small" @click="insertText('# ', '')" title="标题1">H1</el-button>
        <el-button size="small" @click="insertText('## ', '')" title="标题2">H2</el-button>
        <el-button size="small" @click="insertText('### ', '')" title="标题3">H3</el-button>
      </el-button-group>
      
      <el-button-group>
        <el-button size="small" @click="insertText('- ', '')" title="无序列表">
          <el-icon><List /></el-icon>
        </el-button>
        <el-button size="small" @click="insertText('1. ', '')" title="有序列表">
          <el-icon><Menu /></el-icon>
        </el-button>
        <el-button size="small" @click="insertText('> ', '')" title="引用">
          <el-icon><ChatDotRound /></el-icon>
        </el-button>
      </el-button-group>
      
      <el-button-group>
        <el-button size="small" @click="insertLink" title="链接">
          <el-icon><Link /></el-icon>
        </el-button>
        <el-button size="small" @click="showImageSelector" title="插入图片">
          <el-icon><Picture /></el-icon>
        </el-button>
      </el-button-group>
      
      <div class="toolbar-right">
        <el-button
          size="small"
          :type="showPreview ? 'primary' : 'default'"
          @click="togglePreview"
        >
          {{ showPreview ? '隐藏预览' : '显示预览' }}
        </el-button>
      </div>
    </div>
    
    <div class="editor-content" :class="{ 'split-view': showPreview }">
      <div class="editor-pane">
        <el-input
          ref="textareaRef"
          v-model="content"
          type="textarea"
          :rows="rows"
          placeholder="请输入Markdown内容..."
          @input="handleInput"
          @keydown="handleKeydown"
        />
      </div>
      
      <div v-if="showPreview" class="preview-pane">
        <div class="preview-content" v-html="renderedContent"></div>
      </div>
    </div>

    <!-- 图片选择器 -->
    <ImageSelector
      v-model="imageSelectorVisible"
      :multiple="true"
      @select="handleImageSelect"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import {
  Document,
  DocumentCopy,
  List,
  Menu,
  ChatDotRound,
  Link,
  Picture
} from '@element-plus/icons-vue'
import ImageSelector from './ImageSelector.vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  rows: {
    type: Number,
    default: 20
  }
})

const emit = defineEmits(['update:modelValue'])

const textareaRef = ref()
const showPreview = ref(false)
const content = ref(props.modelValue)
const imageSelectorVisible = ref(false)

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  content.value = newValue
})

// 监听内容变化并向外发送
watch(content, (newValue) => {
  emit('update:modelValue', newValue)
})

// 简单的Markdown渲染（实际项目中应该使用专业的Markdown解析器）
const renderedContent = computed(() => {
  const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001'

  let html = content.value
    // 标题
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    // 粗体和斜体
    .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
    .replace(/\*(.*)\*/gim, '<em>$1</em>')
    // 代码
    .replace(/`([^`]*)`/gim, '<code>$1</code>')
    // 链接
    .replace(/\[([^\]]*)\]\(([^)]*)\)/gim, '<a href="$2" target="_blank">$1</a>')
    // 图片 - 处理相对路径
    .replace(/!\[([^\]]*)\]\(([^)]*)\)/gim, (match, alt, src) => {
      // 如果是相对路径，添加完整的URL
      const fullSrc = src.startsWith('/') ? `${baseUrl}${src}` : src
      return `<img src="${fullSrc}" alt="${alt}" style="max-width: 100%; height: auto; margin: 8px 0; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" />`
    })
    // 引用
    .replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>')
    // 列表
    .replace(/^\* (.*$)/gim, '<li>$1</li>')
    .replace(/^- (.*$)/gim, '<li>$1</li>')
    .replace(/^\d+\. (.*$)/gim, '<li>$1</li>')
    // 换行
    .replace(/\n/gim, '<br>')

  return html
})

// 处理输入
const handleInput = () => {
  // 实时预览更新会自动触发
}

// 处理键盘快捷键
const handleKeydown = (event) => {
  // Tab键插入空格
  if (event.key === 'Tab') {
    event.preventDefault()
    insertText('  ', '')
  }
}

// 插入文本
const insertText = async (before, after) => {
  await nextTick()
  const textarea = textareaRef.value?.textarea || textareaRef.value
  if (!textarea) return
  
  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const selectedText = content.value.substring(start, end)
  
  const newText = before + selectedText + after
  content.value = content.value.substring(0, start) + newText + content.value.substring(end)
  
  // 设置光标位置
  await nextTick()
  const newCursorPos = start + before.length + selectedText.length
  textarea.setSelectionRange(newCursorPos, newCursorPos)
  textarea.focus()
}

// 插入链接
const insertLink = () => {
  const url = prompt('请输入链接地址:')
  if (url) {
    const text = prompt('请输入链接文本:', url)
    insertText(`[${text || url}](${url})`, '')
  }
}

// 显示图片选择器
const showImageSelector = () => {
  imageSelectorVisible.value = true
}

// 处理图片选择
const handleImageSelect = (selectedImages) => {
  if (Array.isArray(selectedImages)) {
    // 多选模式
    selectedImages.forEach(image => {
      const alt = image.alt_text || image.filename || '图片'
      insertText(`![${alt}](${image.url})\n`, '')
    })
  } else if (selectedImages) {
    // 单选模式
    const alt = selectedImages.alt_text || selectedImages.filename || '图片'
    insertText(`![${alt}](${selectedImages.url})`, '')
  }
}

// 插入图片（保留原有功能作为备用）
const insertImage = () => {
  const url = prompt('请输入图片地址:')
  if (url) {
    const alt = prompt('请输入图片描述:', '图片')
    insertText(`![${alt}](${url})`, '')
  }
}

// 切换预览
const togglePreview = () => {
  showPreview.value = !showPreview.value
}
</script>

<style scoped>
.markdown-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  gap: 8px;
}

.toolbar-right {
  margin-left: auto;
}

.editor-content {
  display: flex;
  height: 500px;
}

.editor-content.split-view .editor-pane {
  width: 50%;
  border-right: 1px solid #dcdfe6;
}

.editor-pane {
  flex: 1;
}

.editor-pane :deep(.el-textarea__inner) {
  border: none;
  border-radius: 0;
  resize: none;
  height: 100% !important;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
}

.preview-pane {
  width: 50%;
  overflow-y: auto;
  background: white;
}

.preview-content {
  padding: 16px;
  line-height: 1.6;
}

.preview-content :deep(h1),
.preview-content :deep(h2),
.preview-content :deep(h3) {
  margin: 16px 0 8px 0;
  color: #333;
}

.preview-content :deep(h1) {
  font-size: 24px;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.preview-content :deep(h2) {
  font-size: 20px;
}

.preview-content :deep(h3) {
  font-size: 16px;
}

.preview-content :deep(code) {
  background: #f1f1f1;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.preview-content :deep(blockquote) {
  border-left: 4px solid #ddd;
  margin: 16px 0;
  padding: 0 16px;
  color: #666;
}

.preview-content :deep(li) {
  margin: 4px 0;
}
</style>
