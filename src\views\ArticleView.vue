<!-- /src/views/ArticleView.vue -->
<template>
  <MainLayout>
    <div v-if="loading" :class="$style.loading">加载中...</div>
    <div v-else-if="error" :class="$style.error">{{ error }}</div>
    <div v-else-if="article" :class="$style.articleContent">
      <h1>{{ article.title }}</h1>
      <div class="poem-content" v-html="article.content" ref="articleContentRef"></div>
    </div>
    
    <!-- 添加图片查看器组件 -->
    <ImageViewer ref="imageViewerRef" />
  </MainLayout>
</template>

<script setup>
import { onMounted, ref, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import MainLayout from '@/components/layout/MainLayout.vue';
import ImageViewer from '@/components/common/ImageViewer.vue';
import { useArticle } from '@/composables/useArticle';

const articleContentRef = ref(null);
const imageViewerRef = ref(null);

const route = useRoute();
const { article, loading, error, loadArticle } = useArticle();

// 处理图片点击
const setupImageViewer = () => {
  if (articleContentRef.value) {
    const images = articleContentRef.value.querySelectorAll('img');
    
    // 收集所有图片URL
    const imageUrls = Array.from(images).map(img => img.src);
    
    images.forEach((img, index) => {
      // 为图片添加点击事件
      img.style.cursor = 'zoom-in';
      img.addEventListener('click', (event) => {
        event.preventDefault();
        
        // 打开图片查看器，传入当前图片索引和所有图片URL
        if (imageViewerRef.value) {
          // 设置images属性
          imageViewerRef.value.setImages(imageUrls, index);
          // 打开查看器
          imageViewerRef.value.open(index);
        }
      });
    });
  }
};

onMounted(async () => {
  await loadArticle(route.params.slug);
  
  // 在文章加载完成后，确保DOM已更新，为标题添加id
  await nextTick();
  if (articleContentRef.value) {
    const headings = articleContentRef.value.querySelectorAll('h2, h3, h4');
    headings.forEach((heading, index) => {
      if (!heading.id) {
        heading.id = `heading-${index}`;
      }
    });
    
    // 设置图片点击查看大图功能
    setupImageViewer();
  }
});
</script>
 
 <style module>
 /* 新增：文章一级标题样式 */
 .articleContent h1 {
         font-family: 'Playfair Display', serif; /* 普通内容二级标题 */
     font-size: 2.8rem; /* 根据设计稿调整 */
     font-weight: 400; /* 或根据设计稿调整 */
     color: #F5F5DC; /* 保持与其他标题一致或根据设计稿调整 */
     text-align: center;
     margin-top: var(--space-l);
     margin-bottom: var(--space-xl);
     line-height: 1.3;
 }

 /* 新增：诗歌引言样式 */
 .articleContent .poem-introduction {
     font-style: italic;
     color: var(--color-text-secondary);
     text-align: center;
     margin-bottom: var(--space-l);
     font-size: 1rem;
     font-family: var(--font-family-body);
 }

 /* 新增：诗歌内容容器样式 */
 .articleContent .poem-content {
         font-family: 'Playfair Display', serif; /* 普通内容二级标题 */ /* 假设诗歌也用此字体 */
     line-height: 1.9;
     letter-spacing: 0.02em;
     text-align: center;
     margin: var(--space-xl) auto;
     max-width: 80%; /* 调整诗歌部分宽度 */
 }

 /* 调整：诗歌内的二级标题 */
 .articleContent .poem-content h2 {
     font-size: 1.5rem; /* 调整诗歌内标题大小 */
     font-weight: 400;
     color: #E8E8E8; /* 调整颜色 */
     margin-top: var(--space-l);
     margin-bottom: var(--space-m);
     border-bottom: none; /* 移除诗歌内标题下划线 */
 }

 /* 新增：诗歌段落样式 */
 .articleContent .poem-content p {
     margin-bottom: var(--space-m);
     font-size: 1.1rem;
 }

 .articleContent {
     /* 文章内容区域的特定样式 */
     line-height: 1.8; /* 符合设计规范的行高 */
     letter-spacing: 0.01em;
     max-width: 70%;
     margin-right: auto;
     padding-right: var(--space-xl);
     color: #E0E0E0; /* 主文本色 */
     font-family: 'Inter', sans-serif;
 }

 .introduction {
     font-size: 1.1rem;
     color: var(--color-text-secondary);
     font-style: italic;
     margin-bottom: var(--space-xl);
     font-family: var(--font-family-body);
 }

 .poem {
     margin: var(--space-xl) 0;
     font-family: var(--font-family-heading);
     line-height: 1.9; /* 诗歌行高更大 */
     letter-spacing: 0.02em; /* 字间距稍大 */
     text-align: center;
 }

 .poem p {
     margin-bottom: var(--space-m); /* 诗行间距 */
     font-weight: var(--font-weight-regular);
     font-size: 1.1rem;
 }

 .tags {
     color: var(--color-accent);
     font-weight: var(--font-weight-regular);
 }

 .articleContent h2 { /* 文章内二级标题样式 */
     margin-top: var(--space-xl);
     margin-bottom: var(--space-m);
     border-bottom: 1px solid #3A3A3A;
     padding-bottom: var(--space-s);
         font-family: 'Playfair Display', serif; /* 普通内容二级标题 */
     font-weight: 400;
     letter-spacing: 0.02em;
     color: #F0F0F0;
 }

 .articleContent pre { /* 代码块容器样式 */
     background-color: var(--color-background-subtle);
     border-radius: var(--border-radius-soft);
     padding: var(--space-m);
     overflow-x: auto; /* 处理长代码行 */
     margin-bottom: var(--space-l);
 }

 .articleContent code { /* 代码块内代码样式 */
     background-color: transparent; /* 取消内联 code 的背景 */
     padding: 0;
     font-family: var(--font-family-mono);
     font-size: 0.9rem;
 }
 
 /* 标准 HTML 注释：元素越界样式 (与 MainLayout.vue 中的注释对应) */
 @media (min-width: 1024px) {
   .bleedElement {
      width: calc(100% + var(--space-l) * 2);
      margin-left: calc(-1 * var(--space-l));
      max-width: none;
      margin-top: var(--space-xl);
      margin-bottom: var(--space-xl);
   }
 }
 @media (max-width: 1023px) { /* 移动端不越界 */
     .bleedElement {
         width: 100%;
         margin-left: 0;
         margin-top: var(--space-l);
         margin-bottom: var(--space-l);
     }
 }

 /* 移除或注释掉不再直接使用的 .poem 样式，除非其他地方用到 */
 /*
 .poem {
     margin: var(--space-xl) 0;
     font-family: var(--font-family-heading);
     line-height: 1.9;
     letter-spacing: 0.02em;
     text-align: center;
 }

 .poem p {
     margin-bottom: var(--space-m);
     font-weight: var(--font-weight-regular);
     font-size: 1.
 }
 */
</style>