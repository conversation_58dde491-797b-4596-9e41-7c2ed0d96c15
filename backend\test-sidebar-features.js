import fetch from 'node-fetch';

async function testSidebarFeatures() {
  console.log('📋 测试侧边栏扩展功能...\n');

  try {
    // 1. 登录获取token
    console.log('1. 管理员登录');
    const loginResponse = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123456'
      })
    });

    const loginResult = await loginResponse.json();
    if (!loginResult.success) {
      throw new Error('登录失败: ' + loginResult.message);
    }

    const token = loginResult.data.token;
    console.log('✓ 登录成功');

    // 2. 获取一些图片用于测试
    console.log('\n2. 获取图片列表');
    const imagesResponse = await fetch('http://localhost:3001/api/images?limit=3', {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    const imagesResult = await imagesResponse.json();
    if (!imagesResult.success || imagesResult.data.images.length === 0) {
      throw new Error('没有可用的图片进行测试');
    }

    const testImages = imagesResult.data.images.slice(0, 2);
    console.log(`✓ 获取到 ${testImages.length} 张测试图片`);

    // 3. 创建包含扩展功能的测试文章
    console.log('\n3. 创建包含侧边栏扩展功能的测试文章');
    const testArticle = {
      title: '侧边栏功能测试文章',
      slug: 'sidebar-features-test-' + Date.now(),
      description: '这是一篇用于测试侧边栏扩展功能的文章',
      content: `# 侧边栏功能测试

这是一篇专门用于测试博客前端侧边栏扩展功能的文章。

## 测试内容

本文包含以下测试内容：

1. **文章统计信息** - 阅读时间和字数统计
2. **辅助介绍文字** - 显示在侧边栏的补充说明
3. **文章配图** - 多张配图的展示和管理

## 正文内容

这里是文章的正文内容，用于测试字数统计功能。文章内容应该足够长，以便能够准确计算阅读时间。

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

## 结论

通过这篇测试文章，我们可以验证侧边栏的各项扩展功能是否正常工作。`,
      auxiliary_content: `这是一篇测试文章的辅助介绍。

本文主要用于验证以下功能：
• 文章统计信息显示
• 辅助介绍文字展示
• 文章配图管理

请在前端查看侧边栏是否正确显示这些信息。`,
      status: 'published',
      featured: false,
      categories: [6], // 杂谈随笔
      tags: [18], // 随笔
      articleImages: testImages.map(img => img.id)
    };

    const articleResponse = await fetch('http://localhost:3001/api/articles', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(testArticle)
    });

    const articleResult = await articleResponse.json();
    if (articleResult.success) {
      console.log('✓ 测试文章创建成功');
      console.log(`✓ 文章ID: ${articleResult.data.id}`);
      console.log(`✓ 文章标题: ${articleResult.data.title}`);

      // 4. 验证文章详情API返回的数据
      console.log('\n4. 验证文章详情API');
      const detailResponse = await fetch(`http://localhost:3001/api/articles/${articleResult.data.id}`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      const detailResult = await detailResponse.json();
      if (detailResult.success) {
        const article = detailResult.data;
        console.log('✓ 文章详情获取成功');
        console.log(`✓ 辅助内容: ${article.auxiliary_content ? '已设置' : '未设置'}`);
        console.log(`✓ 阅读时间: ${article.reading_time || '未计算'} 分钟`);
        console.log(`✓ 字数统计: ${article.word_count || '未计算'} 字`);
        console.log(`✓ 配图数量: ${article.articleImages?.length || 0} 张`);
      } else {
        console.log('✗ 文章详情获取失败');
      }

      // 5. 测试数据同步
      console.log('\n5. 测试数据同步到前端');
      const syncResponse = await fetch('http://localhost:3001/api/articles/sync', {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` }
      });

      const syncResult = await syncResponse.json();
      if (syncResult.success) {
        console.log('✓ 数据同步成功');
      } else {
        console.log('⚠ 数据同步失败:', syncResult.message);
      }

      // 6. 提供访问链接
      console.log('\n6. 测试访问链接');
      console.log(`✓ 管理界面编辑: http://localhost:5174/articles/edit/${articleResult.data.id}`);
      console.log(`✓ 前端博客查看: http://localhost:5175`);

      // 等待用户确认后清理
      console.log('\n请在浏览器中验证侧边栏功能，然后按任意键继续清理测试数据...');
      
      // 等待5秒后自动清理
      setTimeout(async () => {
        console.log('\n7. 清理测试数据');
        const deleteResponse = await fetch(`http://localhost:3001/api/articles/${articleResult.data.id}`, {
          method: 'DELETE',
          headers: { 'Authorization': `Bearer ${token}` }
        });

        if (deleteResponse.ok) {
          console.log('✓ 测试文章已清理');
        } else {
          console.log('⚠ 清理测试文章失败');
        }
      }, 30000); // 30秒后自动清理

    } else {
      console.log('✗ 测试文章创建失败:', articleResult.message);
    }

    console.log('\n🎉 侧边栏扩展功能测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testSidebarFeatures();
